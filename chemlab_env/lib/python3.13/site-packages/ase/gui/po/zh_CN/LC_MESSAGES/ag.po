# Chinese translations for python-ase package.
# Copyright (C) 2017 CAMD and ASE Developers
# This file is distributed under the same license as the python-ase package.
#
# <PERSON> <<EMAIL>>, 2012.
# <PERSON> <<EMAIL>>, 2012.
# <PERSON><PERSON> <<EMAIL>>, 2012.
# <PERSON><PERSON> <<EMAIL>>, 2017.
#
msgid ""
msgstr ""
"Project-Id-Version: python-ase 3.6.0.2515\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2018-04-03 15:59+0200\n"
"PO-Revision-Date: 2017-09-28 19:14+0200\n"
"Last-Translator: Ke<PERSON> <<EMAIL>>\n"
"Language-Team: Chinese (simplified)\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: ../add.py:16
msgid "Add atoms"
msgstr "添加原子"

#: ../add.py:17
msgid "Specify chemical symbol, formula, or filename."
msgstr ""

#: ../add.py:35
msgid "Add:"
msgstr ""

#: ../add.py:36
#, fuzzy
#| msgid "Movie ..."
msgid "File ..."
msgstr "动画 ..."

#: ../add.py:46
#, fuzzy
#| msgid "_Load molecule"
msgid "Get molecule:"
msgstr "载入分子"

#: ../add.py:52
msgid "Coordinates:"
msgstr ""

#: ../add.py:54
msgid ""
"Coordinates are relative to the center of the selection, if any, else "
"absolute."
msgstr ""

#: ../add.py:56
#, fuzzy
#| msgid "Bad position"
msgid "Check positions"
msgstr "坏位置"

#: ../add.py:57 ../nanoparticle.py:264
msgid "Add"
msgstr "添加"

#. May show UI error
#: ../add.py:95
#, fuzzy
#| msgid "No valid atoms."
msgid "Cannot add atoms"
msgstr "原子不存在。"

#: ../add.py:96
msgid "{} is neither atom, molecule, nor file"
msgstr ""

#: ../add.py:135
#, fuzzy
#| msgid "Bad position"
msgid "Bad positions"
msgstr "坏位置"

#: ../add.py:136
msgid ""
"Atom would be less than 0.5 Å from an existing atom.  To override, uncheck "
"the check positions option."
msgstr ""

#. TRANSLATORS: This is a title of a window.
#: ../celleditor.py:48
msgid "Cell Editor"
msgstr ""

#: ../celleditor.py:52
msgid "A:"
msgstr ""

#: ../celleditor.py:52
msgid "||A||:"
msgstr ""

#: ../celleditor.py:53 ../celleditor.py:55 ../celleditor.py:57
msgid "periodic:"
msgstr ""

#: ../celleditor.py:54
msgid "B:"
msgstr ""

#: ../celleditor.py:54
msgid "||B||:"
msgstr ""

#: ../celleditor.py:56
msgid "C:"
msgstr ""

#: ../celleditor.py:56
msgid "||C||:"
msgstr ""

#: ../celleditor.py:58
msgid "∠BC:"
msgstr ""

#: ../celleditor.py:58
msgid "∠AC:"
msgstr ""

#: ../celleditor.py:59
msgid "∠AB:"
msgstr ""

#: ../celleditor.py:60
#, fuzzy
#| msgid "Scale atomic radii:"
msgid "Scale atoms with cell:"
msgstr "缩放原子半经"

#: ../celleditor.py:61
msgid "Apply Vectors"
msgstr ""

#: ../celleditor.py:62
msgid "Apply Magnitudes"
msgstr ""

#: ../celleditor.py:63
msgid "Apply Angles"
msgstr ""

#: ../celleditor.py:64
msgid ""
"Pressing 〈Enter〉 as you enter values will automatically apply correctly"
msgstr ""

#. TRANSLATORS: verb
#: ../celleditor.py:67
msgid "Center"
msgstr ""

#: ../celleditor.py:68
msgid "Wrap"
msgstr ""

#: ../celleditor.py:69
#, fuzzy
#| msgid "Vacuum: "
msgid "Vacuum:"
msgstr "真空："

#: ../celleditor.py:70
#, fuzzy
#| msgid "Vacuum: "
msgid "Apply Vacuum"
msgstr "真空："

#: ../colors.py:15
msgid "Colors"
msgstr "颜色"

#: ../colors.py:17
msgid "Choose how the atoms are colored:"
msgstr "选择原子颜色"

#: ../colors.py:20
msgid "By atomic number, default \"jmol\" colors"
msgstr "根据原子序数， 默认\"jmol\"颜色"

#: ../colors.py:21
msgid "By tag"
msgstr "根据标签"

#: ../colors.py:22
msgid "By force"
msgstr "根据受力"

#: ../colors.py:23
msgid "By velocity"
msgstr "根据速度"

#: ../colors.py:24
#, fuzzy
#| msgid "By charge"
msgid "By initial charge"
msgstr "   电荷"

#: ../colors.py:25
msgid "By magnetic moment"
msgstr "由磁矩"

#: ../colors.py:26
#, fuzzy
#| msgid "Number of layers:"
msgid "By number of neighbors"
msgstr "层数"

#: ../colors.py:71
msgid "Green"
msgstr "绿色"

#: ../colors.py:71
msgid "Yellow"
msgstr "黄色"

#: ../constraints.py:8
msgid "Constraints"
msgstr "限制"

#: ../constraints.py:9 ../constraints.py:11 ../settings.py:14
msgid "Constrain"
msgstr "限制"

#: ../constraints.py:10 ../constraints.py:14
msgid "selected atoms"
msgstr "已选择的原子"

#: ../constraints.py:12
msgid "immobile atoms"
msgstr "固定的原子："

#: ../constraints.py:13
msgid "Unconstrain"
msgstr "非限制"

#: ../constraints.py:15
msgid "Clear constraints"
msgstr "清除限制"

#: ../energyforces.py:15
msgid "Output:"
msgstr "输出："

#: ../energyforces.py:44
msgid "Save output"
msgstr "保存输出"

#: ../energyforces.py:61
msgid "Potential energy and forces"
msgstr "势能和受力"

#: ../energyforces.py:65
msgid "Calculate potential energy and the force on all atoms"
msgstr "计算所有原子的势能和受力"

#: ../energyforces.py:69
msgid "Write forces on the atoms"
msgstr "把受力标在原子上"

#: ../energyforces.py:86
msgid "Potential Energy:\n"
msgstr "势能:\n"

#: ../energyforces.py:87
#, python-format
msgid "  %8.2f eV\n"
msgstr "  %8.2f eV\n"

#: ../energyforces.py:88
#, python-format
msgid ""
"  %8.4f eV/atom\n"
"\n"
msgstr ""
"  %8.4f eV/原子\n"
"\n"

#: ../energyforces.py:90
msgid "Forces:\n"
msgstr "受力：\n"

#: ../graphene.py:17
msgid ""
"Set up a graphene sheet or a graphene nanoribbon.  A nanoribbon may\n"
"optionally be saturated with hydrogen (or another element)."
msgstr ""
"建立一个石墨烯单层或者石墨烯纳米带。一个纳米带可以\n"
"选择被氢（或其他元素）饱和。"

#: ../graphene.py:30
#, python-format
msgid " %(natoms)i atoms: %(symbols)s, Volume: %(volume).3f A<sup>3</sup>"
msgstr " %(natoms)i 原子: %(symbols)s, 体积: %(volume).3f A<sup>3</sup>"

#: ../graphene.py:38 ../gui.py:524
msgid "Graphene"
msgstr "石墨稀"

#. Choose structure
#: ../graphene.py:45
msgid "Structure: "
msgstr "结构： "

#: ../graphene.py:47
msgid "Infinite sheet"
msgstr "无限sheet"

#: ../graphene.py:47
msgid "Unsaturated ribbon"
msgstr "未饱和的ribbon"

#: ../graphene.py:48
msgid "Saturated ribbon"
msgstr "饱和的ribbon"

#. Orientation
#: ../graphene.py:55
msgid "Orientation: "
msgstr "取向： "

#: ../graphene.py:58
msgid "zigzag"
msgstr "zigzag"

#: ../graphene.py:58
msgid "armchair"
msgstr "armchair"

#: ../graphene.py:71 ../graphene.py:82
msgid "  Bond length: "
msgstr "  健长： "

#: ../graphene.py:72 ../graphene.py:83 ../graphene.py:107 ../nanotube.py:45
msgid "Å"
msgstr "Å"

#. Choose the saturation element and bond length
#: ../graphene.py:77
msgid "Saturation: "
msgstr "饱和："

#: ../graphene.py:80
msgid "H"
msgstr "H"

#. Size
#: ../graphene.py:96
msgid "Width: "
msgstr "宽度："

#: ../graphene.py:97
msgid "  Length: "
msgstr "长度："

#. Vacuum
#: ../graphene.py:105 ../surfaceslab.py:79
msgid "Vacuum: "
msgstr "真空："

#: ../graphene.py:153
msgid "  No element specified!"
msgstr "未指定元素！"

#: ../graphene.py:200
msgid "Please specify a consistent set of atoms. "
msgstr "请指定相应的原子序列。"

#: ../graphene.py:264 ../nanoparticle.py:531 ../nanotube.py:84
#: ../surfaceslab.py:223
msgid "No valid atoms."
msgstr "原子不存在。"

#: ../graphene.py:265 ../nanoparticle.py:532 ../nanotube.py:85
#: ../surfaceslab.py:224 ../widgets.py:108
msgid "You have not (yet) specified a consistent set of parameters."
msgstr "你没有指定合理的参数。"

#: ../graphs.py:11
msgid ""
"Symbols:\n"
"<c>e</c>: total energy\n"
"<c>epot</c>: potential energy\n"
"<c>ekin</c>: kinetic energy\n"
"<c>fmax</c>: maximum force\n"
"<c>fave</c>: average force\n"
"<c>R[n,0-2]</c>: position of atom number <c>n</c>\n"
"<c>d(n<sub>1</sub>,n<sub>2</sub>)</c>: distance between two atoms "
"<c>n<sub>1</sub></c> and <c>n<sub>2</sub></c>\n"
"<c>i</c>: current image number\n"
"<c>E[i]</c>: energy of image number <c>i</c>\n"
"<c>F[n,0-2]</c>: force on atom number <c>n</c>\n"
"<c>V[n,0-2]</c>: velocity of atom number <c>n</c>\n"
"<c>M[n]</c>: magnetic moment of atom number <c>n</c>\n"
"<c>A[0-2,0-2]</c>: unit-cell basis vectors\n"
"<c>s</c>: path length\n"
"<c>a(n1,n2,n3)</c>: angle between atoms <c>n<sub>1</sub></c>, <c>n<sub>2</"
"sub></c> and <c>n<sub>3</sub></c>, centered on <c>n<sub>2</sub></c>\n"
"<c>dih(n1,n2,n3,n4)</c>: dihedral angle between <c>n<sub>1</sub></c>, "
"<c>n<sub>2</sub></c>, <c>n<sub>3</sub></c> and <c>n<sub>4</sub></c>\n"
"<c>T</c>: temperature (K)"
msgstr ""
"符号:\n"
"<c>e</c>: 总能量\n"
"<c>epot</c>: 势能\n"
"<c>ekin</c>: 动能\n"
"<c>fmax</c>: 最大受力\n"
"<c>fave</c>: 均值受里\n"
"<c>R[n,0-2]</c>: <c>n</c>号原子的位置\n"
"<c>d(n<sub>1</sub>,n<sub>2</sub>)</c>: <c>n<sub>1</sub></c>号与<c>n<sub>2</"
"sub></c>号原子的距离\n"
"<c>i</c>: 这次画面序号\n"
"<c>E[i]</c>: <c>i</c>序号话面的能量\n"
"<c>F[n,0-2]</c>: 作用于<c>n</c>号原子的受力\n"
"<c>V[n,0-2]</c>: <c>n</c>号原子的速度\n"
"<c>M[n]</c>: <c>n</c>号原子的磁矩\n"
"<c>A[0-2,0-2]</c>: 单位晶格的基底向量\n"
"<c>s</c>: 路径长度\n"
"<c>a(n1,n2,n3)</c>: <c>n<sub>1</sub></c>号, <c>n<sub>2</sub></c>号与"
"<c>n<sub>3</sub></c>号原子之间的夹角, 集中在 <c>n<sub>2</sub></c>号原子\n"
"<c>dih(n1,n2,n3,n4)</c>: <c>n<sub>1</sub></c>号, <c>n<sub>2</sub></c>号, "
"<c>n<sub>3</sub></c>号与<c>n<sub>4</sub></c>号原子的二面角\n"
"<c>T</c>: 温度 (K)"

#: ../graphs.py:42 ../graphs.py:44
msgid "Plot"
msgstr "画图"

#: ../graphs.py:46
msgid "Save"
msgstr "保存"

#: ../graphs.py:47
msgid "Clear"
msgstr "清空"

#: ../graphs.py:72
msgid "Save data to file ... "
msgstr "保存数据到文件 ..."

#: ../gui.py:335
msgid "Quick Info"
msgstr "简单信息"

#: ../gui.py:427
msgid "_File"
msgstr "文件"

#: ../gui.py:428
msgid "_Open"
msgstr "打开"

#: ../gui.py:429
msgid "_New"
msgstr "新文件"

#: ../gui.py:430
msgid "_Save"
msgstr "保存"

#: ../gui.py:432
msgid "_Quit"
msgstr "退出"

#: ../gui.py:434
msgid "_Edit"
msgstr "编辑"

#: ../gui.py:435
msgid "Select _all"
msgstr "选择所有"

#: ../gui.py:436
msgid "_Invert selection"
msgstr "反选"

#: ../gui.py:437
msgid "Select _constrained atoms"
msgstr "选择被限制的原子"

#: ../gui.py:438
msgid "Select _immobile atoms"
msgstr "选择固定的原子"

#: ../gui.py:443
msgid "Hide selected atoms"
msgstr "把已选择的原子隐藏"

#: ../gui.py:444
msgid "Show selected atoms"
msgstr "显示已选择的原子"

#: ../gui.py:446
msgid "_Modify"
msgstr "修改"

#: ../gui.py:447
msgid "_Add atoms"
msgstr "添加原子"

#: ../gui.py:448
msgid "_Delete selected atoms"
msgstr "删除所选原子"

#: ../gui.py:450
#, fuzzy
#| msgid " unit cells"
msgid "Edit _cell"
msgstr " 晶胞"

#: ../gui.py:452
msgid "_First image"
msgstr "第一个图像"

#: ../gui.py:453
msgid "_Previous image"
msgstr "前一个图像"

#: ../gui.py:454
msgid "_Next image"
msgstr "下一个图像"

#: ../gui.py:455
msgid "_Last image"
msgstr "最后一个图像"

#: ../gui.py:457
msgid "_View"
msgstr "视图"

#: ../gui.py:458
msgid "Show _unit cell"
msgstr "显示原胞"

#: ../gui.py:460
msgid "Show _axes"
msgstr "显示坐标轴"

#: ../gui.py:461
msgid "Show _bonds"
msgstr "显示原子键"

#: ../gui.py:463
msgid "Show _velocities"
msgstr "显示速度"

#: ../gui.py:465
msgid "Show _forces"
msgstr "显示受力"

#: ../gui.py:467
msgid "Show _Labels"
msgstr "显示标签"

#: ../gui.py:468
msgid "_None"
msgstr "无"

#: ../gui.py:469
msgid "Atom _Index"
msgstr "原子指数"

#: ../gui.py:470
msgid "_Magnetic Moments"
msgstr "磁矩"

#. XXX check if exist
#: ../gui.py:471
msgid "_Element Symbol"
msgstr "化学元素符号"

#: ../gui.py:472
msgid "_Initial Charges"
msgstr ""

#: ../gui.py:475
msgid "Quick Info ..."
msgstr "简要信息 ..."

#: ../gui.py:476
msgid "Repeat ..."
msgstr "重复 ..."

#: ../gui.py:477
msgid "Rotate ..."
msgstr "旋转 ..."

#: ../gui.py:478
msgid "Colors ..."
msgstr "颜色 ..."

#. TRANSLATORS: verb
#: ../gui.py:480
msgid "Focus"
msgstr "聚焦"

#: ../gui.py:481
msgid "Zoom in"
msgstr "放大"

#: ../gui.py:482
msgid "Zoom out"
msgstr "缩小"

#: ../gui.py:483
msgid "Change View"
msgstr "改变视图"

#: ../gui.py:485
msgid "Reset View"
msgstr "复原"

#: ../gui.py:486
msgid "xy-plane"
msgstr "xy平面"

#: ../gui.py:487
msgid "yz-plane"
msgstr "yz平面"

#: ../gui.py:488
msgid "zx-plane"
msgstr "zx-平面"

#: ../gui.py:489
msgid "yx-plane"
msgstr "yx-平面"

#: ../gui.py:490
msgid "zy-plane"
msgstr "zy-平面"

#: ../gui.py:491
msgid "xz-plane"
msgstr "xz平面"

#: ../gui.py:492
msgid "a2,a3-plane"
msgstr "a2,a3-平面"

#: ../gui.py:493
msgid "a3,a1-plane"
msgstr "a3,a1-平面"

#: ../gui.py:494
msgid "a1,a2-plane"
msgstr "a1,a2-平面"

#: ../gui.py:495
msgid "a3,a2-plane"
msgstr "a3,a2-平面"

#: ../gui.py:496
msgid "a1,a3-plane"
msgstr "a1,a3-平面"

#: ../gui.py:497
msgid "a2,a1-plane"
msgstr "a2,a1-平面"

#: ../gui.py:498
msgid "Settings ..."
msgstr "设置 ..."

#: ../gui.py:500
msgid "VMD"
msgstr "VMD"

#: ../gui.py:501
msgid "RasMol"
msgstr "RasMol"

#: ../gui.py:502
msgid "xmakemol"
msgstr "xmakemol"

#: ../gui.py:503
msgid "avogadro"
msgstr "avogadro"

#: ../gui.py:505
msgid "_Tools"
msgstr "工具"

#: ../gui.py:506
msgid "Graphs ..."
msgstr "图片 ..."

#: ../gui.py:507
msgid "Movie ..."
msgstr "动画 ..."

#: ../gui.py:508
msgid "Expert mode ..."
msgstr "专家模式 ..."

#: ../gui.py:509
msgid "Constraints ..."
msgstr "限制 ..."

#: ../gui.py:510
msgid "Render scene ..."
msgstr "渲染场景 ..."

#: ../gui.py:511
msgid "_Move atoms"
msgstr "移动原子"

#: ../gui.py:512
msgid "_Rotate atoms"
msgstr "旋转原子"

#: ../gui.py:513
msgid "NE_B"
msgstr "NEB"

#: ../gui.py:514
msgid "B_ulk Modulus"
msgstr "块体的模块"

#: ../gui.py:515
#, fuzzy
#| msgid "Render scene ..."
msgid "Reciprocal space ..."
msgstr "渲染场景 ..."

#. TRANSLATORS: Set up (i.e. build) surfaces, nanoparticles, ...
#: ../gui.py:518
msgid "_Setup"
msgstr "设置"

#: ../gui.py:519
msgid "_Bulk Crystal"
msgstr "晶体"

#: ../gui.py:520
msgid "_Surface slab"
msgstr "表面"

#: ../gui.py:521
msgid "_Nanoparticle"
msgstr "纳米颗粒"

#: ../gui.py:523
msgid "Nano_tube"
msgstr "纳米管"

#: ../gui.py:526
msgid "_Calculate"
msgstr "计算"

#: ../gui.py:527
msgid "Set _Calculator"
msgstr "设置计算器"

#: ../gui.py:528
msgid "_Energy and Forces"
msgstr "能量和受力"

#: ../gui.py:529
msgid "Energy Minimization"
msgstr "能量最小化"

#: ../gui.py:532
msgid "_Help"
msgstr "帮助"

#: ../gui.py:533
msgid "_About"
msgstr "关于"

#: ../gui.py:537
msgid "Webpage ..."
msgstr "网页"

#. Host window will never be shown
#: ../images.py:300
#, fuzzy
#| msgid "Constraints"
msgid "Constraints discarded"
msgstr "限制"

#: ../images.py:301
msgid "Constraints other than FixAtoms have been discarded."
msgstr ""

#: ../modify.py:19
msgid "No atoms selected!"
msgstr "没有原子被选泽了"

#: ../modify.py:22
msgid "Modify"
msgstr "修改"

#: ../modify.py:25
msgid "Change element"
msgstr "改变元素"

#: ../modify.py:28
msgid "Tag"
msgstr "标签"

#: ../modify.py:30
msgid "Moment"
msgstr "磁矩"

#: ../movie.py:11
msgid "Movie"
msgstr "动画"

#: ../movie.py:12
msgid "Image number:"
msgstr "画面序号："

#: ../movie.py:18
msgid "First"
msgstr "最初"

#: ../movie.py:19
msgid "Back"
msgstr "返回"

#: ../movie.py:20
msgid "Forward"
msgstr "前进"

#: ../movie.py:21
msgid "Last"
msgstr "最后"

#: ../movie.py:23
msgid "Play"
msgstr "播放"

#: ../movie.py:24
msgid "Stop"
msgstr "停止"

#. TRANSLATORS: This function plays an animation forwards and backwards
#. alternatingly, e.g. for displaying vibrational movement
#: ../movie.py:28
msgid "Rock"
msgstr "Rock"

#: ../movie.py:41
msgid " Frame rate: "
msgstr "帧速率"

#: ../movie.py:41
msgid " Skip frames: "
msgstr "略过帧数"

#: ../nanoparticle.py:23
msgid ""
"Create a nanoparticle either by specifying the number of layers, or using "
"the\n"
"Wulff construction.  Please press the [Help] button for instructions on how "
"to\n"
"specify the directions.\n"
"WARNING: The Wulff construction currently only works with cubic crystals!\n"
msgstr ""
"通过指定层数或使用Wulff construction创建纳米颗粒。请按［帮助］已获取关于指定"
"方向的指导。\n"
"警告：目前Wulff construction仅适用于立方结构晶体\n"

#: ../nanoparticle.py:30
#, python-brace-format
msgid ""
"\n"
"The nanoparticle module sets up a nano-particle or a cluster with a given\n"
"crystal structure.\n"
"\n"
"1) Select the element, the crystal structure and the lattice constant(s).\n"
"   The [Get structure] button will find the data for a given element.\n"
"\n"
"2) Choose if you want to specify the number of layers in each direction, or "
"if\n"
"   you want to use the Wulff construction.  In the latter case, you must\n"
"   specify surface energies in each direction, and the size of the cluster.\n"
"\n"
"How to specify the directions:\n"
"------------------------------\n"
"\n"
"First time a direction appears, it is interpreted as the entire family of\n"
"directions, i.e. (0,0,1) also covers (1,0,0), (-1,0,0) etc.  If one of "
"these\n"
"directions is specified again, the second specification overrules that "
"specific\n"
"direction.  For this reason, the order matters and you can rearrange the\n"
"directions with the [Up] and [Down] keys.  You can also add a new "
"direction,\n"
"remember to press [Add] or it will not be included.\n"
"\n"
"Example: (1,0,0) (1,1,1), (0,0,1) would specify the {100} family of "
"directions,\n"
"the {111} family and then the (001) direction, overruling the value given "
"for\n"
"the whole family of directions.\n"
msgstr ""
"\n"
"纳米颗粒模块可以根据晶体结构创建纳米颗粒或团蔟\n"
"\n"
"1) 选择元素，晶体结构和晶格常数。\n"
"   ［读取结构］按钮包含指定元素的数据。\n"
"\n"
"2) 进行选择你想通过指定每个方向上的层数或 \n"
"    使用Wulff construction。如使用后者，你必须指定各个方向上的表面能和团蔟的"
"大小。\n"
"\n"
"怎样指定方向：\n"
"------------------------------\n"
"\n"
"方向第一次出现时，它代表整个晶向族，\n"
"就是说： (0,0,1) 包含 (1,0,0), (-1,0,0) 等.  如果被包含的任何一\n"
"个方向被重新指定，那么后者会覆盖该方向的原始定义。所以先后次序很重要。你可\n"
"以用［上］和［下］改变方向。你也可以添加新的方向，但是请记住\n"
"点击［添加］以确认添加。例如：(1,0,0) (1,1,1), (0,0,1) 先指\n"
"定了{100}晶向族，然后是{111}晶向族，最后(001)覆盖了{100}晶向族定义中改晶向\n"
"的定义。\n"

#. Structures:  Abbreviation, name,
#. 4-index (boolean), two lattice const (bool), factory
#: ../nanoparticle.py:90
msgid "Face centered cubic (fcc)"
msgstr "面心立方 (fcc)"

#: ../nanoparticle.py:92
msgid "Body centered cubic (bcc)"
msgstr "体心立方 (bcc)"

#: ../nanoparticle.py:94
msgid "Simple cubic (sc)"
msgstr "简单立方 (sc)"

#: ../nanoparticle.py:96
msgid "Hexagonal closed-packed (hcp)"
msgstr "六角密堆积 (hcp)"

#: ../nanoparticle.py:98
msgid "Graphite"
msgstr "石墨"

#: ../nanoparticle.py:130
msgid "Nanoparticle"
msgstr "纳米颗粒"

#: ../nanoparticle.py:134
msgid "Get structure"
msgstr "读取结构"

#: ../nanoparticle.py:154 ../surfaceslab.py:70
msgid "Structure:"
msgstr "结构:"

#: ../nanoparticle.py:159
msgid "Lattice constant:  a ="
msgstr "晶格常数:  a ="

#: ../nanoparticle.py:163
msgid "Layer specification"
msgstr "层指定"

#: ../nanoparticle.py:163
msgid "Wulff construction"
msgstr "Wulff构造"

#: ../nanoparticle.py:166
msgid "Method: "
msgstr "方法："

#: ../nanoparticle.py:174
msgid "Add new direction:"
msgstr "添加新的方向"

#. Information
#: ../nanoparticle.py:180
msgid "Information about the created cluster:"
msgstr "创建的团蔟信息："

#: ../nanoparticle.py:181
msgid "Number of atoms: "
msgstr "原子数目："

#: ../nanoparticle.py:183
msgid "   Approx. diameter: "
msgstr "  近似直径："

#: ../nanoparticle.py:192
msgid "Automatic Apply"
msgstr "自动运行"

#: ../nanoparticle.py:195 ../nanotube.py:51
msgid "Creating a nanoparticle."
msgstr "创建纳米颗粒中。"

#: ../nanoparticle.py:197 ../nanotube.py:52 ../surfaceslab.py:83
msgid "Apply"
msgstr "使用"

#: ../nanoparticle.py:198 ../nanotube.py:53 ../surfaceslab.py:84
msgid "OK"
msgstr "完成"

#: ../nanoparticle.py:227
msgid "Up"
msgstr "上"

#: ../nanoparticle.py:228
msgid "Down"
msgstr "下"

#: ../nanoparticle.py:229
msgid "Delete"
msgstr "删除"

#: ../nanoparticle.py:271
msgid "Number of atoms"
msgstr "原子数目："

#: ../nanoparticle.py:271
msgid "Diameter"
msgstr "直径"

#: ../nanoparticle.py:279
msgid "above  "
msgstr "上面"

#: ../nanoparticle.py:279
msgid "below  "
msgstr "下面"

#: ../nanoparticle.py:279
msgid "closest  "
msgstr "最近邻的"

#: ../nanoparticle.py:282
msgid "Smaller"
msgstr "更小"

#: ../nanoparticle.py:283
msgid "Larger"
msgstr "更大"

#: ../nanoparticle.py:284
msgid "Choose size using:"
msgstr "选泽大小用:"

#: ../nanoparticle.py:286
msgid "atoms"
msgstr "原子"

#: ../nanoparticle.py:287
msgid "Å³"
msgstr "Å³"

#: ../nanoparticle.py:289
msgid "Rounding: If exact size is not possible, choose the size:"
msgstr "取整：如果没有具体的尺寸，请选择相近尺寸"

#: ../nanoparticle.py:317
msgid "Surface energies (as energy/area, NOT per atom):"
msgstr "表面能（单位面积，不是每个原子）："

#: ../nanoparticle.py:319
msgid "Number of layers:"
msgstr "层数"

#: ../nanoparticle.py:347
msgid "At least one index must be non-zero"
msgstr "至少一个指数必须不为零"

#: ../nanoparticle.py:350
msgid "Invalid hexagonal indices"
msgstr "无效的六角指数"

#: ../nanoparticle.py:416
msgid "Unsupported or unknown structure"
msgstr "不支持或未知的结构"

#: ../nanoparticle.py:417
#, python-brace-format
msgid "Element = {0}, structure = {1}"
msgstr "化学元素 = {0}, 晶格结构 = {1}"

#: ../nanotube.py:13
msgid ""
"Set up a Carbon nanotube by specifying the (n,m) roll-up vector.\n"
"Please note that m <= n.\n"
"\n"
"Nanotubes of other elements can be made by specifying the element\n"
"and bond length."
msgstr ""
"通过指定(n,m) roll-up 矢量，来创建碳纳米管\n"
"请注意 m <= n.\n"
"\n"
"其他元素的纳米管可以通过指定元素和健长来创建。"

#: ../nanotube.py:26
#, python-brace-format
msgid ""
"{natoms} atoms, diameter: {diameter:.3f} Å, total length: {total_length:.3f} "
"Å"
msgstr "{natoms}原子, 直径: {diameter:.3f}埃, 总长度: {total_length:.3f}埃"

#: ../nanotube.py:40
msgid "Nanotube"
msgstr "纳米管"

#: ../nanotube.py:43
msgid "Bond length: "
msgstr "  健长："

#: ../nanotube.py:46
msgid "Select roll-up vector (n,m) and tube length:"
msgstr "选择roll-up 矢量(n,m)和管长度："

#: ../nanotube.py:49
msgid "Length:"
msgstr "长度："

#: ../quickinfo.py:28
msgid "This frame has no atoms."
msgstr "这个帧没有原子"

#: ../quickinfo.py:33
msgid "Single image loaded."
msgstr "加载了单个图像。"

#: ../quickinfo.py:35
#, fuzzy
#| msgid "Image %d loaded (0 - %d)."
msgid "Image {} loaded (0–{})."
msgstr "加载了图像 %d (0 - %d)."

#: ../quickinfo.py:37
#, fuzzy
#| msgid "Number of atoms: "
msgid "Number of atoms: {}"
msgstr "原子数目："

#: ../quickinfo.py:47
#, fuzzy
#| msgid "Unit cell varies."
msgid "Unit cell [Å]:"
msgstr "原胞大小改变了。"

#: ../quickinfo.py:49
msgid "no"
msgstr "不是"

#: ../quickinfo.py:49
msgid "yes"
msgstr "对"

#. TRANSLATORS: This has the form Periodic: no, no, yes
#: ../quickinfo.py:51
#, fuzzy
#| msgid "Periodic: %s, %s, %s"
msgid "Periodic: {}, {}, {}"
msgstr "周期性: %s, %s, %s"

#: ../quickinfo.py:55
msgid "Unit cell is fixed."
msgstr "原胞大小已固定。"

#: ../quickinfo.py:57
msgid "Unit cell varies."
msgstr "原胞大小改变了。"

#: ../quickinfo.py:60
msgid "Volume: {:.3f} Å³"
msgstr ""

#: ../quickinfo.py:88
#, fuzzy
#| msgid "Calculator:"
msgid "Calculator: {} (cached)"
msgstr "计算器"

#: ../quickinfo.py:90
#, fuzzy
#| msgid "Calculator:"
msgid "Calculator: {} (attached)"
msgstr "计算器"

#: ../quickinfo.py:97
#, fuzzy
#| msgid "Energy: "
msgid "Energy: {:.3f} eV"
msgstr "能量："

#: ../quickinfo.py:102
#, fuzzy
#| msgid "Max force: %.2f."
msgid "Max force: {:.3f} eV/Å"
msgstr "最大受力：%.2f."

#: ../quickinfo.py:106
msgid "Magmom: {:.3f} µ"
msgstr ""

#: ../render.py:20 ../render.py:190
msgid "Render current view in povray ... "
msgstr "在povray中渲染当前视图 ..."

#: ../render.py:21 ../render.py:194
#, python-format
msgid "Rendering %d atoms."
msgstr "渲染%d 原子中。"

#: ../render.py:26
msgid "Size"
msgstr "尺寸大小"

#: ../render.py:31 ../render.py:227
msgid "Line width"
msgstr "线宽"

#: ../render.py:32
msgid "Ångström"
msgstr "埃"

#: ../render.py:34 ../render.py:201
msgid "Render constraints"
msgstr "渲染限制"

#: ../render.py:35 ../render.py:215
msgid "Render unit cell"
msgstr "渲染原胞"

#: ../render.py:41 ../render.py:240
msgid "Output basename: "
msgstr "输出基本名"

#: ../render.py:43
msgid "Output filename: "
msgstr "输出文件名"

#: ../render.py:48
msgid "Atomic texture set:"
msgstr "原子材质集"

#: ../render.py:55 ../render.py:283
msgid "Camera type: "
msgstr "相机类型："

#: ../render.py:56
msgid "Camera distance"
msgstr "相机距离"

#. render current frame/all frames
#: ../render.py:59 ../render.py:286
msgid "Render current frame"
msgstr "渲染当前帧"

#: ../render.py:60
msgid "Render all frames"
msgstr "渲染所有 %d 帧"

#: ../render.py:65
msgid "Run povray"
msgstr "运行povray"

#: ../render.py:66
msgid "Keep povray files"
msgstr "保持vray文件"

#: ../render.py:67 ../render.py:304
msgid "Show output window"
msgstr "显示输出窗口"

#: ../render.py:68 ../render.py:295
msgid "Transparent background"
msgstr "透明的背景"

#: ../render.py:72
msgid "Render"
msgstr "渲染"

#: ../render.py:171
msgid ""
"    Textures can be used to highlight different parts of\n"
"    an atomic structure. This window applies the default\n"
"    texture to the entire structure and optionally\n"
"    applies a different texture to subsets of atoms that\n"
"    can be selected using the mouse.\n"
"    An alternative selection method is based on a boolean\n"
"    expression in the entry box provided, using the\n"
"    variables x, y, z, or Z. For example, the expression\n"
"    Z == 11 and x > 10 and y > 10\n"
"    will mark all sodium atoms with x or coordinates\n"
"    larger than 10. In either case, the button labeled\n"
"    `Create new texture from selection` will enable\n"
"    to change the attributes of the current selection.\n"
"    "
msgstr ""
"    材质可以用来标识原子结构的不同部分。这个窗口对所有\n"
"    的结构使用默认的材质。你也可以用鼠标选择对指定的原子集合\n"
"    使用不同的材质。也可以通过提供的boolean表达式来\n"
"    进行原子选择。例如：表达式Z == 11 与 x > 10 与 y > 10，\n"
"    这将选择x和y坐标大于10的钠原子。然后通过‘给指定\n"
"    的原子集合创建新的材质’来更改材质的属性。\n"
"    "

#: ../render.py:206
msgid "Width"
msgstr "宽度"

#: ../render.py:206
msgid "     Height"
msgstr "     高度"

#: ../render.py:228
msgid "Angstrom           "
msgstr "Angstrom           "

#: ../render.py:238
msgid "Set"
msgstr "设置"

#: ../render.py:242
msgid "               Filename: "
msgstr "               文件名："

#: ../render.py:254
msgid " Default texture for atoms: "
msgstr "默认的原子材质："

#: ../render.py:255
msgid "    transparency: "
msgstr "    透明度："

#: ../render.py:258
msgid "Define atom selection for new texture:"
msgstr "选择原子以进行材质定义"

#: ../render.py:260
msgid "Select"
msgstr "选择"

#: ../render.py:264
msgid "Create new texture from selection"
msgstr "选择新的材质"

#: ../render.py:267
msgid "Help on textures"
msgstr "关于材质的帮助"

#: ../render.py:284
msgid "     Camera distance"
msgstr "      相机距离"

#: ../render.py:290
#, python-format
msgid "Render all %d frames"
msgstr "渲染所有 %d 帧"

#: ../render.py:298
msgid "Run povray       "
msgstr "运行povray"

#: ../render.py:301
msgid "Keep povray files       "
msgstr "保持vray文件"

#: ../render.py:389
msgid "  transparency: "
msgstr "   透明度："

#: ../render.py:399
msgid ""
"Can not create new texture! Must have some atoms selected to create a new "
"material!"
msgstr "不能创建新的材质！必须先选择原子，然后创建材料！"

#: ../repeat.py:10
msgid "Repeat"
msgstr "重复"

#: ../repeat.py:11
msgid "Repeat atoms:"
msgstr "重复原子："

#: ../repeat.py:15
msgid "Set unit cell"
msgstr "设置原胞"

#: ../rotate.py:13
msgid "Rotate"
msgstr "旋转"

#: ../rotate.py:14
msgid "Rotation angles:"
msgstr "旋转角度："

#: ../rotate.py:18
msgid "Update"
msgstr "更新"

#: ../rotate.py:19
msgid ""
"Note:\n"
"You can rotate freely\n"
"with the mouse, by holding\n"
"down mouse button 2."
msgstr ""
"注意:\n"
"按住按钮2(通常为右键)，你用鼠标\n"
"进行可以自由的旋转。"

#: ../save.py:14
msgid ""
"Append name with \"@n\" in order to write image\n"
"number \"n\" instead of the current image. Append\n"
"\"@start:stop\" or \"@start:stop:step\" if you want\n"
"to write a range of images. You can leave out\n"
"\"start\" and \"stop\" so that \"name@:\" will give\n"
"you all images. Negative numbers count from the\n"
"last image. Examples: \"name@-1\": last image,\n"
"\"name@-2:\": last two."
msgstr ""
"把原文件的名子(比如: name)加\"@n\"为了写入\"n\"号画面,\n"
"不是现用的画面. 把名子加\"@start:stop\"还是\"@start:stop:step\"\n"
"为了写入一连串画面. 你可以省\"start\"还是\"stop\"的选项,\n"
"以便\"name@:\"会写入所有的画面. 用负数来从最后的画面倒数,\n"
"比如说: \"name@-1\": 最后的画面,\"name@-2:\": 最后两个画面."

#: ../save.py:26
msgid "Save ..."
msgstr "保存 ..."

#: ../settings.py:10
msgid "Settings"
msgstr "设置"

#. Constraints
#: ../settings.py:13
msgid "Constraints:"
msgstr "限制："

#: ../settings.py:16
msgid "release"
msgstr "释放"

#: ../settings.py:17 ../settings.py:26
msgid " selected atoms"
msgstr "已选择的原子"

#: ../settings.py:18
msgid "Constrain immobile atoms"
msgstr "限制固定的原子"

#: ../settings.py:19
msgid "Clear all constraints"
msgstr "清除所有的限制"

#. Visibility
#: ../settings.py:22
msgid "Visibility:"
msgstr "可视化"

#: ../settings.py:23
msgid "Hide"
msgstr "隐藏"

#: ../settings.py:25
msgid "show"
msgstr "显示"

#: ../settings.py:27
msgid "View all atoms"
msgstr "显示所有原子"

#. Miscellaneous
#: ../settings.py:30
msgid "Miscellaneous:"
msgstr "其他："

#: ../settings.py:33
msgid "Scale atomic radii:"
msgstr "缩放原子半经"

#: ../simulation.py:30
msgid " (rerun simulation)"
msgstr "  （重新运行模拟）"

#: ../simulation.py:31
msgid " (continue simulation)"
msgstr " （继续模拟）"

#: ../simulation.py:33
msgid "Select starting configuration:"
msgstr "选择开始构型"

#: ../simulation.py:38
#, python-format
msgid "There are currently %i configurations loaded."
msgstr "目前加载了%i个构型。"

#: ../simulation.py:43
msgid "Choose which one to use as the initial configuration"
msgstr "选择初始化构型"

#: ../simulation.py:47
#, python-format
msgid "The first configuration %s."
msgstr "第一个构型 %s."

#: ../simulation.py:50
msgid "Configuration number "
msgstr "构型数目"

#: ../simulation.py:56
#, python-format
msgid "The last configuration %s."
msgstr "最后一个构型 %s."

#: ../simulation.py:92
msgid "Run"
msgstr "运行"

#: ../simulation.py:112
msgid "No calculator: Use Calculate/Set Calculator on the menu."
msgstr "未找到计算器: 请使用菜单上的Calculate/设置计算器。"

#: ../simulation.py:123
msgid "No atoms present"
msgstr "没有发现原子"

#: ../status.py:58
#, python-format
msgid " tag=%(tag)s"
msgstr "标签=%(tag)s"

#. TRANSLATORS: mom refers to magnetic moment
#: ../status.py:62
#, python-brace-format
msgid " mom={0:1.2f}"
msgstr "磁矩={0:1.2f}"

#: ../status.py:66
#, python-brace-format
msgid " q={0:1.2f}"
msgstr "q={0:1.2f}"

#: ../status.py:111
msgid "dihedral"
msgstr "二面的"

#: ../surfaceslab.py:12
msgid ""
"  Use this dialog to create surface slabs.  Select the element by\n"
"writing the chemical symbol or the atomic number in the box.  Then\n"
"select the desired surface structure.  Note that some structures can\n"
"be created with an othogonal or a non-orthogonal unit cell, in these\n"
"cases the non-orthogonal unit cell will contain fewer atoms.\n"
"\n"
"  If the structure matches the experimental crystal structure, you can\n"
"look up the lattice constant, otherwise you have to specify it\n"
"yourself."
msgstr ""
"  使用这个窗口来创建表面。通过输入化学元素或原子序号来选择\n"
"元素。然后选择需要的表面结构。注意，有些结构的原胞可以是正交\n"
"的或非正交的。这种情况下，非正交的原胞包含更少的原子。\n"
"\n"
"如果符合实验的晶体结构，你可以查找晶格常数，否则你需要自己指定。"

#. Name, structure, orthogonal, function
#: ../surfaceslab.py:24
msgid "FCC(100)"
msgstr "FCC(100)"

#: ../surfaceslab.py:24 ../surfaceslab.py:25 ../surfaceslab.py:26
#: ../surfaceslab.py:27
msgid "fcc"
msgstr "fcc"

#: ../surfaceslab.py:25
msgid "FCC(110)"
msgstr "FCC(110)"

#: ../surfaceslab.py:26 ../surfaceslab.py:173
msgid "FCC(111)"
msgstr "FCC(111)"

#: ../surfaceslab.py:27 ../surfaceslab.py:176
msgid "FCC(211)"
msgstr "FCC(211)"

#: ../surfaceslab.py:28
msgid "BCC(100)"
msgstr "BCC(100)"

#: ../surfaceslab.py:28 ../surfaceslab.py:29 ../surfaceslab.py:30
msgid "bcc"
msgstr "bcc"

#: ../surfaceslab.py:29 ../surfaceslab.py:170
msgid "BCC(110)"
msgstr "BCC(111)"

#: ../surfaceslab.py:30 ../surfaceslab.py:167
msgid "BCC(111)"
msgstr "BCC(111)"

#: ../surfaceslab.py:31 ../surfaceslab.py:180
msgid "HCP(0001)"
msgstr "HCP(0001)"

#: ../surfaceslab.py:31 ../surfaceslab.py:32 ../surfaceslab.py:134
#: ../surfaceslab.py:190
msgid "hcp"
msgstr "hcp"

#: ../surfaceslab.py:32 ../surfaceslab.py:183
msgid "HCP(10-10)"
msgstr "HCP(10-10)"

#: ../surfaceslab.py:33
msgid "DIAMOND(100)"
msgstr "金刚石结构(100)"

#: ../surfaceslab.py:33 ../surfaceslab.py:34
msgid "diamond"
msgstr "金刚石"

#: ../surfaceslab.py:34
msgid "DIAMOND(111)"
msgstr "金刚石结构(111)"

#: ../surfaceslab.py:55
msgid "Get from database"
msgstr "从数据库读取"

#: ../surfaceslab.py:67
msgid "Surface"
msgstr "表面"

#: ../surfaceslab.py:71
msgid "Orthogonal cell:"
msgstr "正交原胞:"

#: ../surfaceslab.py:72
#, fuzzy
#| msgid "Lattice constant:\ta"
msgid "Lattice constant:"
msgstr "晶格常数：\ta"

#: ../surfaceslab.py:73
msgid "\ta"
msgstr ""

#: ../surfaceslab.py:74
#, fuzzy
#| msgid "\t\tc"
msgid "\tc"
msgstr "\t\tc"

#: ../surfaceslab.py:75
#, fuzzy
#| msgid "Size"
msgid "Size:"
msgstr "尺寸大小"

#: ../surfaceslab.py:76
msgid "\tx: "
msgstr "\tx: "

#: ../surfaceslab.py:76 ../surfaceslab.py:77 ../surfaceslab.py:78
msgid " unit cells"
msgstr " 晶胞"

#: ../surfaceslab.py:77
msgid "\ty: "
msgstr "\ty: "

#: ../surfaceslab.py:78
msgid "\tz: "
msgstr "\tz: "

#. TRANSLATORS: This is a title of a window.
#: ../surfaceslab.py:82
msgid "Creating a surface."
msgstr "创建表面。"

#. TRANSLATORS: E.g. "... assume fcc crystal structure for Au"
#: ../surfaceslab.py:110
msgid "Error: Reference values assume {} crystal structure for {}!"
msgstr ""

#: ../surfaceslab.py:164
msgid "Please enter an even value for orthogonal cell"
msgstr "请为正交的晶格输入一个双数"

#: ../surfaceslab.py:177
msgid "Please enter a value divisible by 3 for orthogonal cell"
msgstr "请为正交的晶格输入一个可被3除尽的数"

#: ../surfaceslab.py:197
msgid " Vacuum: {} Å."
msgstr "真空： {}埃"

#. TRANSLATORS: e.g. "Au fcc100 surface with 2 atoms."
#. or "Au fcc100 surface with 2 atoms. Vacuum: 5 Å."
#: ../surfaceslab.py:205
#, python-brace-format
msgid "{symbol} {surf} surface with one atom.{vacuum}"
msgid_plural "{symbol} {surf} surface with {natoms} atoms.{vacuum}"
msgstr[0] "{symbol} {surf} 表面{natoms}号的原子.{vacuum}"

#: ../ui.py:46
msgid "Error"
msgstr "错误"

#: ../ui.py:53
msgid "Version"
msgstr "版本"

#: ../ui.py:54
msgid "Web-page"
msgstr "网页"

#: ../ui.py:55
msgid "About"
msgstr "关于"

#: ../ui.py:60 ../ui.py:64 ../widgets.py:17
msgid "Help"
msgstr "帮助"

#: ../ui.py:552
msgid "Open ..."
msgstr "打开 ..."

#: ../ui.py:553
msgid "Automatic"
msgstr "自动"

#: ../ui.py:571
msgid "Choose parser:"
msgstr "选泽一个分析器:"

#: ../ui.py:577
msgid "Read error"
msgstr ""

#: ../ui.py:578
msgid "Could not read {}: {}"
msgstr ""

#: ../widgets.py:14
msgid "Element:"
msgstr "元素:"

#. This infobox is indescribably ugly because of the
#. ridiculously large font size used by Tkinter.  Ouch!
#: ../widgets.py:34
msgid ""
"Enter a chemical symbol or the name of a molecule from the G2 testset:\n"
"{}"
msgstr ""

#: ../widgets.py:68
msgid "No element specified!"
msgstr "未指定元素！"

#: ../widgets.py:90
msgid "ERROR: Invalid element!"
msgstr "错误：无效元素！"

#: ../widgets.py:107
msgid "No Python code"
msgstr "没有Python代码"

#~ msgid ""
#~ "  Use this dialog to create crystal lattices. First select the "
#~ "structure,\n"
#~ "  either from a set of common crystal structures, or by space group "
#~ "description.\n"
#~ "  Then add all other lattice parameters.\n"
#~ "\n"
#~ "  If an experimental crystal structure is available for an atom, you can\n"
#~ "  look up the crystal type and lattice constant, otherwise you have to "
#~ "specify it\n"
#~ "  yourself.  "
#~ msgstr ""
#~ "  使用这个对话窗口来创建晶格。首先，从常见晶体结构或者根据空间群中选择结"
#~ "构。\n"
#~ "  然后，添加其他晶格参数。\n"
#~ "\n"
#~ "  你可以查找实验晶体结构参数，并依次设置晶格类型和晶格常数。\n"
#~ "否则你需要自己对其进行指定。"

#~ msgid "Create Bulk Crystal by Spacegroup"
#~ msgstr "根据空间群创建晶体"

#~ msgid "Number: 1"
#~ msgstr "标号： 1"

#~ msgid "Lattice: "
#~ msgstr "晶格："

#~ msgid "\tSpace group: "
#~ msgstr "\t空间群： "

#~ msgid "Size: x: "
#~ msgstr "尺寸大小：x: "

#~ msgid "  y: "
#~ msgstr "  y: "

#~ msgid "  z: "
#~ msgstr "  z: "

#~ msgid "free"
#~ msgstr "自由"

#~ msgid "equals b"
#~ msgstr "等于 b"

#~ msgid "equals c"
#~ msgstr "等于 c"

#~ msgid "fixed"
#~ msgstr "固定的"

#~ msgid "equals a"
#~ msgstr "等于 a"

#~ msgid "equals beta"
#~ msgstr "等于 beta"

#~ msgid "equals gamma"
#~ msgstr "等于 gamma"

#~ msgid "equals alpha"
#~ msgstr "等于 alpha"

#~ msgid "Lattice parameters"
#~ msgstr "晶格参数"

#~ msgid "\t\ta:\t"
#~ msgstr "\t\ta:\t"

#~ msgid "\talpha:\t"
#~ msgstr "\talpha:\t"

#~ msgid "\t\tb:\t"
#~ msgstr "\t\tb:\t"

#~ msgid "\tbeta:\t"
#~ msgstr "\tbeta:\t"

#~ msgid "\t\tc:\t"
#~ msgstr "\t\tc:\t"

#~ msgid "\tgamma:\t"
#~ msgstr "\tgamma:\t"

#~ msgid "Basis: "
#~ msgstr "基组： "

#~ msgid "  Element:\t"
#~ msgstr "   元素：\t"

#~ msgid "Creating a crystal."
#~ msgstr "创建一个晶体。"

#~ msgid "Symbol: %s"
#~ msgstr "化学符号: %s"

#~ msgid "Number: %s"
#~ msgstr "序号: %s"

#~ msgid "Invalid Spacegroup!"
#~ msgstr "此空间群不存在！"

#~ msgid "Please specify a consistent set of atoms."
#~ msgstr "请指定相应的原子序列。"

#~ msgid "Can't find lattice definition!"
#~ msgstr "没有发现已定义的晶格！"

#~ msgid "Absolute position:"
#~ msgstr "绝对位置"

#~ msgid "Relative to average position (of selection):"
#~ msgstr "随均值的位置"

#~ msgid ""
#~ "%s\n"
#~ "\n"
#~ "Number of atoms: %d.\n"
#~ "\n"
#~ "Unit cell:\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "\n"
#~ "%s\n"
#~ "%s\n"
#~ msgstr ""
#~ "%s\n"
#~ "\n"
#~ "原子数目: %d.\n"
#~ "\n"
#~ "原胞:\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "%s\n"
#~ "%s\n"

#~ msgid "Volume: "
#~ msgstr "体积："

#~ msgid "Size: \tx: "
#~ msgstr "大小: \tx: "

#~ msgid ""
#~ "To make most calculations on the atoms, a Calculator object must first\n"
#~ "be associated with it.  ASE supports a number of calculators, supporting\n"
#~ "different elements, and implementing different physical models for the\n"
#~ "interatomic interactions."
#~ msgstr ""
#~ "如果要进行计算，请先指定一个计算器。ASE支持多个计算器，\n"
#~ "这些计算起支持多种元素，并且有多种物理模型处理原子间的作用。"

# XXX 计算器
#, fuzzy
#~ msgid ""
#~ "The Lennard-Jones pair potential is one of the simplest\n"
#~ "possible models for interatomic interactions, mostly\n"
#~ "suitable for noble gasses and model systems.\n"
#~ "\n"
#~ "Interactions are described by an interaction length and an\n"
#~ "interaction strength."
#~ msgstr ""
#~ "Lennard-Jones势是描述原子间相互作用的最简单的模型之一。它适合于惰性气\n"
#~ "体和模型体系. 该势使用原子间距和相互作用强度参数来描述原子间的相互作用"

#~ msgid ""
#~ "The EMT potential is a many-body potential, giving a\n"
#~ "good description of the late transition metals crystalling\n"
#~ "in the FCC crystal structure.  The elements described by the\n"
#~ "main set of EMT parameters are Al, Ni, Cu, Pd, Ag, Pt, and\n"
#~ "Au, the Al potential is however not suitable for materials\n"
#~ "science application, as the stacking fault energy is wrong.\n"
#~ "\n"
#~ "A number of parameter sets are provided.\n"
#~ "\n"
#~ "<b>Default parameters:</b>\n"
#~ "\n"
#~ "The default EMT parameters, as published in K. W. Jacobsen,\n"
#~ "P. Stoltze and J. K. Nørskov, <i>Surf. Sci.</i> <b>366</b>, 394 (1996).\n"
#~ "\n"
#~ "<b>Alternative Cu, Ag and Au:</b>\n"
#~ "\n"
#~ "An alternative set of parameters for Cu, Ag and Au,\n"
#~ "reoptimized to experimental data including the stacking\n"
#~ "fault energies by Torben Rasmussen (partly unpublished).\n"
#~ "\n"
#~ "<b>Ruthenium:</b>\n"
#~ "\n"
#~ "Parameters for Ruthenium, as published in J. Gavnholt and\n"
#~ "J. Schiøtz, <i>Phys. Rev. B</i> <b>77</b>, 035404 (2008).\n"
#~ "\n"
#~ "<b>Metallic glasses:</b>\n"
#~ "\n"
#~ "Parameters for MgCu and CuZr metallic glasses. MgCu\n"
#~ "parameters are in N. P. Bailey, J. Schiøtz and\n"
#~ "K. W. Jacobsen, <i>Phys. Rev. B</i> <b>69</b>, 144205 (2004).\n"
#~ "CuZr in A. Paduraru, A. Kenoufi, N. P. Bailey and\n"
#~ "J. Schiøtz, <i>Adv. Eng. Mater.</i> <b>9</b>, 505 (2007).\n"
#~ msgstr ""
#~ "有效介质理论(EMT)势是一种多体势。它能很好的描述FCC结构的后过渡金属元素。\n"
#~ "EMT所包含的元素有 （铝）Al, （镍）Ni, （铜）Cu, （钯）Pd, \n"
#~ "（银）Ag, （铂）Pt, (金)Au。但由于（铝）Al势给出错误的堆积能，\n"
#~ "其不适合材料科学计算。\n"
#~ "\n"
#~ "本软件包所提供的EMT势提供了很多参数的默认值。\n"
#~ "\n"
#~ "<b>默认值：</b>\n"
#~ "\n"
#~ "EMT参数的默认值来自于K. W. Jacobsen，\n"
#~ "P. Stoltze and J. K. Nørskov, <i>Surf. Sci.</i> <b>366</b>, 394 (1996).\n"
#~ "\n"
#~ "<b>Cu, Ag和Au的另一组参数值:</b>\n"
#~ "\n"
#~ "Torben Rasmussen 根据实验值进行了重新优化, 得到了Cu, Ag和Au\n"
#~ "的另一组参数值（部分已发表）。这组参数很好的修正了堆积能计算值。\n"
#~ "\n"
#~ "<b>（钌）Ru：</b>\n"
#~ "\n"
#~ "（钌）Ru的参数来自 J. Gavnholt and\n"
#~ "J. Schiøtz, <i>Phys. Rev. B</i> <b>77</b>, 035404 (2008).\n"
#~ "\n"
#~ "<b>金属玻璃：</b>\n"
#~ "\n"
#~ "MgCu 和 CuZr 金属玻璃的参数。 MgCu\n"
#~ "的参数来自于 N. P. Bailey, J. Schiøtz and\n"
#~ "K. W. Jacobsen, <i>Phys. Rev. B</i> <b>69</b>, 144205 (2004).\n"
#~ "CuZr的参数来自于 A. Paduraru, A. Kenoufi, N. P. Bailey and\n"
#~ "J. Schiøtz, <i>Adv. Eng. Mater.</i> <b>9</b>, 505 (2007).\n"

#~ msgid ""
#~ "The EMT potential is a many-body potential, giving a\n"
#~ "good description of the late transition metals crystalling\n"
#~ "in the FCC crystal structure.  The elements described by the\n"
#~ "main set of EMT parameters are Al, Ni, Cu, Pd, Ag, Pt, and\n"
#~ "Au.  In addition, this implementation allows for the use of\n"
#~ "H, N, O and C adatoms, although the description of these is\n"
#~ "most likely not very good.\n"
#~ "\n"
#~ "<b>This is the ASE implementation of EMT.</b> For large\n"
#~ "simulations the ASAP implementation is more suitable; this\n"
#~ "implementation is mainly to make EMT available when ASAP is\n"
#~ "not installed.\n"
#~ msgstr ""
#~ "EMT势是一种多体势。它能很好的描述FCC结构的后过渡性金属元素。\n"
#~ "EMT所包含的元素有 （铝）Al, （镍）Ni, （铜）Cu, （钯）Pd, \n"
#~ "（银）Ag, （铂）Pt, (金)Au。另外，此版本允许用户使用（氢）H, \n"
#~ "（氮）N, （氧）O和（碳）C作为吸附原子, 但是其精度并不高。\n"
#~ "\n"
#~ "<b>这是ASE中的所包含的EMT计算器，它仅提供测试所用的最基本的\n"
#~ "功能。如需对于大体系进行计算，请安装使用ASAP中所包含的版本。\n"

#~ msgid ""
#~ "The Brenner potential is a reactive bond-order potential for\n"
#~ "carbon and hydrocarbons.  As a bond-order potential, it takes\n"
#~ "into account that carbon orbitals can hybridize in different\n"
#~ "ways, and that carbon can form single, double and triple\n"
#~ "bonds.  That the potential is reactive means that it can\n"
#~ "handle gradual changes in the bond order as chemical bonds\n"
#~ "are formed or broken.\n"
#~ "\n"
#~ "The Brenner potential is implemented in Asap, based on a\n"
#~ "C implentation published at http://www.rahul.net/pcm/brenner/ .\n"
#~ "\n"
#~ "The potential is documented here:\n"
#~ "  Donald W Brenner, Olga A Shenderova, Judith A Harrison,\n"
#~ "  Steven J Stuart, Boris Ni and Susan B Sinnott:\n"
#~ "  \"A second-generation reactive empirical bond order (REBO)\n"
#~ "  potential energy expression for hydrocarbons\",\n"
#~ "  J. Phys.: Condens. Matter 14 (2002) 783-802.\n"
#~ "  doi: 10.1088/0953-8984/14/4/312\n"
#~ msgstr ""
#~ "Brenner势是一种适用于碳材料和碳氢化合物的反应键极势。\n"
#~ "作为一种键极势, 它考虑了碳轨道的多种可能杂化形式，\n"
#~ "以及碳可以单键，双键，三重键。反应键极势中的反应意味着他能\n"
#~ "处理化学键断裂和形成过程中键极的逐步变化。\n"
#~ "\n"
#~ "ASAP 包含Brenner势, 这势是用C语言编写的。来自于http://www.rahul.net/pcm/"
#~ "brenner/ .\n"
#~ "\n"
#~ "该势能的参考文献：\n"
#~ "  Donald W Brenner, Olga A Shenderova, Judith A Harrison,\n"
#~ "  Steven J Stuart, Boris Ni and Susan B Sinnott:\n"
#~ "  \"A second-generation reactive empirical bond order (REBO)\n"
#~ "  potential energy expression for hydrocarbons\",\n"
#~ "  J. Phys.: Condens. Matter 14 (2002) 783-802.\n"
#~ "  doi: 10.1088/0953-8984/14/4/312\n"

# Here, I am not sure about how to present the name of GPAW in chinese.***
#, fuzzy
#~| msgid ""
#~| "GPAW implements Density Functional Theory using a\n"
#~| "<b>G</b>rid-based real-space representation of the wave\n"
#~| "functions, and the <b>P</b>rojector <b>A</b>ugmented <b>W</b>ave\n"
#~| "method for handling the core regions.  \n"
#~ msgid ""
#~ "GPAW implements Density Functional Theory using a\n"
#~ "<b>G</b>rid-based real-space representation of the wave\n"
#~ "functions, and the <b>P</b>rojector <b>A</b>ugmented <b>W</b>ave\n"
#~ "method for handling the core regions.\n"
#~ msgstr ""
#~ "GPAW 基于密度泛函理论（Density Functional Theory），使用实空间格点展开波函"
#~ "数，\n"
#~ "使用投射增广波方法（ <b>P</b>rojector <b>A</b>ugmented <b>W</b>ave "
#~ "method）\n"
#~ "处理核心电子。\n"

#, fuzzy
#~| msgid ""
#~| "FHI-aims is an external package implementing density \n"
#~| "functional theory and quantum chemical methods using \n"
#~| "all-electron methods and a numeric local orbital basis set. \n"
#~| "For full details, see http://www.fhi-berlin.mpg.de/aims/ \n"
#~| "or Comp. Phys. Comm. v180 2175 (2009). The ASE \n"
#~| "documentation contains information on the keywords and \n"
#~| "functionalities available within this interface. \n"
#~ msgid ""
#~ "FHI-aims is an external package implementing density\n"
#~ "functional theory and quantum chemical methods using\n"
#~ "all-electron methods and a numeric local orbital basis set.\n"
#~ "For full details, see http://www.fhi-berlin.mpg.de/aims/\n"
#~ "or Comp. Phys. Comm. v180 2175 (2009). The ASE\n"
#~ "documentation contains information on the keywords and\n"
#~ "functionalities available within this interface.\n"
#~ msgstr ""
#~ "FHI-aims 是一个外部的软件包。它基于密度泛函理论和量子化学方法，\n"
#~ "使用全电子和数值局部轨道基组。\n"
#~ "参考信息： http://www.fhi-berlin.mpg.de/aims/ \n"
#~ "或者 Comp. Phys. Comm. v180 2175 (2009). ASE \n"
#~ "的使用手册里包含关键词和功能信息。\n"

#~ msgid ""
#~ "WARNING:\n"
#~ "Your system seems to have more than zero but less than\n"
#~ "three periodic dimensions. Please check that this is\n"
#~ "really what you want to compute. Assuming full\n"
#~ "3D periodicity for this calculator."
#~ msgstr ""
#~ "警告：\n"
#~ "你的计算体系的周期性大于零维但小于三维。请对此进行确认。\n"
#~ "此计算器将使用三维周期性进行计算。"

#~ msgid ""
#~ "VASP is an external package implementing density\n"
#~ "functional functional theory using pseudopotentials\n"
#~ "or the projector-augmented wave method together\n"
#~ "with a plane wave basis set. For full details, see\n"
#~ "http://cms.mpi.univie.ac.at/vasp/vasp/\n"
#~ msgstr ""
#~ "VASP 是一个外部的软件包。它基于密度泛函理论，使用\n"
#~ "赝势或者PAW方法，波函数用平面波展开。参考信息：\n"
#~ "http://cms.mpi.univie.ac.at/vasp/vasp/\n"

#~ msgid "Default (Al, Ni, Cu, Pd, Ag, Pt, Au)"
#~ msgstr "默认（铝Al, 镍Ni, 铜Cu, 钯Pd,银Ag, 铂Pt, 金Au）"

#~ msgid "Alternative Cu, Ag and Au"
#~ msgstr "另外 铜Cu，银Ag， 金Au"

#~ msgid "Ruthenium"
#~ msgstr "Ruthenium"

#~ msgid "CuMg and CuZr metallic glass"
#~ msgstr "CuMg 和 CuZr 金属玻璃"

#, fuzzy
#~| msgid "Set _Calculator"
#~ msgid "Select calculator"
#~ msgstr "设置计算器"

#~ msgid "None"
#~ msgstr "无"

#~ msgid "Lennard-Jones (ASAP)"
#~ msgstr "Lennard-Jones (ASAP)"

#~ msgid "Setup"
#~ msgstr "设置"

#~ msgid "EMT - Effective Medium Theory (ASAP)"
#~ msgstr "EMT - 有效介质理论 (ASAP)"

#~ msgid "EMT - Effective Medium Theory (ASE)"
#~ msgstr "EMT - 有效介质理论 (ASE)"

#~ msgid "Brenner Potential (ASAP)"
#~ msgstr "Brenner 势 (ASAP)"

#~ msgid "Density Functional Theory (GPAW)"
#~ msgstr "密度泛函理论 (GPAW)"

#~ msgid "Density Functional Theory (FHI-aims)"
#~ msgstr "密度泛函理论 (FHI-aims)"

#~ msgid "Density Functional Theory (VASP)"
#~ msgstr "密度泛函理论 (VASP)"

#~ msgid "Check that the calculator is reasonable."
#~ msgstr "检查计算器是否合理。"

#~ msgid "ASAP is not installed. (Failed to import asap3)"
#~ msgstr "ASAP 没有安装。（加载asap3失败）"

#~ msgid "You must set up the Lennard-Jones parameters"
#~ msgstr "你必须设置Lennard-Jones参数"

#~ msgid "Could not create useful Lennard-Jones calculator."
#~ msgstr "创建Lennard-Jones 计算器失败。"

#~ msgid "Could not attach EMT calculator to the atoms."
#~ msgstr "不能将EMT计算器赋予给原子。"

#, fuzzy
#~| msgid "You must set up the GPAW parameters"
#~ msgid "You must set up the EAM parameters"
#~ msgstr "你必须设置GPAW的参数"

#~ msgid "GPAW is not installed. (Failed to import gpaw)"
#~ msgstr "GPAW 没有安装。（加载gpaw失败）"

#~ msgid "You must set up the GPAW parameters"
#~ msgstr "你必须设置GPAW的参数"

#~ msgid "You must set up the FHI-aims parameters"
#~ msgstr "你必须设置FHI-aims的参数"

#~ msgid "You must set up the VASP parameters"
#~ msgstr "你必须设置VASP的参数"

#~ msgid "Element %(sym)s not allowed by the '%(name)s' calculator"
#~ msgstr "'%(name)s'计算器中无法使用元素 %(sym)s"

#~ msgid "Info"
#~ msgstr "信息"

#~ msgid "Lennard-Jones parameters"
#~ msgstr "Lennard-Jones参数"

#~ msgid "Specify the Lennard-Jones parameters here"
#~ msgstr "请设置Lennard-Jones参数"

#~ msgid "Epsilon (eV):"
#~ msgstr "Epsilon (eV):"

#~ msgid "Sigma (Å):"
#~ msgstr "Sigma (Å):"

# I am not sure about this part. What calculator is this info for?***
#~ msgid "Shift to make smooth at cutoff"
#~ msgstr "在阶段处进行调整，使其光滑"

# XXX截断 
#, fuzzy
#~| msgid "GPAW parameters"
#~ msgid "EAM parameters"
#~ msgstr "GPAW 参数"

#, fuzzy
#~| msgid "Import control.in"
#~ msgid "Import Potential"
#~ msgstr "导入control.in"

#, fuzzy
#~| msgid "Import control.in file ... "
#~ msgid "Import .alloy or .adp potential file ... "
#~ msgstr "导入control.in文件 ..."

# XXX截断 
#~ msgid "GPAW parameters"
#~ msgstr "GPAW 参数"

#~ msgid "%i atoms.\n"
#~ msgstr "%i 原子.\n"

#~ msgid "Orthogonal unit cell: %.2f x %.2f x %.2f Å."
#~ msgstr "正交原胞: %.2f x %.2f x %.2f Å."

#~ msgid "Non-orthogonal unit cell:\n"
#~ msgstr "非正交原胞:\n"

#~ msgid "Exchange-correlation functional: "
#~ msgstr "交换－相关 势能："

#~ msgid "Grid spacing"
#~ msgstr "格子间距"

#~ msgid "Grid points"
#~ msgstr "格点"

#~ msgid "h<sub>eff</sub> = (%.3f, %.3f, %.3f) Å"
#~ msgstr "h<sub>eff</sub> = (%.3f, %.3f, %.3f) Å"

#~ msgid "k-points  k = ("
#~ msgstr "k点数  k = ("

#~ msgid "k-points x size:  (%.1f, %.1f, %.1f) Å"
#~ msgstr "k点数 x 尺寸:  (%.1f, %.1f, %.1f) Å"

#~ msgid "Spin polarized"
#~ msgstr "自旋极化"

#~ msgid "FD - Finite Difference (grid) mode"
#~ msgstr "FD - 有限差分 (格点) 模式"

#~ msgid "LCAO - Linear Combination of Atomic Orbitals"
#~ msgstr "LCAO - 原子轨道线性组合"

#~ msgid "Mode: "
#~ msgstr "模式："

#~ msgid "sz - Single Zeta"
#~ msgstr "sz - 单Zeta"

#~ msgid "szp - Single Zeta polarized"
#~ msgstr "szp - 单Zeta极化"

#~ msgid "dzp - Double Zeta polarized"
#~ msgstr "dzp - 双Zeta极化"

#~ msgid "Basis functions: "
#~ msgstr "基组函数："

#~ msgid "Non-standard mixer parameters"
#~ msgstr "非标准混合参数"

#~ msgid "FHI-aims parameters"
#~ msgstr "FHI-aims参数"

#~ msgid "Periodic geometry, unit cell is:\n"
#~ msgstr "周期性构型，原胞是：\n"

#~ msgid "Non-periodic geometry.\n"
#~ msgstr "非周期性构型\n"

# Not sure about this translation.
#~ msgid "Hirshfeld-based dispersion correction"
#~ msgstr "基于Hirshfeld的分布修正"

#~ msgid "Spin / initial moment "
#~ msgstr "自旋 / 初始磁矩"

#~ msgid "   Charge"
#~ msgstr "   电荷"

#~ msgid "   Relativity"
#~ msgstr "    相对论"

#~ msgid " Threshold"
#~ msgstr "   阈值"

#~ msgid "Self-consistency convergence:"
#~ msgstr "自恰收敛："

#~ msgid "Compute forces"
#~ msgstr "计算受力"

#~ msgid "Energy:                 "
#~ msgstr "能量：                   "

#~ msgid " eV   Sum of eigenvalues:  "
#~ msgstr " eV   本征值之和："

#~ msgid " eV"
#~ msgstr " eV"

#~ msgid "Electron density: "
#~ msgstr "电子密度："

#~ msgid "        Force convergence:  "
#~ msgstr "         力的收敛："

#~ msgid " eV/Ang  "
#~ msgstr " eV/Ang "

#~ msgid "Additional keywords: "
#~ msgstr "其他关键词："

#~ msgid "FHI-aims execution command: "
#~ msgstr "FHI-aims执行命令："

#~ msgid "Directory for species defaults: "
#~ msgstr "默认的元素目录："

#~ msgid "Set Defaults"
#~ msgstr "设置默认值"

#~ msgid "Import control.in"
#~ msgstr "导入control.in"

#~ msgid "Export control.in"
#~ msgstr "导出control.in"

#~ msgid "Export parameters ... "
#~ msgstr "导出参数"

#~ msgid "Import control.in file ... "
#~ msgstr "导入control.in文件 ..."

#~ msgid ""
#~ "Please use the facilities provided in this window to manipulate the "
#~ "keyword: %s!"
#~ msgstr "请使用这个窗口里提供的工具来修改关键词：%s!"

#~ msgid ""
#~ "Don't know this keyword: %s\n"
#~ "\n"
#~ "Please check!\n"
#~ "\n"
#~ "If you really think it should be available, please add it to the top of "
#~ "ase/calculators/aims.py."
#~ msgstr ""
#~ "不存在这个关键词： %s\n"
#~ "\n"
#~ "请检查！\n"
#~ "\n"
#~ "如果你觉得此关键词应当存在，请添加在 ase/calculators/aims.py的上方。"

#~ msgid "VASP parameters"
#~ msgstr "VASP 参数"

#~ msgid "Periodic geometry, unit cell is: \n"
#~ msgstr "周期性构型，原胞是：\n"

#~ msgid ")    Cutoff: "
#~ msgstr ")    截断: "

#~ msgid "    Precision: "
#~ msgstr "   精度："

#~ msgid "k-points x size:  (%.1f, %.1f, %.1f) Å       "
#~ msgstr "k点数 x 尺寸:  (%.1f, %.1f, %.1f) Å       "

# don't know how to translate this term.
#~ msgid "Smearing: "
#~ msgstr "Smearing: "

# order of what?***
#~ msgid " order: "
#~ msgstr " order: "

#~ msgid " width: "
#~ msgstr " 宽度: "

#~ msgid "Self-consistency convergence: "
#~ msgstr "自恰收敛:"

#~ msgid "VASP execution command: "
#~ msgstr "VASP 执行命令："

#~ msgid "Import VASP files"
#~ msgstr "导入 VASP 文件"

#~ msgid "Export VASP files"
#~ msgstr "导出 VASP 文件"

#~ msgid "<b>WARNING:</b> cutoff energy is lower than recommended minimum!"
#~ msgstr "<b>警告：</b> 截断能（E-cutoff）低于推荐值！"

#~ msgid "Import VASP input files: choose directory ... "
#~ msgstr "导入 VASP 输入文件：选择目录 ..."

#~ msgid "Export VASP input files: choose directory ... "
#~ msgstr "导出 VASP 输入文件：选择目录 ..."

#~ msgid ""
#~ "Don't know this keyword: %s\n"
#~ "Please check!\n"
#~ "\n"
#~ "If you really think it should be available, please add it to the top of "
#~ "ase/calculators/vasp.py."
#~ msgstr ""
#~ "不存在这个关键词： %s\n"
#~ "\n"
#~ "请检查！\n"
#~ "\n"
#~ "如果你觉得此关键词应当存在，请添加在 ase/calculators/vasp.py的上方。"

#, fuzzy
#~| msgid ""
#~| "\n"
#~| "    Global commands work on all frames or only on the current frame\n"
#~| "    - Assignment of a global variable may not reference a local one\n"
#~| "    - use 'Current frame' switch to switch off application to all "
#~| "frames\n"
#~| "    <c>e</c>:\t\ttotal energy of one frame\n"
#~| "    <c>fmax</c>:\tmaximal force in one frame\n"
#~| "    <c>A</c>:\tunit cell\n"
#~| "    <c>E</c>:\t\ttotal energy array of all frames\n"
#~| "    <c>F</c>:\t\tall forces in one frame\n"
#~| "    <c>M</c>:\tall magnetic moments\n"
#~| "    <c>R</c>:\t\tall atomic positions\n"
#~| "    <c>S</c>:\tall selected atoms (boolean array)\n"
#~| "    <c>D</c>:\tall dynamic atoms (boolean array)\n"
#~| "    examples: <c>frame = 1</c>, <c>A[0][1] += 4</c>, <c>e-E[-1]</c>\n"
#~| "\n"
#~| "    Atom commands work on each atom (or a selection) individually\n"
#~| "    - these can use global commands on the RHS of an equation\n"
#~| "    - use 'selected atoms only' to restrict application of command\n"
#~| "    <c>x,y,z</c>:\tatomic coordinates\n"
#~| "    <c>r,g,b</c>:\tatom display color, range is [0..1]\n"
#~| "    <c>rad</c>:\tatomic radius for display\n"
#~| "    <c>s</c>:\t\tatom is selected\n"
#~| "    <c>d</c>:\t\tatom is movable\n"
#~| "    <c>f</c>:\t\tforce\n"
#~| "    <c>Z</c>:\tatomic number\n"
#~| "    <c>m</c>:\tmagnetic moment\n"
#~| "    examples: <c>x -= A[0][0], s = z > 5, Z = 6</c>\n"
#~| "\n"
#~| "    Special commands and objects:\n"
#~| "    <c>sa,cf</c>:\t(un)restrict to selected atoms/current frame\n"
#~| "    <c>frame</c>:\tframe number\n"
#~| "    <c>center</c>:\tcenters the system in its existing unit cell\n"
#~| "    <c>del S</c>:\tdelete selection\n"
#~| "    <c>CM</c>:\tcenter of mass\n"
#~| "    <c>ans[-i]</c>:\tith last calculated result\n"
#~| "    <c>exec file</c>: executes commands listed in file\n"
#~| "    <c>cov[Z]</c>:(read only): covalent radius of atomic number Z\n"
#~| "    <c>gui</c>:\tadvanced: ase-gui window python object\n"
#~| "    <c>img</c>:\tadvanced: ase-gui images object\n"
#~| "    "
#~ msgid ""
#~ "\n"
#~ "    Global commands work on all frames or only on the current frame\n"
#~ "    - Assignment of a global variable may not reference a local one\n"
#~ "    - use 'Current frame' switch to switch off application to all frames\n"
#~ "    <c>e</c>:\t\ttotal energy of one frame\n"
#~ "    <c>fmax</c>:\tmaximal force in one frame\n"
#~ "    <c>A</c>:\tunit cell\n"
#~ "    <c>E</c>:\t\ttotal energy array of all frames\n"
#~ "    <c>F</c>:\t\tall forces in one frame\n"
#~ "    <c>M</c>:\tall magnetic moments\n"
#~ "    <c>R</c>:\t\tall atomic positions\n"
#~ "    <c>S</c>:\tall selected atoms (boolean array)\n"
#~ "    <c>D</c>:\tall dynamic atoms (boolean array)\n"
#~ "    examples: <c>frame = 1</c>, <c>A[0][1] += 4</c>, <c>e-E[-1]</c>\n"
#~ "\n"
#~ "    Atom commands work on each atom (or a selection) individually\n"
#~ "    - these can use global commands on the RHS of an equation\n"
#~ "    - use 'selected atoms only' to restrict application of command\n"
#~ "    <c>x,y,z</c>:\tatomic coordinates\n"
#~ "    <c>r,g,b</c>:\tatom display color, range is [0..1]\n"
#~ "    <c>rad</c>:\tatomic radius for display\n"
#~ "    <c>s</c>:\t\tatom is selected\n"
#~ "    <c>d</c>:\t\tatom is movable\n"
#~ "    <c>f</c>:\t\tforce\n"
#~ "    <c>Z</c>:\tatomic number\n"
#~ "    <c>m</c>:\tmagnetic moment\n"
#~ "    examples: <c>x -= A[0][0], s = z > 5, Z = 6</c>\n"
#~ "\n"
#~ "    Special commands and objects:\n"
#~ "    <c>sa,cf</c>:\t(un)restrict to selected atoms/current frame\n"
#~ "    <c>frame</c>:\tframe number\n"
#~ "    <c>center</c>:\tcenters the system in its existing unit cell\n"
#~ "    <c>del S</c>:\tdelete selection\n"
#~ "    <c>CM</c>:\tcenter of mass\n"
#~ "    <c>ans[-i]</c>:\tith last calculated result\n"
#~ "    <c>exec file</c>: executes commands listed in file\n"
#~ "    <c>cov[Z]</c>:(read only): covalent radius of atomic number Z\n"
#~ "    <c>gui</c>:\tadvanced: gui window python object\n"
#~ "    <c>img</c>:\tadvanced: gui images object\n"
#~ "    "
#~ msgstr ""
#~ "\n"
#~ "    全局命令作用于所有构型或者当前构型\n"
#~ "    - 全局变量的指定可能与局域变量无关\n"
#~ "    - 使用'当前构型'开关使其不应用于所有构型\n"
#~ "    <c>e</c>:\t\t一个构型的总能\n"
#~ "    <c>fmax</c>:\t一个构型的最大受力\n"
#~ "    <c>A</c>:\t原胞\n"
#~ "    <c>E</c>:\t\t所有构型的总能\n"
#~ "    <c>F</c>:\t\t一个构型的总受力\n"
#~ "    <c>M</c>:\t所有构型的磁矩\n"
#~ "    <c>R</c>:\t\t所有的原子位置\n"
#~ "    <c>S</c>:\t所有所选的原子(boolean array)\n"
#~ "    <c>D</c>:\t所有可移动的原子 (boolean array)\n"
#~ "    例子: <c>构型 = 1</c>, <c>A[0][1] += 4</c>, <c>e-E[-1]</c>\n"
#~ "\n"
#~ "    Atom 命令分别作用于各个原子（或所选的原子集合）\n"
#~ "    - 这些可使用全局命令作用于the RHS of an equation\n"
#~ "    - 使用 '仅应用于被选择的原子' 来限制命令的应用\n"
#~ "    <c>x,y,z</c>:\t原子的坐标\n"
#~ "    <c>r,g,b</c>:\t要显示的原子颜色，范围是[0..1]\n"
#~ "    <c>rad</c>:\t显示的原子半径\n"
#~ "    <c>s</c>:\t\t已被选择的原子\n"
#~ "    <c>d</c>:\t\t可移动的原子\n"
#~ "    <c>f</c>:\t\t受力\n"
#~ "    <c>Z</c>:\t原子数\n"
#~ "    <c>m</c>:\t磁矩\n"
#~ "    例子: <c>x -= A[0][0], s = z > 5, Z = 6</c>\n"
#~ "\n"
#~ "    特殊命令和对象:\n"
#~ "    <c>sa,cf</c>:\t（不）限制于所选原子/当前构型\n"
#~ "    <c>frame</c>:\t构型序号\n"
#~ "    <c>center</c>:\t将体系居中于当前存在的原胞中\n"
#~ "    <c>del S</c>:\t删除选择\n"
#~ "    <c>CM</c>:\t重心\n"
#~ "    <c>ans[-i]</c>:\t最后第i个计算结果\n"
#~ "    <c>exec file</c>: 执行文件中列出的命令\n"
#~ "    <c>cov[Z]</c>:(仅仅读取): 原子数为Z的原子共价半径\n"
#~ "    <c>gui</c>:\t高级：ase-gui 窗口 python 对象\n"
#~ "    <c>img</c>:\t高级：ase-gui 图像 对象\n"
#~ "    "

#~ msgid "Expert user mode"
#~ msgstr "专家级模式"

#~ msgid "Welcome to the ASE Expert user mode"
#~ msgstr "欢迎使用ASE专家级模式"

#~ msgid "Only selected atoms (sa)   "
#~ msgstr "仅应用于被选择的原子(sa)   "

#~ msgid "Only current frame (cf)  "
#~ msgstr "仅应用于当前构型 (cf)  "

#~ msgid ""
#~ "Global: Use A, D, E, M, N, R, S, n, frame; Atoms: Use a, f, m, s, x, y, "
#~ "z, Z     "
#~ msgstr ""
#~ "全局: 使用 A, D, E, M, N, R, S, n, 构型; Atoms: 使用 a, f, m, s, x, y, z, "
#~ "Z     "

#~ msgid "*** WARNING: file does not exist - %s"
#~ msgstr "*** 警告：文件不存在 - %s"

#~ msgid "*** WARNING: No atoms selected to work with"
#~ msgstr "*** 警告： 没有选择原子"

#~ msgid "*** Only working on selected atoms"
#~ msgstr "*** 仅应用于所选的原子"

#~ msgid "*** Working on all atoms"
#~ msgstr "*** 应用于所有原子"

#~ msgid "*** Only working on current image"
#~ msgstr "***  仅应用于当前图像"

#~ msgid "*** Working on all images"
#~ msgstr "*** 应用于所有图像"

#~ msgid "Save Terminal text ..."
#~ msgstr "保存终端文字 ..."

#~ msgid "Cancel"
#~ msgstr "取消"

#~ msgid "Algorithm: "
#~ msgstr "算法："

#~ msgid "Convergence criterion: F<sub>max</sub> = "
#~ msgstr "收敛标准: F<sub>max</sub> = "

#~ msgid "Max. number of steps: "
#~ msgstr "最多迭代次数："

#~ msgid "Pseudo time step: "
#~ msgstr "赝时间次数："

#~ msgid "Energy minimization"
#~ msgstr "能量最小化"

#~ msgid "Minimize the energy with respect to the positions."
#~ msgstr "根据位置，进行能量最小化"

#~ msgid "Running ..."
#~ msgstr "运行中 ..."

#~ msgid "Minimization CANCELLED after %i steps."
#~ msgstr "%i步后优化被取消。"

#~ msgid "Out of memory, consider using LBFGS instead"
#~ msgstr "内存不足，请改用LBFGS方法"

#~ msgid "Minimization completed in %i steps."
#~ msgstr "经过%i步后，优化结束。"

#~ msgid "Progress"
#~ msgstr "过程"

#~ msgid "Scaling deformation:"
#~ msgstr "调整变形："

#~ msgid "Step number %s of %s."
#~ msgstr " 第%s步，总共  %s步。"

#~ msgid "Energy minimization:"
#~ msgstr "能量优化："

#~ msgid "Step number: "
#~ msgstr "步数："

#~ msgid "F<sub>max</sub>: "
#~ msgstr "F<sub>max</sub>: "

#~ msgid "unknown"
#~ msgstr "未知"

#~ msgid "Status: "
#~ msgstr "状态："

#~ msgid "Iteration: "
#~ msgstr "迭代："

#~ msgid "log<sub>10</sub>(change):"
#~ msgstr "log<sub>10</sub>(变化):"

#~ msgid "Wave functions: "
#~ msgstr "波函数："

#~ msgid "Density: "
#~ msgstr "密度："

#~ msgid "GPAW version: "
#~ msgstr "GPAW 版本："

#~ msgid "N/A"
#~ msgstr "N/A"

#~ msgid "Memory estimate: "
#~ msgstr "所需内存大小估计"

#~ msgid "No info"
#~ msgstr "没有信息"

#~ msgid "Initializing"
#~ msgstr "初始化中"

#~ msgid "Positions:"
#~ msgstr "位置："

#~ msgid "Starting calculation"
#~ msgstr "开始计算中"

#~ msgid "unchanged"
#~ msgstr "没改变"

#~ msgid "Self-consistency loop"
#~ msgstr "自恰迭代循环"

#~ msgid "Calculating forces"
#~ msgstr "正在计算受力"

#~ msgid " (converged)"
#~ msgstr "（收敛）"

#~ msgid "No atoms loaded."
#~ msgstr "未加载原子"

#~ msgid "FCC(111) non-orthogonal"
#~ msgstr "FCC(111) 非正交的"

#~ msgid "FCC(111) orthogonal"
#~ msgstr "FCC(111) 正交的"

#~ msgid "BCC(110) non-orthogonal"
#~ msgstr "BCC(110) 非正交的"

#~ msgid "BCC(110) orthogonal"
#~ msgstr "BCC(110) 正交的"

#~ msgid "BCC(111) non-orthogonal"
#~ msgstr "BCC(111) 非正交的"

#~ msgid "BCC(111) orthogonal"
#~ msgstr "BCC(111) 正交的"

#~ msgid "HCP(0001) non-orthogonal"
#~ msgstr "HCP(0001) 非正交的"

#~ msgid "Element: "
#~ msgstr "元素"

#~ msgid "a:"
#~ msgstr "a"

#~ msgid "(%.1f %% of ideal)"
#~ msgstr "(%.1f %% 理想的)"

#~ msgid "      \t\tz: "
#~ msgstr "      \t\tz: "

#~ msgid " layers,  "
#~ msgstr " 层， "

#~ msgid " Å vacuum"
#~ msgstr "Å 真空"

#~ msgid "\t\tNo size information yet."
#~ msgstr "\t\t还没有尺寸大小信息。"

#~ msgid "%i atoms."
#~ msgstr "%i 原子"

#~ msgid "No structure specified!"
#~ msgstr "未指定结构！"

#~ msgid "%(struct)s lattice constant unknown for %(element)s."
#~ msgstr "对于%(element)s， %(struct)s 晶格常数未知。"

#~ msgid "By atomic number, user specified"
#~ msgstr "根据原子序数，用户指定"

#~ msgid "Manually specified"
#~ msgstr "手动设置"

#~ msgid "All the same color"
#~ msgstr "使用统一颜色"

#, fuzzy
#~| msgid "This should not be displayed!"
#~ msgid "This should not be displayed in forces!"
#~ msgstr "这不应该被显示！"

#~ msgid "Min: "
#~ msgstr "最小值："

#~ msgid "  Max: "
#~ msgstr "  最大值："

#~ msgid "  Steps: "
#~ msgstr " 步骤："

#~ msgid "This should not be displayed!"
#~ msgstr "这不应该被显示！"

#~ msgid "Create a color scale:"
#~ msgstr "创建一个颜色标度："

#~ msgid "Black - white"
#~ msgstr "黑色－白色"

#~ msgid "Black - red - yellow - white"
#~ msgstr "黑色－红色－黄色－白色"

#~ msgid "Black - green - white"
#~ msgstr "黑色－绿色－白色"

#~ msgid "Black - blue - cyan"
#~ msgstr "黑色－蓝色－橙色"

#, fuzzy
#~| msgid "Black - white"
#~ msgid "Blue - white - red"
#~ msgstr "黑色－白色"

#~ msgid "Hue"
#~ msgstr "色度"

#~ msgid "Named colors"
#~ msgstr "命名的颜色"

#~ msgid "Create"
#~ msgstr "创建"

#~ msgid "ERROR"
#~ msgstr "错误"

#~ msgid "ERR"
#~ msgstr "错误"

#~ msgid "Incorrect color specification"
#~ msgstr "颜色指定错误"

#~ msgid " selected atoms:"
#~ msgstr " 已选择的原子："

#~ msgid "Close"
#~ msgstr "关闭"

#~ msgid "Debug"
#~ msgstr "除错"

#~ msgid "Bug Detected"
#~ msgstr "发现错误"

#~ msgid "A programming error has been detected."
#~ msgstr "发现一个程序错误。"

#~ msgid ""
#~ "It probably isn't fatal, but the details should be reported to the "
#~ "developers nonetheless."
#~ msgstr "这也许不致命，但请将错误细节提交给开发者"

#~ msgid "Report..."
#~ msgstr "提交..."

#~ msgid "Details..."
#~ msgstr "细节..."

#~ msgid ""
#~ "From: buggy_application\"\n"
#~ "To: bad_programmer\n"
#~ "Subject: Exception feedback\n"
#~ "\n"
#~ "%s"
#~ msgstr ""
#~ "来自: buggy_application\"\n"
#~ "至: bad_programmer\n"
#~ "主题: Exception feedback\n"
#~ "\n"
#~ "%s"

#~ msgid "Bug Details"
#~ msgstr "错误细节"

#~ msgid "Create a new file"
#~ msgstr "创建新文件"

#~ msgid "New ase.gui window"
#~ msgstr "新ase.gui窗口"

#~ msgid "Save current file"
#~ msgstr "保存当前文件"

#~ msgid "Quit"
#~ msgstr "退出"

#~ msgid "_Copy"
#~ msgstr "复制"

#~ msgid "Copy current selection and its orientation to clipboard"
#~ msgstr "复制当前的选择和它的取向到剪贴板"

#~ msgid "_Paste"
#~ msgstr "粘贴"

#~ msgid "Insert current clipboard selection"
#~ msgstr "插入当前剪贴板的内容"

#~ msgid "Change tags, moments and atom types of the selected atoms"
#~ msgstr "修改所选原子的标签，磁矩和类型"

#~ msgid "Insert or import atoms and molecules"
#~ msgstr "插入或导入原子和分子"

#~ msgid "Delete the selected atoms"
#~ msgstr "删除所选原子"

#, fuzzy
#~| msgid "xy-plane"
#~ msgid "'xy' Plane"
#~ msgstr "xy平面"

#, fuzzy
#~| msgid "yz-plane"
#~ msgid "'yz' Plane"
#~ msgstr "yz平面"

#, fuzzy
#~| msgid "xz-plane"
#~ msgid "'xz' Plane"
#~ msgstr "xz平面"

#~ msgid "Create a bulk crystal with arbitrary orientation"
#~ msgstr "创建一个任意取向的晶体"

#~ msgid "Create the most common surfaces"
#~ msgstr "创建常用的表面"

#~ msgid "Create a crystalline nanoparticle"
#~ msgstr "创建晶体纳米颗粒"

#~ msgid "Create a nanotube"
#~ msgstr "创建纳米管"

#~ msgid "Create a graphene sheet or nanoribbon"
#~ msgstr "创建石墨烯单层或纳米带"

#~ msgid "Set a calculator used in all calculation modules"
#~ msgstr "设置所有计算模块使用的计算器"

#~ msgid "Calculate energy and forces"
#~ msgstr "计算能量和受力"

#~ msgid "Minimize the energy"
#~ msgstr "最小化能量"

#~ msgid "Scale system"
#~ msgstr "缩放体系"

#~ msgid "Deform system by scaling it"
#~ msgstr "通过缩放改变体系形状"

#~ msgid "Debug ..."
#~ msgstr "除错"

#~ msgid "Orien_t atoms"
#~ msgstr "取向原子"

#~ msgid "<<filename>>"
#~ msgstr "<<filename>>"

#~ msgid "Paste"
#~ msgstr "粘贴"

#~ msgid "Insert atom or molecule"
#~ msgstr "插入原子或分子"

#~ msgid "_Cancel"
#~ msgstr "取消"

#~ msgid "Atom"
#~ msgstr "原子"

#~ msgid "Confirmation"
#~ msgstr "确认"

#~ msgid "Delete selected atom?"
#~ msgid_plural "Delete selected atoms?"
#~ msgstr[0] "删除所选原子？"

#~ msgid "File type:"
#~ msgstr "文件类型："

#~ msgid "Not implemented!"
#~ msgstr "该模块尚未加入！"

#~ msgid "do you really need it?"
#~ msgstr "你确定需要它？"

#~ msgid "Dummy placeholder object"
#~ msgstr "虚设占位符对象"

#~ msgid "Set all directions to default values"
#~ msgstr "把所有方向设置成默认值"

#~ msgid "Particle size: "
#~ msgstr "纳米颗粒大小："

#~ msgid "%.1f Å"
#~ msgstr "%.1f Å"

#~ msgid "Python"
#~ msgstr "Python"

#~ msgid ""
#~ "\n"
#~ "Title: %(title)s\n"
#~ "Time: %(time)s\n"
#~ msgstr ""
#~ "\n"
#~ "标题: %(title)s\n"
#~ "时间: %(time)s\n"

#~ msgid "ag: Python code"
#~ msgstr "ag: Python代码"

#~ msgid "Information:"
#~ msgstr "信息："

#~ msgid "Python code:"
#~ msgstr "Python代码："

#~ msgid "Homogeneous scaling"
#~ msgstr "均匀缩放"

#~ msgid "3D deformation   "
#~ msgstr "3维形变  "

#~ msgid "2D deformation   "
#~ msgstr "2维形变  "

#~ msgid "1D deformation   "
#~ msgstr "1维形变  "

#~ msgid "Bulk"
#~ msgstr "块体"

#~ msgid "x-axis"
#~ msgstr "x轴"

#~ msgid "y-axis"
#~ msgstr "y轴"

#~ msgid "z-axis"
#~ msgstr "z轴"

#~ msgid "Allow deformation along non-periodic directions."
#~ msgstr "允许沿着非周期性方向形变。"

#~ msgid "Deformation:"
#~ msgstr "形变："

#~ msgid "Maximal scale factor: "
#~ msgstr "最大缩放比例："

#~ msgid "Scale offset: "
#~ msgstr "缩放移位："

#~ msgid "Number of steps: "
#~ msgstr "步数；"

#~ msgid "Only positive deformation"
#~ msgstr "仅允许正变形"

#~ msgid "On   "
#~ msgstr "开"

#~ msgid "Off"
#~ msgstr "关"

#~ msgid "Results:"
#~ msgstr "结果；"

#~ msgid "Keep original configuration"
#~ msgstr "保持原始构型"

#~ msgid "Load optimal configuration"
#~ msgstr "加载优化的构型"

#~ msgid "Load all configurations"
#~ msgstr "加载所有的构型"

#~ msgid "Strain\t\tEnergy [eV]"
#~ msgstr "应力\t\t能量 [eV]"

#~ msgid "Fit:"
#~ msgstr "拟合："

#~ msgid "2nd"
#~ msgstr "第2个"

#~ msgid "3rd"
#~ msgstr "第3个"

#~ msgid "Order of fit: "
#~ msgstr "拟合级数："

#~ msgid "Calculation CANCELLED."
#~ msgstr "计算被取消(!!!)。"

#~ msgid "Calculation completed."
#~ msgstr "计算结束。"

#~ msgid "No trustworthy minimum: Old configuration kept."
#~ msgstr "没有可信任的最小值：原始构型被保留。"

#~ msgid ""
#~ "Insufficent data for a fit\n"
#~ "(only %i data points)\n"
#~ msgstr ""
#~ "需要拟合的数据不够\n"
#~ "(只有 %i 个数据点)\n"

#~ msgid ""
#~ "REVERTING TO 2ND ORDER FIT\n"
#~ "(only 3 data points)\n"
#~ "\n"
#~ msgstr ""
#~ "还原到2阶拟合\n"
#~ "(只有3个数据点)\n"
#~ "\n"

#~ msgid "No minimum found!"
#~ msgstr "未发现最小值！"

#~ msgid ""
#~ "\n"
#~ "WARNING: Minimum is outside interval\n"
#~ msgstr ""
#~ "\n"
#~ "警告：最小值不再区间内\n"

# XXX 在 
#~ msgid "It is UNRELIABLE!\n"
#~ msgstr "不可靠！\n"

#~ msgid "\n"
#~ msgstr "\n"

#~ msgid "No crystal structure data"
#~ msgstr "没有找到晶体结构数据"

#~ msgid "Tip for status box ..."
#~ msgstr "状态盒的建议 ..."

#~ msgid "Clear constraint"
#~ msgstr "清空限制"

#~ msgid ""
#~ "\n"
#~ "An exception occurred!  Please report the issue to\n"
#~ "<EMAIL> - thanks!  Please also report this "
#~ "if\n"
#~ "it was a user error, so that a better error message can be provided\n"
#~ "next time."
#~ msgstr ""
#~ "\n"
#~ "发现异常！ 请提交异常信息到\n"
#~ "<EMAIL> - 谢谢！ 如果这个是用户错误，也请一起"
#~ "提交，\n"
#~ "以方便我们提供更好的错误信息。"

#~ msgid "Max force: %.2f (this frame), %.2f (all frames)"
#~ msgstr "最大的受力: %.2f (这个结构), %.2f (所有结构)"

#~ msgid "Max velocity: %.2f (this frame), %.2f (all frames)"
#~ msgstr "最大速度：%.2f (这个结构), %.2f (所有结构)"

#~ msgid "Max velocity: %.2f."
#~ msgstr "最大速度：%.2f."

#~ msgid "DFT"
#~ msgstr "DFT"

#~ msgid "XC-functional: "
#~ msgstr "交换相关泛函: "

#~ msgid "DFT ..."
#~ msgstr "DFT ..."

#~ msgid "building menus failed: %s"
#~ msgstr "创建菜单失败：%s"

#~ msgid "Dacapo netCDF output file"
#~ msgstr "Dacapo netCDF 输出文件"

#~ msgid "Virtual Nano Lab file"
#~ msgstr "Virtual Nano Lab 文件"

#~ msgid "ASE pickle trajectory"
#~ msgstr "ASE pickle 轨迹"

#~ msgid "ASE bundle trajectory"
#~ msgstr "ASE bundle 轨迹"

#~ msgid "GPAW text output"
#~ msgstr "GPAW 文本输出"

#~ msgid "CUBE file"
#~ msgstr "CUBE 文件"

#~ msgid "XCrySDen Structure File"
#~ msgstr "XCrySDen 结构文件"

#~ msgid "Dacapo text output"
#~ msgstr "Dacapo 文本输出"

#~ msgid "XYZ-file"
#~ msgstr "XYZ-文件"

#~ msgid "VASP POSCAR/CONTCAR file"
#~ msgstr "VASP POSCAR/CONTCAR 文件"

#~ msgid "VASP OUTCAR file"
#~ msgstr "VASP OUTCAR 文件"

#~ msgid "Protein Data Bank"
#~ msgstr "Protein Data Bank"

#~ msgid "CIF-file"
#~ msgstr "CIF-文件"

#~ msgid "FHI-aims geometry file"
#~ msgstr "FHI-aims 构型文件"

#~ msgid "FHI-aims output file"
#~ msgstr "FHI-aims 输出文件"

#~ msgid "TURBOMOLE coord file"
#~ msgstr "TURBOMOLE coord 文件"

#~ msgid "exciting input"
#~ msgstr "exciting 输入"

#~ msgid "WIEN2k structure file"
#~ msgstr "WIEN2k 结构文件"

#~ msgid "DftbPlus input file"
#~ msgstr "DftbPlus 输入文件"

#~ msgid "ETSF format"
#~ msgstr "ETSF 格式"

#~ msgid "CASTEP geom file"
#~ msgstr "CASTEP geom 文件"

#~ msgid "CASTEP output file"
#~ msgstr "CASTEP 输出文件"

#~ msgid "CASTEP trajectory file"
#~ msgstr "CASTEP 轨迹文件"

#~ msgid "DFTBPlus GEN format"
#~ msgstr "DFTBPlus GEN 格式"

#~ msgid "XYZ file"
#~ msgstr "XYZ 文件"

#~ msgid "ASE trajectory"
#~ msgstr "ASE 轨迹"

#~ msgid "PDB file"
#~ msgstr "PDB 文件"

#~ msgid "Gaussian cube file"
#~ msgstr "Gaussian cube 文件"

#~ msgid "Python script"
#~ msgstr "Python 脚本"

#~ msgid "VNL file"
#~ msgstr "VNL 文件"

#~ msgid "Portable Network Graphics"
#~ msgstr "Portable Network Graphics"

#~ msgid "Persistence of Vision"
#~ msgstr "Persistence of Vision"

#~ msgid "Encapsulated PostScript"
#~ msgstr "Encapsulated PostScript"

#~ msgid "FHI-aims geometry input"
#~ msgstr "FHI-aims 结构输入"

#~ msgid "VASP geometry input"
#~ msgstr "VASP 结构输入"

#~ msgid "cif file"
#~ msgstr "cif 文件"

#~ msgid "Save current image only (#%d)"
#~ msgstr "仅保存当前图像 (#%d)"

#~ msgid "Slice: "
#~ msgstr "切片："

#~ msgid "Help for slice ..."
#~ msgstr "切片帮助"

#~ msgid "ase-gui INTERNAL ERROR: strange response in Save,"
#~ msgstr "ase-gui内部错误：对保存命令的响应出错"

#~ msgid "Unknown output format!"
#~ msgstr "未知输入格式"

#~ msgid "Use one of: %s"
#~ msgstr "使用 :%s 之一"

#~ msgid "选择 Calculator"
#~ msgstr "选择计算器"

#~ msgid "Help for plot ..."
#~ msgstr "画图帮助 ..."
