# Basque translations for ase package.
# Copyright (C) 2018-2022 ASE developers
# This file is distributed under the same license as the ase package.
#
# <AUTHOR> <EMAIL>, 2018.
# <AUTHOR> <EMAIL>, 2022.
msgid ""
msgstr ""
"Project-Id-Version: ase\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-10-23 20:47-0400\n"
"PO-Revision-Date: 2024-10-23 21:01-0400\n"
"Last-Translator: U.P. <<EMAIL>>\n"
"Language-Team: Basque\n"
"Language: eu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: ../add.py:10
msgid "(selection)"
msgstr "(hautapena)"

#: ../add.py:16
msgid "Add atoms"
msgstr "Atomoak gehitu"

#: ../add.py:17
msgid "Specify chemical symbol, formula, or filename."
msgstr "Sinbolo kimikoa, formula edo fitxategi izena zehaztu."

#: ../add.py:44
msgid "Add:"
msgstr "Gehitu:"

#: ../add.py:45
msgid "File ..."
msgstr "Fitxategia ..."

#: ../add.py:54
msgid "Coordinates:"
msgstr "Koordenatuak"

#: ../add.py:56
msgid ""
"Coordinates are relative to the center of the selection, if any, else "
"absolute."
msgstr ""
"Koordenatuak hautapenaren zentroarekiko erlatiboak dira, baldin badago, "
"bestela absolutuak."

#: ../add.py:58
msgid "Check positions"
msgstr "Posizioak egiaztatu"

#: ../add.py:59 ../nanoparticle.py:264
msgid "Add"
msgstr "Gehitu"

#. May show UI error
#: ../add.py:104
msgid "Cannot add atoms"
msgstr "Ezin dira atomoak gehitu"

#: ../add.py:105
msgid "{} is neither atom, molecule, nor file"
msgstr "{} ez da ez atomoa, ez molekula, ezta fitxategia ere"

#: ../add.py:143
msgid "Bad positions"
msgstr "Posizio okerrak"

#: ../add.py:144
msgid ""
"Atom would be less than 0.5 Å from an existing atom.  To override, uncheck "
"the check positions option."
msgstr ""
"Atomoa badagoen beste atomo batetik 0.5 Å baino gutxiagora egongo da. Muga "
"hau kentzeko, \"posizioak egiaztatu\" desmarkatu."

#. TRANSLATORS: This is a title of a window.
#: ../celleditor.py:49
msgid "Cell Editor"
msgstr "Gelaxka Editorea"

#: ../celleditor.py:53
msgid "A:"
msgstr "A:"

#: ../celleditor.py:53
msgid "||A||:"
msgstr "||A||:"

#: ../celleditor.py:54 ../celleditor.py:56 ../celleditor.py:58
msgid "periodic:"
msgstr "periodikoa:"

#: ../celleditor.py:55
msgid "B:"
msgstr "B:"

#: ../celleditor.py:55
msgid "||B||:"
msgstr "||B||:"

#: ../celleditor.py:57
msgid "C:"
msgstr "C:"

#: ../celleditor.py:57
msgid "||C||:"
msgstr "||C||:"

#: ../celleditor.py:59
msgid "∠BC:"
msgstr "∠BC:"

#: ../celleditor.py:59
msgid "∠AC:"
msgstr "∠AC:"

#: ../celleditor.py:60
msgid "∠AB:"
msgstr "∠AB:"

#: ../celleditor.py:61
msgid "Scale atoms with cell:"
msgstr "Atomoak gelaxka-unitatearekin eskalatu"

#: ../celleditor.py:62
msgid "Apply Vectors"
msgstr "Bektoreak Aplikatu"

#: ../celleditor.py:63
msgid "Apply Magnitudes"
msgstr "Magnitudeak Aplikatu"

#: ../celleditor.py:64
msgid "Apply Angles"
msgstr "Angeluak Aplikatu"

#: ../celleditor.py:65
msgid ""
"Pressing 〈Enter〉 as you enter values will automatically apply correctly"
msgstr "Balioak sartzerakoan 〈Enter〉 sakatu automatikoki aplikatzeko"

#. TRANSLATORS: verb
#: ../celleditor.py:68
msgid "Center"
msgstr "Zentroa"

#: ../celleditor.py:69
msgid "Wrap"
msgstr "Bildu"

#: ../celleditor.py:70
msgid "Vacuum:"
msgstr "Hutsa"

#: ../celleditor.py:71
msgid "Apply Vacuum"
msgstr "Hutsa Aplikatu"

#: ../colors.py:17
msgid "Colors"
msgstr "Koloreak"

#: ../colors.py:19
msgid "Choose how the atoms are colored:"
msgstr "Atomoak nola koloreztatu hautatu:"

#: ../colors.py:22
msgid "By atomic number, default \"jmol\" colors"
msgstr "Elemenetuaren zenbaki bidez, \"jmol\" koloreak besterik ezean"

#: ../colors.py:23
msgid "By tag"
msgstr "Etiketa bidez"

#: ../colors.py:24
msgid "By force"
msgstr "Indarraren bidez"

#: ../colors.py:25
msgid "By velocity"
msgstr "Abiaduraren bidez"

#: ../colors.py:26
msgid "By initial charge"
msgstr "Hasierako kargaren bidez"

#: ../colors.py:27
msgid "By magnetic moment"
msgstr "Momentu magnetikoaren bidez"

#: ../colors.py:28
msgid "By number of neighbors"
msgstr "Bizilagun kopuruaren bidez"

#: ../colors.py:98
msgid "cmap:"
msgstr "kolore mapa:"

#: ../colors.py:100
msgid "N:"
msgstr "N:"

#. XXX what are optimal allowed range and steps ?
#: ../colors.py:116
msgid "min:"
msgstr "min:"

#: ../colors.py:119
msgid "max:"
msgstr "max:"

#: ../constraints.py:7
msgid "Constraints"
msgstr "Mugak"

#: ../constraints.py:8 ../settings.py:12
msgid "Fix"
msgstr "Finkatu"

#: ../constraints.py:9 ../constraints.py:11
msgid "selected atoms"
msgstr "hautatutako atomoetatik"

#: ../constraints.py:10
msgid "Release"
msgstr "Askatu"

#: ../constraints.py:12 ../settings.py:16
msgid "Clear all constraints"
msgstr "Muga guztiak ezabatu"

#: ../graphs.py:9
msgid ""
"Symbols:\n"
"<c>e</c>: total energy\n"
"<c>epot</c>: potential energy\n"
"<c>ekin</c>: kinetic energy\n"
"<c>fmax</c>: maximum force\n"
"<c>fave</c>: average force\n"
"<c>R[n,0-2]</c>: position of atom number <c>n</c>\n"
"<c>d(n<sub>1</sub>,n<sub>2</sub>)</c>: distance between two atoms "
"<c>n<sub>1</sub></c> and <c>n<sub>2</sub></c>\n"
"<c>i</c>: current image number\n"
"<c>E[i]</c>: energy of image number <c>i</c>\n"
"<c>F[n,0-2]</c>: force on atom number <c>n</c>\n"
"<c>V[n,0-2]</c>: velocity of atom number <c>n</c>\n"
"<c>M[n]</c>: magnetic moment of atom number <c>n</c>\n"
"<c>A[0-2,0-2]</c>: unit-cell basis vectors\n"
"<c>s</c>: path length\n"
"<c>a(n1,n2,n3)</c>: angle between atoms <c>n<sub>1</sub></c>, <c>n<sub>2</"
"sub></c> and <c>n<sub>3</sub></c>, centered on <c>n<sub>2</sub></c>\n"
"<c>dih(n1,n2,n3,n4)</c>: dihedral angle between <c>n<sub>1</sub></c>, "
"<c>n<sub>2</sub></c>, <c>n<sub>3</sub></c> and <c>n<sub>4</sub></c>\n"
"<c>T</c>: temperature (K)"
msgstr ""
"Sinboloak:\n"
"<c>e</c>: energia totala\n"
"<c>epot</c>: energia potentziala\n"
"<c>ekin</c>: energia zinetikoa\n"
"<c>fmax</c>: indar maximoa\n"
"<c>fave</c>: bataz besteko indarra\n"
"<c>R[n,0-2]</c>:  <c>n</c> zenbakidun atomoaren posizioa\n"
"<c>d(n<sub>1</sub>,n<sub>2</sub>)</c>: bi atomoren arteko distantzia "
"<c>n<sub>1</sub></c> eta <c>n<sub>2</sub></c>\n"
"<c>i</c>: momentuko irudi zenbakia\n"
"<c>E[i]</c>: <c>i</c> zenbakiko irudiko energia\n"
"<c>F[n,0-2]</c>: <c>n</c> zenbakidun atomoan indarra\n"
"<c>V[n,0-2]</c>: <c>n</c> zenbakidun atomoaren abiadura\n"
"<c>M[n]</c>: <c>n</c> zenbakidun atomoaren momentu magnetikoa\n"
"<c>A[0-2,0-2]</c>: gelaxka-unitatearen oinarri bektoreak\n"
"<c>s</c>: bidearen luzera\n"
"<c>a(n1,n2,n3)</c>: <c>n<sub>1</sub></c>, <c>n<sub>2</sub></c> eta "
"<c>n<sub>3</sub></c> atomoen arteko angelua, <c>n<sub>2</sub></c>-n "
"zentratua\n"
"<c>dih(n1,n2,n3,n4)</c>: diedro angelua <c>n<sub>1</sub></c>, <c>n<sub>2</"
"sub></c>, <c>n<sub>3</sub></c> eta <c>n<sub>4</sub></c> artean\n"
"<c>T</c>: tenperatura (K)"

#: ../graphs.py:40 ../graphs.py:42
msgid "Plot"
msgstr "Grafikoa"

#: ../graphs.py:44
msgid "Save"
msgstr "Gorde"

#: ../graphs.py:67
msgid "Save data to file ... "
msgstr "Datuak fitxategira gorde ... "

#: ../gui.py:208
msgid "Delete atoms"
msgstr "Atomoak ezabatu"

#: ../gui.py:209
msgid "Delete selected atoms?"
msgstr "Hautatutako atomoak ezabatu?"

#. Subprocess probably crashed
#: ../gui.py:266
msgid "Failure in subprocess"
msgstr "Azpiprozesuan hutsegitea"

#: ../gui.py:273
msgid "Plotting failed"
msgstr "Grafikoan hutsegitea"

#: ../gui.py:280
msgid "Images must have energies and forces, and atoms must not be stationary."
msgstr ""
"Irudiek energiak eta indarrak izan behar dituzte eta atomoek mugitu beharra "
"daukate."

#: ../gui.py:293
msgid "Images must have energies and varying cell."
msgstr "Irudiek energiak eta gelaxka aldakorrak izan behar dituzte."

#: ../gui.py:300
msgid "Requires 3D cell."
msgstr "3D gelaxka eskatzen du."

#: ../gui.py:334
msgid "Quick Info"
msgstr "Informazio azkarra"

#: ../gui.py:471
msgid "_File"
msgstr "_Fitxategia"

#: ../gui.py:472
msgid "_Open"
msgstr "_Ireki"

#: ../gui.py:473
msgid "_New"
msgstr "_Berria"

#: ../gui.py:474
msgid "_Save"
msgstr "_Gorde"

#: ../gui.py:476
msgid "_Quit"
msgstr "I_rten"

#: ../gui.py:478
msgid "_Edit"
msgstr "_Editatu"

#: ../gui.py:479
msgid "Select _all"
msgstr "Hautatu _dena"

#: ../gui.py:480
msgid "_Invert selection"
msgstr "Hautapena _alderantzizkatu"

#: ../gui.py:481
msgid "Select _constrained atoms"
msgstr "_Mugatutako atomoak hautatu"

#: ../gui.py:482
msgid "Select _immobile atoms"
msgstr "Atomo _mugiezinak hautatu"

#. M('---'),
#: ../gui.py:484
msgid "_Cut"
msgstr "_Ebaki"

#: ../gui.py:485
msgid "_Copy"
msgstr "_Kopiatu"

#: ../gui.py:486
msgid "_Paste"
msgstr "_Itsatsi"

#: ../gui.py:488
msgid "Hide selected atoms"
msgstr "Hautatutako atomoak ezkutatu"

#: ../gui.py:489
msgid "Show selected atoms"
msgstr "Hautatutako atomoak erakutsi"

#: ../gui.py:491
msgid "_Modify"
msgstr "_Aldatu"

#: ../gui.py:492
msgid "_Add atoms"
msgstr "Atomoak _gehitu"

#: ../gui.py:493
msgid "_Delete selected atoms"
msgstr "Hautatutako atomoak _ezabatu"

#: ../gui.py:495
msgid "Edit _cell"
msgstr "_Gelaxka editatu"

#: ../gui.py:497
msgid "_First image"
msgstr "_Lehenengo irudia"

#: ../gui.py:498
msgid "_Previous image"
msgstr "A_urreko irudia"

#: ../gui.py:499
msgid "_Next image"
msgstr "_Hurrengo irudia"

#: ../gui.py:500
msgid "_Last image"
msgstr "A_zken irudia"

#: ../gui.py:501
msgid "Append image copy"
msgstr "Irudiaren kopia esleitu"

#: ../gui.py:503
msgid "_View"
msgstr "_Ikusi"

#: ../gui.py:504
msgid "Show _unit cell"
msgstr "_Gelaxka-unitatea erakutsi"

#: ../gui.py:506
msgid "Show _axes"
msgstr "_Ardatzak erakutsi"

#: ../gui.py:508
msgid "Show _bonds"
msgstr "_Loturak erakutsi"

#: ../gui.py:510
msgid "Show _velocities"
msgstr "A_biadurak erakutsi"

#: ../gui.py:512
msgid "Show _forces"
msgstr "_Indarrak erakutsi"

#: ../gui.py:514
msgid "Show _Labels"
msgstr "_Etiketak erakutsi"

#: ../gui.py:515
msgid "_None"
msgstr "_Bat ere ez"

#: ../gui.py:516
msgid "Atom _Index"
msgstr "Atomo-_indizeak"

#: ../gui.py:517
msgid "_Magnetic Moments"
msgstr "_Momentu Magnetikoa"

#. XXX check if exist
#: ../gui.py:518
msgid "_Element Symbol"
msgstr "_Elementuen Ikurra"

#: ../gui.py:519
msgid "_Initial Charges"
msgstr "_Hasierako Kargak"

#: ../gui.py:522
msgid "Quick Info ..."
msgstr "Informazio azkarra ..."

#: ../gui.py:523
msgid "Repeat ..."
msgstr "Errepikatu ..."

#: ../gui.py:524
msgid "Rotate ..."
msgstr "Biratu ..."

#: ../gui.py:525
msgid "Colors ..."
msgstr "Koloreak ..."

#. TRANSLATORS: verb
#: ../gui.py:527
msgid "Focus"
msgstr "Fokatu"

#: ../gui.py:528
msgid "Zoom in"
msgstr "Gerturatu"

#: ../gui.py:529
msgid "Zoom out"
msgstr "Urrutiratu"

#: ../gui.py:530
msgid "Change View"
msgstr "Ikuspegia aldatu"

#: ../gui.py:532
msgid "Reset View"
msgstr "Ikuspegia berrezarri"

#: ../gui.py:533
msgid "xy-plane"
msgstr "xy-planoa"

#: ../gui.py:534
msgid "yz-plane"
msgstr "yz-planoa"

#: ../gui.py:535
msgid "zx-plane"
msgstr "zx-planoa"

#: ../gui.py:536
msgid "yx-plane"
msgstr "yx-planoa"

#: ../gui.py:537
msgid "zy-plane"
msgstr "zy-planoa"

#: ../gui.py:538
msgid "xz-plane"
msgstr "xz-planoa"

#: ../gui.py:539
msgid "a2,a3-plane"
msgstr "a2,a3-planoa"

#: ../gui.py:540
msgid "a3,a1-plane"
msgstr "a3,a1-planoa"

#: ../gui.py:541
msgid "a1,a2-plane"
msgstr "a1,a2-planoa"

#: ../gui.py:542
msgid "a3,a2-plane"
msgstr "a3,a2-planoa"

#: ../gui.py:543
msgid "a1,a3-plane"
msgstr "a1,a3-planoa"

#: ../gui.py:544
msgid "a2,a1-plane"
msgstr "a2,a1-planoa"

#: ../gui.py:545
msgid "Settings ..."
msgstr "Ezarpenak ..."

#: ../gui.py:547
msgid "VMD"
msgstr "VMD"

#: ../gui.py:548
msgid "RasMol"
msgstr "RasMol"

#: ../gui.py:549
msgid "xmakemol"
msgstr "xmakemol"

#: ../gui.py:550
msgid "avogadro"
msgstr "avogadro"

#: ../gui.py:552
msgid "_Tools"
msgstr "_Tresnak"

#: ../gui.py:553
msgid "Graphs ..."
msgstr "Grafikoak ..."

#: ../gui.py:554
msgid "Movie ..."
msgstr "Bideoa ..."

#: ../gui.py:555
msgid "Constraints ..."
msgstr "Mugak ..."

#: ../gui.py:556
msgid "Render scene ..."
msgstr "Eszena errendatu ..."

#: ../gui.py:557
msgid "_Move selected atoms"
msgstr "Hautatutako atomoak mugitu"

#: ../gui.py:558
msgid "_Rotate selected atoms"
msgstr "Hautatutako atomoak _biratu"

#: ../gui.py:560
msgid "NE_B plot"
msgstr "NE_B grafikoa"

#: ../gui.py:561
msgid "B_ulk Modulus"
msgstr "Bolumen-modulua"

#: ../gui.py:562
msgid "Reciprocal space ..."
msgstr "Espazio erreziprokoa ..."

#. TRANSLATORS: Set up (i.e. build) surfaces, nanoparticles, ...
#: ../gui.py:565
msgid "_Setup"
msgstr "_Konfiguratu"

#: ../gui.py:566
msgid "_Surface slab"
msgstr "_Gainazal lauza"

#: ../gui.py:567
msgid "_Nanoparticle"
msgstr "_Nanopartikula"

#: ../gui.py:569
msgid "Nano_tube"
msgstr "Nano_tuboa"

#. (_('_Calculate'),
#. [M(_('Set _Calculator'), self.calculator_window, disabled=True),
#. M(_('_Energy and Forces'), self.energy_window, disabled=True),
#. M(_('Energy Minimization'), self.energy_minimize_window,
#. disabled=True)]),
#: ../gui.py:577
msgid "_Help"
msgstr "_Laguntza"

#: ../gui.py:578
msgid "_About"
msgstr "_Honi buruz"

#: ../gui.py:582
msgid "Webpage ..."
msgstr "Web orria ..."

#. Host window will never be shown
#: ../images.py:259
msgid "Constraints discarded"
msgstr "Mugak baztertuta"

#: ../images.py:260
msgid "Constraints other than FixAtoms have been discarded."
msgstr "Finkatutako atomoak ez beste mugak baztertu egin dira."

#: ../modify.py:20
msgid "No atoms selected!"
msgstr "Ez da atomorik hautatu!"

#: ../modify.py:23
msgid "Modify"
msgstr "Moldatu"

# Elementua aldatu
#: ../modify.py:26
msgid "Change element"
msgstr "Elementua aldatu"

#: ../modify.py:29
msgid "Tag"
msgstr "Etiketa"

#: ../modify.py:31
msgid "Moment"
msgstr "Momentua"

#: ../movie.py:10
msgid "Movie"
msgstr "Bideoa"

#: ../movie.py:11
msgid "Image number:"
msgstr "Irudi zenbakia:"

#: ../movie.py:17
msgid "First"
msgstr "Lehenengoa"

#: ../movie.py:18
msgid "Back"
msgstr "Atzera"

#: ../movie.py:19
msgid "Forward"
msgstr "Aurrera"

#: ../movie.py:20
msgid "Last"
msgstr "Azkena"

#: ../movie.py:22
msgid "Play"
msgstr "Erreproduzitu"

#: ../movie.py:23
msgid "Stop"
msgstr "Hasi"

#. TRANSLATORS: This function plays an animation forwards and backwards
#. alternatingly, e.g. for displaying vibrational movement
#: ../movie.py:27
msgid "Rock"
msgstr "Fotograma errepikatu"

#: ../movie.py:40
msgid " Frame rate: "
msgstr "Fotograma segundoko: "

#: ../movie.py:40
msgid " Skip frames: "
msgstr "Fotograma saltatu: "

#. Delayed imports:
#. ase.cluster.data
#: ../nanoparticle.py:20
msgid ""
"Create a nanoparticle either by specifying the number of layers, or using "
"the\n"
"Wulff construction.  Please press the [Help] button for instructions on how "
"to\n"
"specify the directions.\n"
"WARNING: The Wulff construction currently only works with cubic crystals!\n"
msgstr ""
"Nanopartikula bat sortu geruza kopurua zehaztuz edo Wulff eraikuntza\n"
"erabiliz. [Laguntza] botoia sakatu norantza\n"
"nola zehaztu jakiteko.\n"
"ADI: Wulff eraikuntzak kristal kubikoekin bakarrik funtzionatzen du.\n"

#: ../nanoparticle.py:27
#, python-brace-format
msgid ""
"\n"
"The nanoparticle module sets up a nano-particle or a cluster with a given\n"
"crystal structure.\n"
"\n"
"1) Select the element, the crystal structure and the lattice constant(s).\n"
"   The [Get structure] button will find the data for a given element.\n"
"\n"
"2) Choose if you want to specify the number of layers in each direction, or "
"if\n"
"   you want to use the Wulff construction.  In the latter case, you must\n"
"   specify surface energies in each direction, and the size of the cluster.\n"
"\n"
"How to specify the directions:\n"
"------------------------------\n"
"\n"
"First time a direction appears, it is interpreted as the entire family of\n"
"directions, i.e. (0,0,1) also covers (1,0,0), (-1,0,0) etc.  If one of "
"these\n"
"directions is specified again, the second specification overrules that "
"specific\n"
"direction.  For this reason, the order matters and you can rearrange the\n"
"directions with the [Up] and [Down] keys.  You can also add a new "
"direction,\n"
"remember to press [Add] or it will not be included.\n"
"\n"
"Example: (1,0,0) (1,1,1), (0,0,1) would specify the {100} family of "
"directions,\n"
"the {111} family and then the (001) direction, overruling the value given "
"for\n"
"the whole family of directions.\n"
msgstr ""
"\n"
"Nanopartikula moduluak nanopartikula bat edo multzo bat (cluster) sortuko du "
"ezarritako kristal egiturarekin.\n"
"\n"
"1) Elementua, kristal egitura eta sareta konstantea(k) hautatu.\n"
"   [Egitura lortu] botoiak ezarritako elementuarentzako datuak eskuratuko "
"ditu.\n"
"\n"
"2) Hautatu geruza kopurua norantza bakoitzean, edo Wulff eraikuntzaren "
"artean.\n"
"   Kasu honetan, gainazaleko energia zehaztu norantza bakoitzean, eta "
"multzoaren tamaina.\n"
"\n"
"Norantzak nola zehaztu:\n"
"-----------------------\n"
"\n"
"Norantza bat lehenengo aldiz azaltzen denean, norantza guztiak bezala\n"
"hartuko da, hau da (0,0,1) norantzak bere gain hartzen ditu (1,0,0),\n"
"(-1,0,0) ea. Norantza horietakoren bat berriro zehazten bada, bigarren\n"
"zehaztapen honek lehenengoa gainidatziko du. Horregatik, ordenak\n"
"garrantzia du eta berrordenatu ditzakezu [Gora] eta [Behera]\n"
"botoiekin. Beste norantza bat ere gehitu dezakezu, gogoratu [Gehitu]\n"
"sakatzea kontuan izateko.\n"
"\n"
"Adibidez: (1,0,0) (1,1,1), (0,0,1) norantzek {100} norantza familia\n"
"ezarriko du, {111} familia eta ondoren (001) norantza, familia guztiko\n"
"norantzak gainidatziz.\n"

#: ../nanoparticle.py:87
msgid "Face centered cubic (fcc)"
msgstr "Aurpegira zentratutako kubikoak (fcc)"

#: ../nanoparticle.py:88
msgid "Body centered cubic (bcc)"
msgstr "Gorpura zentratutako kubikoa (bcc)"

#: ../nanoparticle.py:89
msgid "Simple cubic (sc)"
msgstr "Kubiko sinplea (sc)"

#: ../nanoparticle.py:90
msgid "Hexagonal closed-packed (hcp)"
msgstr "Paketatu itxiko hexagonola (hcp)"

#: ../nanoparticle.py:91
msgid "Graphite"
msgstr "Grafitoa"

#: ../nanoparticle.py:136
msgid "Nanoparticle"
msgstr "Nanopartikula"

#: ../nanoparticle.py:140
msgid "Get structure"
msgstr "Egitura lortu"

#: ../nanoparticle.py:155 ../surfaceslab.py:68
msgid "Structure:"
msgstr "Egitura:"

#: ../nanoparticle.py:159
msgid "Lattice constant:  a ="
msgstr "Sareta konstatea:  a ="

#: ../nanoparticle.py:163
msgid "Layer specification"
msgstr "Geruza zehaztapena"

#: ../nanoparticle.py:163
msgid "Wulff construction"
msgstr "Wulff eraikuntza"

#: ../nanoparticle.py:166
msgid "Method: "
msgstr "Metodoa: "

#: ../nanoparticle.py:174
msgid "Add new direction:"
msgstr "Norantza berria gehitu"

#. Information
#: ../nanoparticle.py:180
msgid "Information about the created cluster:"
msgstr "Sortutako multzoaren (cluster) inguruko informazioa"

#: ../nanoparticle.py:181
msgid "Number of atoms: "
msgstr "Atomo kopurua: "

#: ../nanoparticle.py:183
msgid "   Approx. diameter: "
msgstr "   Diametroa gutxi gorabehera: "

#: ../nanoparticle.py:192
msgid "Automatic Apply"
msgstr "Automatikoki Aplikatu"

#: ../nanoparticle.py:195 ../nanotube.py:49
msgid "Creating a nanoparticle."
msgstr "Nanopartikula sortzen."

#: ../nanoparticle.py:197 ../nanotube.py:50 ../surfaceslab.py:81
msgid "Apply"
msgstr "Aplikatu"

#: ../nanoparticle.py:198 ../nanotube.py:51 ../surfaceslab.py:82
msgid "OK"
msgstr "Ados"

#: ../nanoparticle.py:227
msgid "Up"
msgstr "Gora"

#: ../nanoparticle.py:228
msgid "Down"
msgstr "Behera"

#: ../nanoparticle.py:229
msgid "Delete"
msgstr "Ezabatu"

#: ../nanoparticle.py:271
msgid "Number of atoms"
msgstr "Atomo kopurua"

#: ../nanoparticle.py:271
msgid "Diameter"
msgstr "Diametroa"

#: ../nanoparticle.py:279
msgid "above  "
msgstr "gainean"

#: ../nanoparticle.py:279
msgid "below  "
msgstr "azpian"

#: ../nanoparticle.py:279
msgid "closest  "
msgstr "gertuena"

#: ../nanoparticle.py:282
msgid "Smaller"
msgstr "Txikiena"

#: ../nanoparticle.py:283
msgid "Larger"
msgstr "Handiena"

#: ../nanoparticle.py:284
msgid "Choose size using:"
msgstr "Hautatu tamaina erabiliz:"

#: ../nanoparticle.py:286
msgid "atoms"
msgstr "atomoak"

#: ../nanoparticle.py:287
msgid "Å³"
msgstr "Å³"

#: ../nanoparticle.py:289
msgid "Rounding: If exact size is not possible, choose the size:"
msgstr "Biribildu: Tamaina zehatza ezinezkoa bada, tamaina hautatu:"

#: ../nanoparticle.py:317
msgid "Surface energies (as energy/area, NOT per atom):"
msgstr "Gainazaleko energia (energia/area, EZ da atomokoa):"

#: ../nanoparticle.py:319
msgid "Number of layers:"
msgstr "Geruza kopurua"

#: ../nanoparticle.py:347
msgid "At least one index must be non-zero"
msgstr "Gutxienez indize batek ze du zero izan behar"

#: ../nanoparticle.py:350
msgid "Invalid hexagonal indices"
msgstr "Indize hexagonal baliogabeak"

#: ../nanoparticle.py:415
msgid "Unsupported or unknown structure"
msgstr "Egitura ezezaguna edo onartu gabekoa"

#: ../nanoparticle.py:416
#, python-brace-format
msgid "Element = {0}, structure = {1}"
msgstr "Elementua = {0}, egitura = {1}"

#: ../nanoparticle.py:530 ../nanotube.py:82 ../surfaceslab.py:221
msgid "No valid atoms."
msgstr "Atomo baliogabeak."

#: ../nanoparticle.py:531 ../nanotube.py:83 ../surfaceslab.py:222
#: ../widgets.py:93
msgid "You have not (yet) specified a consistent set of parameters."
msgstr "Ez duzu (oraindik) parametro multzo zuzena zehaztu."

#: ../nanotube.py:10
msgid ""
"Set up a Carbon nanotube by specifying the (n,m) roll-up vector.\n"
"Please note that m <= n.\n"
"\n"
"Nanotubes of other elements can be made by specifying the element\n"
"and bond length."
msgstr ""
"Karbonozko nanotuboa sortu, (n,m) biribilketa bektorea zehaztuz.\n"
"Kontuan izan m <= n izan behar dela.\n"
"\n"
"Beste elementutako nanotuboak sortzeko elementua eta lortura luzera zehaztu."

#: ../nanotube.py:23
#, python-brace-format
msgid ""
"{natoms} atoms, diameter: {diameter:.3f} Å, total length: {total_length:.3f} "
"Å"
msgstr ""
"{natoms} atomo, diametroa: {diameter:.3f} Å, luzera totala: "
"{total_length:.3f} Å"

#: ../nanotube.py:38
msgid "Nanotube"
msgstr "Nanotuboa"

#: ../nanotube.py:41
msgid "Bond length: "
msgstr "Lotura luzera: "

#: ../nanotube.py:43
msgid "Å"
msgstr "Å"

#: ../nanotube.py:44
msgid "Select roll-up vector (n,m) and tube length:"
msgstr "Biribilketa bektorea (n,m) eta tubo luzera hautatu:"

#: ../nanotube.py:47
msgid "Length:"
msgstr "Luzera:"

#: ../quickinfo.py:28
msgid "This frame has no atoms."
msgstr "Fotograma honek ez du atomorik."

#: ../quickinfo.py:33
msgid "Single image loaded."
msgstr "Irudi bakarra kargatuta."

#: ../quickinfo.py:35
msgid "Image {} loaded (0–{})."
msgstr "{} irudia kargatuta (0–{})"

#: ../quickinfo.py:37
msgid "Number of atoms: {}"
msgstr "Atomo kopurua: {}"

#: ../quickinfo.py:47
msgid "Unit cell [Å]:"
msgstr "Unitate-gelaxka [Å]:"

#: ../quickinfo.py:49
msgid "no"
msgstr "ez"

#: ../quickinfo.py:49
msgid "yes"
msgstr "bai"

#. TRANSLATORS: This has the form Periodic: no, no, yes
#: ../quickinfo.py:52
msgid "Periodic: {}, {}, {}"
msgstr "Periodikoa: {}, {}, {}"

#: ../quickinfo.py:57
msgid "Lengths [Å]: {:.3f}, {:.3f}, {:.3f}"
msgstr "Luzerak [Å]: {:.3f}, {:.3f}, {:.3f}"

#: ../quickinfo.py:58
msgid "Angles: {:.1f}°, {:.1f}°, {:.1f}°"
msgstr "Angeluak: {:.1f}°, {:.1f}°, {:.1f}°"

#: ../quickinfo.py:61
msgid "Volume: {:.3f} Å³"
msgstr "Bolumena: {:.3f} Å³"

#: ../quickinfo.py:67
msgid "Unit cell is fixed."
msgstr "Unitate-gelaxka finkoa."

#: ../quickinfo.py:69
msgid "Unit cell varies."
msgstr "Unitate-gelaxka aldakorra."

#: ../quickinfo.py:75
msgid "Could not recognize the lattice type"
msgstr "Ezin da Bravais-sarea identifikatu"

#: ../quickinfo.py:77
msgid "Unexpected error determining lattice type"
msgstr "Bravais-sarearen determinazioan ezusteko errorea"

#: ../quickinfo.py:79
msgid ""
"Reduced Bravais lattice:\n"
"{}"
msgstr ""
"Bravais-sare murriztua:\n"
"{}"

#: ../quickinfo.py:107
msgid "Calculator: {} (cached)"
msgstr "Kalkulagailua: {} (gordetakoa)"

#: ../quickinfo.py:109
msgid "Calculator: {} (attached)"
msgstr "Kalkulagailua: {} (atxikitakoa)"

#: ../quickinfo.py:116
msgid "Energy: {:.3f} eV"
msgstr "Energia: {:.3f} eV"

#: ../quickinfo.py:121
msgid "Max force: {:.3f} eV/Å"
msgstr "Indar maximoa: {:.3f} eV/Å"

#: ../quickinfo.py:125
msgid "Magmom: {:.3f} µ"
msgstr "Momentu magnetikoa: {:.3f} µ"

#: ../render.py:20
msgid "Render current view in povray ... "
msgstr "Uneko ikuspegia povray-rekin errendatu ... "

#: ../render.py:21
#, python-format
msgid "Rendering %d atoms."
msgstr "%d atomo errendatzen."

#: ../render.py:26
msgid "Size"
msgstr "Tamaina"

#: ../render.py:31
msgid "Line width"
msgstr "Lerro zabalera"

#: ../render.py:32
msgid "Ångström"
msgstr "Ångström"

#: ../render.py:34
msgid "Render constraints"
msgstr "Mugak errendatu"

#: ../render.py:35
msgid "Render unit cell"
msgstr "Gelaxka-unitatea errendatu"

#: ../render.py:41
msgid "Output basename: "
msgstr "Irteerako oinarri-izena: "

#: ../render.py:43
msgid "POVRAY executable"
msgstr "POVRAY exekutagarria"

#: ../render.py:45
msgid "Output filename: "
msgstr "Irteerako fitxategi-izena: "

#: ../render.py:50
msgid "Atomic texture set:"
msgstr "Testura atomikoen sorta:"

#: ../render.py:57
msgid "Camera type: "
msgstr "Kamera mota: "

#: ../render.py:58
msgid "Camera distance"
msgstr "Kamera distantzia"

#. render current frame/all frames
#: ../render.py:61
msgid "Render current frame"
msgstr "Uneko fotograma errendatu"

#: ../render.py:62
msgid "Render all frames"
msgstr "Fotograma guztiak errendatu"

#: ../render.py:67
msgid "Run povray"
msgstr "povray exekutatu"

#: ../render.py:68
msgid "Keep povray files"
msgstr "povray fitxategiak mantendu"

#: ../render.py:69
msgid "Show output window"
msgstr "Irteera leihoa erakutsi"

#: ../render.py:70
msgid "Transparent background"
msgstr "Atzeko plano gardena"

#: ../render.py:74
msgid "Render"
msgstr "Errendatu"

#: ../repeat.py:7
msgid "Repeat"
msgstr "Errepikatu"

#: ../repeat.py:8
msgid "Repeat atoms:"
msgstr "Errepikatu atomoak"

#: ../repeat.py:12
msgid "Set unit cell"
msgstr "Gelaxka-unitatea ezarri"

#: ../rotate.py:11
msgid "Rotate"
msgstr "Biratu"

#: ../rotate.py:12
msgid "Rotation angles:"
msgstr "Biraketa angeluak:"

#: ../rotate.py:16
msgid "Update"
msgstr "Eguneratu"

#: ../rotate.py:17
msgid ""
"Note:\n"
"You can rotate freely\n"
"with the mouse, by holding\n"
"down mouse button 2."
msgstr ""
"Oharra:\n"
"Sagurarekin libreki biratu\n"
"dezakezu, 2. botoia\n"
"sakatuz."

#: ../save.py:15
msgid ""
"Append name with \"@n\" in order to write image\n"
"number \"n\" instead of the current image. Append\n"
"\"@start:stop\" or \"@start:stop:step\" if you want\n"
"to write a range of images. You can leave out\n"
"\"start\" and \"stop\" so that \"name@:\" will give\n"
"you all images. Negative numbers count from the\n"
"last image. Examples: \"name@-1\": last image,\n"
"\"name@-2:\": last two."
msgstr ""
"Une honetako irudiaren ordez n-garren irudia gordetzeko, esleitu \"@n\"\n"
"izenari. Irudi multzo bat gordetzeko, esleitu \"@lehena:azkena\" edo\n"
"\"@lehena:azkena:pausoa\". \"lehena\" eta \"azkena\" kanpoan utz ditzakezu,\n"
"hartara \"izena@:\"-ek irudi guztiak emango dizkizu. Zenbaki negatiboen\n"
"bidez azken iruditik atzerantz zenbatu dezakezu. Adibideak:\n"
"\"izena@-1:\" azken irudia, \"izena@-2:\" azken bi irudiak."

#: ../save.py:27
msgid "Save ..."
msgstr "Gorde ..."

#: ../save.py:85 ../ui.py:32
msgid "Error"
msgstr "Errorea"

#: ../settings.py:8
msgid "Settings"
msgstr "Hobespenak"

#. Constraints
#: ../settings.py:11
msgid "Constraints:"
msgstr "Mugak:"

#: ../settings.py:14
msgid "release"
msgstr "askatu"

#: ../settings.py:15 ../settings.py:23
msgid " selected atoms"
msgstr " hautatutako atomoak"

#. Visibility
#: ../settings.py:19
msgid "Visibility:"
msgstr "Ikusgarritasuna:"

#: ../settings.py:20
msgid "Hide"
msgstr "Ezkutatu"

#: ../settings.py:22
msgid "show"
msgstr "erakutsi"

#: ../settings.py:24
msgid "View all atoms"
msgstr "Atomo guztiak ikusi"

#. Miscellaneous
#: ../settings.py:27
msgid "Miscellaneous:"
msgstr "Askotarikoa:"

#: ../settings.py:30
msgid "Scale atomic radii:"
msgstr "Atomo erreadioaren eskala:"

#: ../settings.py:37
msgid "Scale force vectors:"
msgstr "Indar bektoreen eskala:"

#: ../settings.py:44
msgid "Scale velocity vectors:"
msgstr "Abiadura bektoreen eskala:"

#: ../status.py:80
#, python-format
msgid " tag=%(tag)s"
msgstr " tag=%(tag)s"

#. TRANSLATORS: mom refers to magnetic moment
#: ../status.py:84
msgid " mom={:1.2f}"
msgstr " mom={:1.2f}"

#: ../status.py:88
msgid " q={:1.2f}"
msgstr " q={:1.2f}"

#: ../status.py:126
msgid "dihedral"
msgstr "diedro"

#: ../surfaceslab.py:9
msgid ""
"  Use this dialog to create surface slabs.  Select the element by\n"
"writing the chemical symbol or the atomic number in the box.  Then\n"
"select the desired surface structure.  Note that some structures can\n"
"be created with an othogonal or a non-orthogonal unit cell, in these\n"
"cases the non-orthogonal unit cell will contain fewer atoms.\n"
"\n"
"  If the structure matches the experimental crystal structure, you can\n"
"look up the lattice constant, otherwise you have to specify it\n"
"yourself."
msgstr ""
"  Gainazalak sortzeko elkarrizketa kuadro hau erabili. Elementua sinbolo\n"
"kimikoa edo zenbaki atomikoa idatziz hautatu. Ondoren, gainazal\n"
"egitura hautatu. Gogoan izan egitura batzuk gelaxka-unitate\n"
"ortogonalarekin edo ez-ortogonalarekin sortu daitezkeela, azken kasu\n"
"horretan, gelaxka-unitate ez-ortogonalak atomo gutxiago edukiko ditu.\n"
"\n"
"  Sortutako egiturak kristal egitura experimentalarekin bat egiten\n"
"badu, sareta konstantea eskuratu dezakezu, bestela, eskuz zehaztu\n"
"beharko duzu."

#. Name, structure, orthogonal, function
#: ../surfaceslab.py:21
msgid "FCC(100)"
msgstr "FCC(100)"

#: ../surfaceslab.py:21 ../surfaceslab.py:22 ../surfaceslab.py:23
#: ../surfaceslab.py:24
msgid "fcc"
msgstr "fcc"

#: ../surfaceslab.py:22
msgid "FCC(110)"
msgstr "FCC(110)"

#: ../surfaceslab.py:23 ../surfaceslab.py:171
msgid "FCC(111)"
msgstr "FCC(111)"

#: ../surfaceslab.py:24 ../surfaceslab.py:174
msgid "FCC(211)"
msgstr "FCC(211)"

#: ../surfaceslab.py:25
msgid "BCC(100)"
msgstr "BCC(100)"

#: ../surfaceslab.py:25 ../surfaceslab.py:26 ../surfaceslab.py:27
msgid "bcc"
msgstr "bcc"

#: ../surfaceslab.py:26 ../surfaceslab.py:168
msgid "BCC(110)"
msgstr "BCC(110)"

#: ../surfaceslab.py:27 ../surfaceslab.py:165
msgid "BCC(111)"
msgstr "BCC(111)"

#: ../surfaceslab.py:28 ../surfaceslab.py:178
msgid "HCP(0001)"
msgstr "HCP(0001)"

#: ../surfaceslab.py:28 ../surfaceslab.py:29 ../surfaceslab.py:132
#: ../surfaceslab.py:188
msgid "hcp"
msgstr "hcp"

#: ../surfaceslab.py:29 ../surfaceslab.py:181
msgid "HCP(10-10)"
msgstr "HCP(10-10)"

#: ../surfaceslab.py:30
msgid "DIAMOND(100)"
msgstr "DIAMANTEA(100)"

#: ../surfaceslab.py:30 ../surfaceslab.py:31
msgid "diamond"
msgstr "diamantea"

#: ../surfaceslab.py:31
msgid "DIAMOND(111)"
msgstr "DIAMANTEA(111)"

#: ../surfaceslab.py:53
msgid "Get from database"
msgstr "Datu basetik eskuratu"

#: ../surfaceslab.py:65
msgid "Surface"
msgstr "Gainazala"

#: ../surfaceslab.py:69
msgid "Orthogonal cell:"
msgstr "Gelaxka ortogonala:"

#: ../surfaceslab.py:70
msgid "Lattice constant:"
msgstr "Sareta konstantea"

#: ../surfaceslab.py:71
msgid "\ta"
msgstr "\ta"

#: ../surfaceslab.py:72
msgid "\tc"
msgstr "\tc"

#: ../surfaceslab.py:73
msgid "Size:"
msgstr "Tamaina:"

#: ../surfaceslab.py:74
msgid "\tx: "
msgstr "\tx: "

#: ../surfaceslab.py:74 ../surfaceslab.py:75 ../surfaceslab.py:76
msgid " unit cells"
msgstr " gelaxka-unite"

#: ../surfaceslab.py:75
msgid "\ty: "
msgstr "\ty: "

#: ../surfaceslab.py:76
msgid "\tz: "
msgstr "\tz: "

#: ../surfaceslab.py:77
msgid "Vacuum: "
msgstr "Hutsa: "

#. TRANSLATORS: This is a title of a window.
#: ../surfaceslab.py:80
msgid "Creating a surface."
msgstr "Gainazala sortuz."

#. TRANSLATORS: E.g. "... assume fcc crystal structure for Au"
#: ../surfaceslab.py:108
msgid "Error: Reference values assume {} crystal structure for {}!"
msgstr ""
"Errorea: Erreferentziazko balioek {} kristal egitura espero dute {} "
"egiturarako!"

#: ../surfaceslab.py:162
msgid "Please enter an even value for orthogonal cell"
msgstr "Gelaxka ortogonaletarako sartu balio bikoitia"

#: ../surfaceslab.py:175
msgid "Please enter a value divisible by 3 for orthogonal cell"
msgstr "Gelaxka ortogonaletarako sartu 3rekin zatigarria den balioa"

#: ../surfaceslab.py:195
msgid " Vacuum: {} Å."
msgstr " Hutsa: {} Å."

#. TRANSLATORS: e.g. "Au fcc100 surface with 2 atoms."
#. or "Au fcc100 surface with 2 atoms. Vacuum: 5 Å."
#: ../surfaceslab.py:203
#, python-brace-format
msgid "{symbol} {surf} surface with one atom.{vacuum}"
msgid_plural "{symbol} {surf} surface with {natoms} atoms.{vacuum}"
msgstr[0] "{symbol} {surf} gainazala atomo batekin.{vacuum}"
msgstr[1] "{symbol} {surf} gainazala {natoms} atomorekin.{vacuum}"

#: ../ui.py:39
msgid "Version"
msgstr "Bertsioa"

#: ../ui.py:40
msgid "Web-page"
msgstr "Web orria"

#: ../ui.py:41
msgid "About"
msgstr "Honi buruz"

#: ../ui.py:47 ../ui.py:51 ../widgets.py:12
msgid "Help"
msgstr "Laguntza"

#: ../ui.py:560
msgid "Open ..."
msgstr "Ireki ..."

#: ../ui.py:567
msgid "Automatic"
msgstr "Automatikoa"

#: ../ui.py:585
msgid "Choose parser:"
msgstr "Parserra hautatu:"

#: ../ui.py:591
msgid "Read error"
msgstr "Irakurketa errorea"

#: ../ui.py:592
#, python-brace-format
msgid "Could not read {filename}: {err}"
msgstr "{filename} ezin da irakurri: {err}"

#: ../view.py:130
msgid "one image loaded"
msgid_plural "{} images loaded"
msgstr[0] "irudi bakarra kargatuta"
msgstr[1] "{} irudi kargatuta"

#: ../widgets.py:10
msgid "Element:"
msgstr "Elementua:"

#: ../widgets.py:24
msgid "Enter a chemical symbol or the atomic number."
msgstr "Sinbolo kimikoa edo zenbaki atomikoa sartu."

#. Title of a popup window
#: ../widgets.py:26
msgid "Info"
msgstr "Info"

#: ../widgets.py:56
msgid "No element specified!"
msgstr "Ez da elementurik zehaztu!"

#: ../widgets.py:75
msgid "ERROR: Invalid element!"
msgstr "ERROREA: Baliogabeko elementua!"

#: ../widgets.py:92
msgid "No Python code"
msgstr "Ez dago Python koderik"

#~ msgid "Get molecule:"
#~ msgstr "Molekula eskuratu:"

#~ msgid "Yellow"
#~ msgstr "Horia"

#~ msgid "Constrain"
#~ msgstr "Mugatu"

#~ msgid "immobile atoms"
#~ msgstr "atomo mugiezinak"

#~ msgid "Unconstrain"
#~ msgstr "Mugak ezabatu"

#~ msgid "Clear constraints"
#~ msgstr "Mugak kendu"

#~ msgid ""
#~ "Set up a graphene sheet or a graphene nanoribbon.  A nanoribbon may\n"
#~ "optionally be saturated with hydrogen (or another element)."
#~ msgstr ""
#~ "Grafenozko izara edo nanozinta prestatu. Nanozinta, aukeran, "
#~ "hidrogenoarekin (edo beste edozein elementu) saturatu daiteke."

#~ msgid " %(natoms)i atoms: %(symbols)s, Volume: %(volume).3f A<sup>3</sup>"
#~ msgstr ""
#~ " %(natoms)i atomo: %(symbols)s, Bolumena: %(volume).3f A<sup>3</sup>"

#~ msgid "Graphene"
#~ msgstr "Grafenoa"

#~ msgid "Structure: "
#~ msgstr "Egitura:"

#~ msgid "Infinite sheet"
#~ msgstr "Izara infinitoa"

#~ msgid "Unsaturated ribbon"
#~ msgstr "Saturatu gabeko zinta"

#~ msgid "Saturated ribbon"
#~ msgstr "Zinta saturatua"

#~ msgid "Orientation: "
#~ msgstr "Orientazioa: "

#~ msgid "zigzag"
#~ msgstr "zigzaga"

#~ msgid "armchair"
#~ msgstr "besaulkia"

#~ msgid "  Bond length: "
#~ msgstr "  Lotura luzera: "

#~ msgid "Saturation: "
#~ msgstr "Saturazioa: "

#~ msgid "H"
#~ msgstr "H"

#~ msgid "Width: "
#~ msgstr "Zabalera: "

#~ msgid "  Length: "
#~ msgstr "  Luzera: "

#~ msgid "  No element specified!"
#~ msgstr "  Ez da elementurik zehaztu!"

#~ msgid "Please specify a consistent set of atoms. "
#~ msgstr "Mesedez, atomo multzo konsistentea zehaztu. "

#~ msgid "Expert mode ..."
#~ msgstr "Aditu modua ..."

#~ msgid "_Move atoms"
#~ msgstr "Atomoak _mugitu"

#~ msgid "_Rotate atoms"
#~ msgstr "Atomoak _biratu"

#~ msgid "_Bulk Crystal"
#~ msgstr "_Kristala"

#~ msgid ""
#~ "    Textures can be used to highlight different parts of\n"
#~ "    an atomic structure. This window applies the default\n"
#~ "    texture to the entire structure and optionally\n"
#~ "    applies a different texture to subsets of atoms that\n"
#~ "    can be selected using the mouse.\n"
#~ "    An alternative selection method is based on a boolean\n"
#~ "    expression in the entry box provided, using the\n"
#~ "    variables x, y, z, or Z. For example, the expression\n"
#~ "    Z == 11 and x > 10 and y > 10\n"
#~ "    will mark all sodium atoms with x or coordinates\n"
#~ "    larger than 10. In either case, the button labeled\n"
#~ "    `Create new texture from selection` will enable\n"
#~ "    to change the attributes of the current selection.\n"
#~ "    "
#~ msgstr ""
#~ "    Testurak atomo egitura bateko atal desberdinak nabarmentzeko\n"
#~ "    erabili daitezke. Leiho honek testura lehenetsiak aplikatuko\n"
#~ "    dizkio egitura guztiari, eta aukeran, saguaz hautatu daitezkeen\n"
#~ "    atomo azpimultzoari testura desberdinak.  Hautaketa modu\n"
#~ "    alternatiboa espresio bulear baten bidez egin daiteke, emandako\n"
#~ "    kutxan x, y, z, edo Z aldagaiak erabiliz. Adibidez, Z == 11 and x\n"
#~ "    > 10 and y > 10 espresioak, x eta y koordenatuak 10 baino\n"
#~ "    handiagoak dituzten sodio atomo guztiak markatuko ditu. Edonola\n"
#~ "    ere, `Hautapenetik testura berria sortu` botoiak uneko\n"
#~ "    hautapenaren atributuak aldatzea ahalbideratuko du.\n"
#~ "    "

#~ msgid "Width"
#~ msgstr "Zabalera"

#~ msgid "     Height"
#~ msgstr "     Altuera"

#~ msgid "Angstrom           "
#~ msgstr "Angstrom           "

#~ msgid "Set"
#~ msgstr "Sorta"

#~ msgid "               Filename: "
#~ msgstr "               Fitxategi-izena: "

#~ msgid " Default texture for atoms: "
#~ msgstr " Atomoentzako testura lehenetsia: "

#~ msgid "    transparency: "
#~ msgstr "    gardentasuna: "

#~ msgid "Define atom selection for new texture:"
#~ msgstr "Testura berrirako atomo hautapena definitu:"

#~ msgid "Select"
#~ msgstr "Hautatu"

#~ msgid "Create new texture from selection"
#~ msgstr "Hautapenetik testura berria sortu"

#~ msgid "Help on textures"
#~ msgstr "Laguntza testurekin"

#~ msgid "     Camera distance"
#~ msgstr "     Kamera distantzia"

#~ msgid "Render all %d frames"
#~ msgstr "%d fotograma errendatu"

#~ msgid "Run povray       "
#~ msgstr "povray exekutatu       "

#~ msgid "Keep povray files       "
#~ msgstr "povray fitxategiak mantentu      "

#~ msgid "  transparency: "
#~ msgstr "  gardentasuna: "

#~ msgid ""
#~ "Can not create new texture! Must have some atoms selected to create a new "
#~ "material!"
#~ msgstr ""
#~ "Ezin da testura berria sortu! Material berria sortzeko atomo batzuk "
#~ "hautatuta izan behar dituzu"

#~ msgid "Constrain immobile atoms"
#~ msgstr "Atomo mugiezinak mugatu"

#~ msgid "Output:"
#~ msgstr "Irteera:"

#~ msgid "Save output"
#~ msgstr "Irteera gorde"

#~ msgid "Potential energy and forces"
#~ msgstr "Energia eta indar potentziala"

#~ msgid "Calculate potential energy and the force on all atoms"
#~ msgstr "Atomo guztien energia potentziala eta indarra kalkulatu"

#~ msgid "Write forces on the atoms"
#~ msgstr "Atomoetan indarrak idatzi"

#~ msgid "Potential Energy:\n"
#~ msgstr "Energia Potentziala:\n"

#~ msgid "  %8.2f eV\n"
#~ msgstr "  %8.2f eV\n"

#~ msgid ""
#~ "  %8.4f eV/atom\n"
#~ "\n"
#~ msgstr ""
#~ "  %8.4f eV/atomo\n"
#~ "\n"

#~ msgid "Forces:\n"
#~ msgstr "Indarrak:\n"

#~ msgid "Clear"
#~ msgstr "Garbitu"

#~ msgid "_Calculate"
#~ msgstr "_Kalkulatu"

#~ msgid "Set _Calculator"
#~ msgstr "Kalkulagailua _ezarri"

#~ msgid "_Energy and Forces"
#~ msgstr "Energia eta _Indarrak"

#~ msgid "Energy Minimization"
#~ msgstr "Energia Minimizazioa"
