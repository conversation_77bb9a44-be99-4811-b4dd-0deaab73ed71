# Spanish translations for ASE package.
# Copyright (C) 2012-2019 ASE developers
# This file is distributed under the same license as the ASE package.
# <PERSON> <<EMAIL>>, 2012.
# Ask <PERSON><PERSON><PERSON> <ask<PERSON><PERSON>@gmail.com>, 2012-19.
#
msgid ""
msgstr ""
"Project-Id-Version: ase-3.5.2\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-10-23 20:47-0400\n"
"PO-Revision-Date: 2024-10-23 20:54-0400\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: ../add.py:10
msgid "(selection)"
msgstr "(selección)"

#: ../add.py:16
msgid "Add atoms"
msgstr "Agregar átomos"

#: ../add.py:17
msgid "Specify chemical symbol, formula, or filename."
msgstr "Especificar símbolo químico, fórmula o archivo."

#: ../add.py:44
msgid "Add:"
msgstr "Añadir:"

#: ../add.py:45
msgid "File ..."
msgstr "Archivo …"

#: ../add.py:54
msgid "Coordinates:"
msgstr "Coordenadas:"

#: ../add.py:56
msgid ""
"Coordinates are relative to the center of the selection, if any, else "
"absolute."
msgstr ""
"Coordenadas son relativas al centro de la selección si hay alguna. En otro "
"caso son absolutas."

#: ../add.py:58
msgid "Check positions"
msgstr "Validar posiciones"

#: ../add.py:59 ../nanoparticle.py:264
msgid "Add"
msgstr "Agregar"

#. May show UI error
#: ../add.py:104
msgid "Cannot add atoms"
msgstr "No se pueden añadir átomos"

#: ../add.py:105
msgid "{} is neither atom, molecule, nor file"
msgstr "{} no es ni átomo ni molécula ni archivo"

#: ../add.py:143
msgid "Bad positions"
msgstr "Posiciones no válidas"

#: ../add.py:144
msgid ""
"Atom would be less than 0.5 Å from an existing atom.  To override, uncheck "
"the check positions option."
msgstr ""
"Átomo quedaría a una distancia menor que 0,5 Å de otro átomo. Para ignorar "
"este aviso, desmarque la casilla de validar posiciones."

#. TRANSLATORS: This is a title of a window.
#: ../celleditor.py:49
msgid "Cell Editor"
msgstr "Editor de celda"

#: ../celleditor.py:53
msgid "A:"
msgstr "A:"

#: ../celleditor.py:53
msgid "||A||:"
msgstr "||A||:"

#: ../celleditor.py:54 ../celleditor.py:56 ../celleditor.py:58
msgid "periodic:"
msgstr "periódico:"

#: ../celleditor.py:55
msgid "B:"
msgstr "B:"

#: ../celleditor.py:55
msgid "||B||:"
msgstr "||B||:"

#: ../celleditor.py:57
msgid "C:"
msgstr "C:"

#: ../celleditor.py:57
msgid "||C||:"
msgstr "||C||:"

#: ../celleditor.py:59
msgid "∠BC:"
msgstr "∠BC:"

#: ../celleditor.py:59
msgid "∠AC:"
msgstr "∠AC:"

#: ../celleditor.py:60
msgid "∠AB:"
msgstr "∠AB:"

#: ../celleditor.py:61
msgid "Scale atoms with cell:"
msgstr "Ajustar posiciones proporcionalmente:"

#: ../celleditor.py:62
msgid "Apply Vectors"
msgstr "Aplicar vectores"

#: ../celleditor.py:63
msgid "Apply Magnitudes"
msgstr "Aplicar longitudes"

#: ../celleditor.py:64
msgid "Apply Angles"
msgstr "Aplicar ángulos"

#: ../celleditor.py:65
msgid ""
"Pressing 〈Enter〉 as you enter values will automatically apply correctly"
msgstr "Pulse 〈Entrar〉al introducir valores para aplicar automáticamente"

#. TRANSLATORS: verb
#: ../celleditor.py:68
msgid "Center"
msgstr "Centrar"

#: ../celleditor.py:69
msgid "Wrap"
msgstr "Envolver"

#: ../celleditor.py:70
msgid "Vacuum:"
msgstr "Vacío:"

#: ../celleditor.py:71
msgid "Apply Vacuum"
msgstr "Aplicar vacío"

#: ../colors.py:17
msgid "Colors"
msgstr "Colores"

#: ../colors.py:19
msgid "Choose how the atoms are colored:"
msgstr "Elija el color de los átomos:"

#: ../colors.py:22
msgid "By atomic number, default \"jmol\" colors"
msgstr "Por número atómico, colores de \"jmol\" por defecto"

#: ../colors.py:23
msgid "By tag"
msgstr "Por etiqueta"

#: ../colors.py:24
msgid "By force"
msgstr "Por fuerza"

#: ../colors.py:25
msgid "By velocity"
msgstr "Por velocidad"

#: ../colors.py:26
msgid "By initial charge"
msgstr "Por carga inicial"

#: ../colors.py:27
msgid "By magnetic moment"
msgstr "Por momento magnético"

#: ../colors.py:28
msgid "By number of neighbors"
msgstr "Por número de vecinos"

#: ../colors.py:98
msgid "cmap:"
msgstr "colores:"

#: ../colors.py:100
msgid "N:"
msgstr "N:"

#. XXX what are optimal allowed range and steps ?
#: ../colors.py:116
msgid "min:"
msgstr "mín:"

#: ../colors.py:119
msgid "max:"
msgstr "max:"

#: ../constraints.py:7
msgid "Constraints"
msgstr "Restricciones"

#: ../constraints.py:8 ../settings.py:12
msgid "Fix"
msgstr "Fijar"

#: ../constraints.py:9 ../constraints.py:11
msgid "selected atoms"
msgstr "átomos seleccionados"

#: ../constraints.py:10
msgid "Release"
msgstr "Soltar"

#: ../constraints.py:12 ../settings.py:16
msgid "Clear all constraints"
msgstr "Eliminar todas las restricciones"

#: ../graphs.py:9
msgid ""
"Symbols:\n"
"<c>e</c>: total energy\n"
"<c>epot</c>: potential energy\n"
"<c>ekin</c>: kinetic energy\n"
"<c>fmax</c>: maximum force\n"
"<c>fave</c>: average force\n"
"<c>R[n,0-2]</c>: position of atom number <c>n</c>\n"
"<c>d(n<sub>1</sub>,n<sub>2</sub>)</c>: distance between two atoms "
"<c>n<sub>1</sub></c> and <c>n<sub>2</sub></c>\n"
"<c>i</c>: current image number\n"
"<c>E[i]</c>: energy of image number <c>i</c>\n"
"<c>F[n,0-2]</c>: force on atom number <c>n</c>\n"
"<c>V[n,0-2]</c>: velocity of atom number <c>n</c>\n"
"<c>M[n]</c>: magnetic moment of atom number <c>n</c>\n"
"<c>A[0-2,0-2]</c>: unit-cell basis vectors\n"
"<c>s</c>: path length\n"
"<c>a(n1,n2,n3)</c>: angle between atoms <c>n<sub>1</sub></c>, <c>n<sub>2</"
"sub></c> and <c>n<sub>3</sub></c>, centered on <c>n<sub>2</sub></c>\n"
"<c>dih(n1,n2,n3,n4)</c>: dihedral angle between <c>n<sub>1</sub></c>, "
"<c>n<sub>2</sub></c>, <c>n<sub>3</sub></c> and <c>n<sub>4</sub></c>\n"
"<c>T</c>: temperature (K)"
msgstr ""
"Ayuda para graficar …\n"
"\n"
"<c>e</c>: energía total\n"
"<c>epot</c>: energía potencial\n"
"<c>ekin</c>: energía cinética\n"
"<c>fmax</c>: fuerza máxima\n"
"<c>fave</c>: fuerza media\n"
"<c>R[n,0-2]</c>: posición del átomo de número <c>n</c>\n"
"<c>d(n<sub>1</sub>,n<sub>2</sub>)</c>: distancia entre dos átomos "
"<c>n<sub>1</sub></c> y <c>n<sub>2</sub></c>\n"
"<c>i</c>: número de la imagen actual\n"
"<c>E[i]</c>: energía del número de la imagen <c>i</c>\n"
"<c>F[n,0-2]</c>: fuerza de en número de átomos de <c>n</c>\n"
"<c>V[n,0-2]</c>: velocidad de número de átomos de <c>n</c>\n"
"<c>M[n]</c>: momento magnético de número de átomos de <c>n</c>\n"
"<c>A[0-2,0-2]</c>: celda unitaria vectores de la base\n"
"<c>s</c>: longitud de la trayectoria\n"
"<c>a(n1,n2,n3)</c>: ángulo entre los átomos <c>n<sub>1</sub></c>, "
"<c>n<sub>2</sub></c> y <c>n<sub>3</sub></c>, centrado en <c>n<sub>2</sub></"
"c>\n"
"<c>dih(n1,n2,n3,n4)</c>: diedro ángulo entre <c>n<sub>1</sub></c>, "
"<c>n<sub>2</sub></c>, <c>n<sub>3</sub></c> y <c>n<sub>4</sub></c>\n"
"<c>T</c>: temperatura (K)"

#: ../graphs.py:40 ../graphs.py:42
msgid "Plot"
msgstr "Graficar"

#: ../graphs.py:44
msgid "Save"
msgstr "Guardar"

#: ../graphs.py:67
msgid "Save data to file ... "
msgstr "Salve los datos a un archivo …"

#: ../gui.py:208
msgid "Delete atoms"
msgstr "Borrar átomos"

#: ../gui.py:209
msgid "Delete selected atoms?"
msgstr "¿Borrar los átomos seleccionados?"

#. Subprocess probably crashed
#: ../gui.py:266
msgid "Failure in subprocess"
msgstr "Error en subproceso"

#: ../gui.py:273
msgid "Plotting failed"
msgstr "Error al dibujar gráfico"

#: ../gui.py:280
msgid "Images must have energies and forces, and atoms must not be stationary."
msgstr "Imágenes deben tener energías y fuerzas, y los átomos deben moverse."

#: ../gui.py:293
msgid "Images must have energies and varying cell."
msgstr "Imágenes deben tener energías y la celda unitaria debe ser variable."

#: ../gui.py:300
msgid "Requires 3D cell."
msgstr "Se necesita celda unitaria 3D."

#: ../gui.py:334
msgid "Quick Info"
msgstr "Información rápida"

#: ../gui.py:471
msgid "_File"
msgstr "_Archivo"

#: ../gui.py:472
msgid "_Open"
msgstr "_Abrir"

#: ../gui.py:473
msgid "_New"
msgstr "_Nuevo"

#: ../gui.py:474
msgid "_Save"
msgstr "_Guardar"

#: ../gui.py:476
msgid "_Quit"
msgstr "_Salir"

#: ../gui.py:478
msgid "_Edit"
msgstr "_Editar"

#: ../gui.py:479
msgid "Select _all"
msgstr "Seleccionar _todo"

#: ../gui.py:480
msgid "_Invert selection"
msgstr "_Invertir selección"

#: ../gui.py:481
msgid "Select _constrained atoms"
msgstr "Seleccionar los átomos _restringidos"

#: ../gui.py:482
msgid "Select _immobile atoms"
msgstr "Seleccionar los átomos _inmóbiles"

#. M('---'),
#: ../gui.py:484
msgid "_Cut"
msgstr "_Cortar"

#: ../gui.py:485
msgid "_Copy"
msgstr "_Copiar"

#: ../gui.py:486
msgid "_Paste"
msgstr "_Pegar"

#: ../gui.py:488
msgid "Hide selected atoms"
msgstr "Ocultar átomos seleccionados"

#: ../gui.py:489
msgid "Show selected atoms"
msgstr "Mostrar átomos seleccionados"

#: ../gui.py:491
msgid "_Modify"
msgstr "_Modificar"

#: ../gui.py:492
msgid "_Add atoms"
msgstr "_Añadir átomos"

#: ../gui.py:493
msgid "_Delete selected atoms"
msgstr "_Borrar átomos seleccionados"

#: ../gui.py:495
msgid "Edit _cell"
msgstr "Editar _celda"

#: ../gui.py:497
msgid "_First image"
msgstr "_Primera imagen"

#: ../gui.py:498
msgid "_Previous image"
msgstr "_Imagen previa"

#: ../gui.py:499
msgid "_Next image"
msgstr "_Próxima imagen"

#: ../gui.py:500
msgid "_Last image"
msgstr "Ú_ltima imagen"

#: ../gui.py:501
msgid "Append image copy"
msgstr "Agregar copia de imagen"

#: ../gui.py:503
msgid "_View"
msgstr "_Ver"

#: ../gui.py:504
msgid "Show _unit cell"
msgstr "Mostrar la celda _unitaria"

#: ../gui.py:506
msgid "Show _axes"
msgstr "Mostrar los _ejes"

#: ../gui.py:508
msgid "Show _bonds"
msgstr "Mostrar los _enlaces"

#: ../gui.py:510
msgid "Show _velocities"
msgstr "Mostrar las _velocidades"

#: ../gui.py:512
msgid "Show _forces"
msgstr "Mostrar las _fuerzas"

#: ../gui.py:514
msgid "Show _Labels"
msgstr "Mostrar los _etiquetas"

#: ../gui.py:515
msgid "_None"
msgstr "_Ninguno"

#: ../gui.py:516
msgid "Atom _Index"
msgstr "_Índice de Atom"

#: ../gui.py:517
msgid "_Magnetic Moments"
msgstr "Momentos _Magnético"

#. XXX check if exist
#: ../gui.py:518
msgid "_Element Symbol"
msgstr "Símbolo _Químico"

#: ../gui.py:519
msgid "_Initial Charges"
msgstr "Cargas _iniciales"

#: ../gui.py:522
msgid "Quick Info ..."
msgstr "Información rápida …"

#: ../gui.py:523
msgid "Repeat ..."
msgstr "Repetir …"

#: ../gui.py:524
msgid "Rotate ..."
msgstr "Rotar …"

#: ../gui.py:525
msgid "Colors ..."
msgstr "Colores …"

#. TRANSLATORS: verb
#: ../gui.py:527
msgid "Focus"
msgstr "Enfocar"

#: ../gui.py:528
msgid "Zoom in"
msgstr "Ampliar"

#: ../gui.py:529
msgid "Zoom out"
msgstr "Alejar"

#: ../gui.py:530
msgid "Change View"
msgstr "Cambiar de vista"

#: ../gui.py:532
msgid "Reset View"
msgstr "Reiniciar la vista"

#: ../gui.py:533
msgid "xy-plane"
msgstr "plano xy"

#: ../gui.py:534
msgid "yz-plane"
msgstr "plano yz"

#: ../gui.py:535
msgid "zx-plane"
msgstr "plano xz"

#: ../gui.py:536
msgid "yx-plane"
msgstr "plano yx"

#: ../gui.py:537
msgid "zy-plane"
msgstr "plano zy"

#: ../gui.py:538
msgid "xz-plane"
msgstr "plano xz"

#: ../gui.py:539
msgid "a2,a3-plane"
msgstr "plano a2,a3"

#: ../gui.py:540
msgid "a3,a1-plane"
msgstr "plano a3,a1"

#: ../gui.py:541
msgid "a1,a2-plane"
msgstr "plano a1,a2"

#: ../gui.py:542
msgid "a3,a2-plane"
msgstr "plano a3,a2"

#: ../gui.py:543
msgid "a1,a3-plane"
msgstr "plano a1,a3"

#: ../gui.py:544
msgid "a2,a1-plane"
msgstr "plano a2,a1"

#: ../gui.py:545
msgid "Settings ..."
msgstr "Ajustes …"

#: ../gui.py:547
msgid "VMD"
msgstr "VMD"

#: ../gui.py:548
msgid "RasMol"
msgstr "RasMol"

#: ../gui.py:549
msgid "xmakemol"
msgstr "xmakemol"

#: ../gui.py:550
msgid "avogadro"
msgstr "avogadro"

#: ../gui.py:552
msgid "_Tools"
msgstr "_Herramientas"

#: ../gui.py:553
msgid "Graphs ..."
msgstr "Gráficos …"

#: ../gui.py:554
msgid "Movie ..."
msgstr "Película …"

#: ../gui.py:555
msgid "Constraints ..."
msgstr "Restricciones …"

#: ../gui.py:556
msgid "Render scene ..."
msgstr "Dibujar escena …"

#: ../gui.py:557
msgid "_Move selected atoms"
msgstr "_Movér átomos seleccionados"

#: ../gui.py:558
msgid "_Rotate selected atoms"
msgstr "_Rotar átomos seleccionados"

#: ../gui.py:560
msgid "NE_B plot"
msgstr "Gráfico NE_B"

#: ../gui.py:561
msgid "B_ulk Modulus"
msgstr "Módulo de b_ulto"

#: ../gui.py:562
msgid "Reciprocal space ..."
msgstr "Espacio recíproco …"

#. TRANSLATORS: Set up (i.e. build) surfaces, nanoparticles, ...
#: ../gui.py:565
msgid "_Setup"
msgstr "_Configurar"

#: ../gui.py:566
msgid "_Surface slab"
msgstr "Trozo de _superficie"

#: ../gui.py:567
msgid "_Nanoparticle"
msgstr "_Nanopartícula"

#: ../gui.py:569
msgid "Nano_tube"
msgstr "Nano_tubo"

#. (_('_Calculate'),
#. [M(_('Set _Calculator'), self.calculator_window, disabled=True),
#. M(_('_Energy and Forces'), self.energy_window, disabled=True),
#. M(_('Energy Minimization'), self.energy_minimize_window,
#. disabled=True)]),
#: ../gui.py:577
msgid "_Help"
msgstr "_Ayuda"

#: ../gui.py:578
msgid "_About"
msgstr "_Acerca de ag"

#: ../gui.py:582
msgid "Webpage ..."
msgstr "Página web …"

#. Host window will never be shown
#: ../images.py:259
msgid "Constraints discarded"
msgstr "Restricciones descartadas"

#: ../images.py:260
msgid "Constraints other than FixAtoms have been discarded."
msgstr "Se han descartado las restricciones salvo FixAtoms."

#: ../modify.py:20
msgid "No atoms selected!"
msgstr "¡No hay átomos seleccionados!"

#: ../modify.py:23
msgid "Modify"
msgstr "Modificar"

#: ../modify.py:26
msgid "Change element"
msgstr "Cambiar elemento"

#: ../modify.py:29
msgid "Tag"
msgstr "Etiqueta"

#: ../modify.py:31
msgid "Moment"
msgstr "Momento magnético"

#: ../movie.py:10
msgid "Movie"
msgstr "Película"

#: ../movie.py:11
msgid "Image number:"
msgstr "Imagen número:"

#: ../movie.py:17
msgid "First"
msgstr "Primero"

#: ../movie.py:18
msgid "Back"
msgstr "Volver"

#: ../movie.py:19
msgid "Forward"
msgstr "Avanzar"

#: ../movie.py:20
msgid "Last"
msgstr "Último"

#: ../movie.py:22
msgid "Play"
msgstr "Reproducir"

#: ../movie.py:23
msgid "Stop"
msgstr "Detener"

#. TRANSLATORS: This function plays an animation forwards and backwards
#. alternatingly, e.g. for displaying vibrational movement
#: ../movie.py:27
msgid "Rock"
msgstr "Repetir cuadro"

#: ../movie.py:40
msgid " Frame rate: "
msgstr "Velocidad del cuadro: "

#: ../movie.py:40
msgid " Skip frames: "
msgstr "Saltar los cuadros: "

#. Delayed imports:
#. ase.cluster.data
#: ../nanoparticle.py:20
msgid ""
"Create a nanoparticle either by specifying the number of layers, or using "
"the\n"
"Wulff construction.  Please press the [Help] button for instructions on how "
"to\n"
"specify the directions.\n"
"WARNING: The Wulff construction currently only works with cubic crystals!\n"
msgstr ""
"Crear una nanopartícula especificando el número de capas,\n"
"ó utilizando la construcción de Wulff. Por favor, presione\n"
"el boton de ayuda para leer las instrucciones sobre cómo\n"
"especificar las direcciones.\n"
"¡ADVERTENCIA: En esta versión, la construcción de Wulff \n"
"sólo funciona para cristales cúbicos!\n"

#: ../nanoparticle.py:27
#, python-brace-format
msgid ""
"\n"
"The nanoparticle module sets up a nano-particle or a cluster with a given\n"
"crystal structure.\n"
"\n"
"1) Select the element, the crystal structure and the lattice constant(s).\n"
"   The [Get structure] button will find the data for a given element.\n"
"\n"
"2) Choose if you want to specify the number of layers in each direction, or "
"if\n"
"   you want to use the Wulff construction.  In the latter case, you must\n"
"   specify surface energies in each direction, and the size of the cluster.\n"
"\n"
"How to specify the directions:\n"
"------------------------------\n"
"\n"
"First time a direction appears, it is interpreted as the entire family of\n"
"directions, i.e. (0,0,1) also covers (1,0,0), (-1,0,0) etc.  If one of "
"these\n"
"directions is specified again, the second specification overrules that "
"specific\n"
"direction.  For this reason, the order matters and you can rearrange the\n"
"directions with the [Up] and [Down] keys.  You can also add a new "
"direction,\n"
"remember to press [Add] or it will not be included.\n"
"\n"
"Example: (1,0,0) (1,1,1), (0,0,1) would specify the {100} family of "
"directions,\n"
"the {111} family and then the (001) direction, overruling the value given "
"for\n"
"the whole family of directions.\n"
msgstr ""
"\n"
"Este módulo crea una nanopartícula o un cúmulo dada una\n"
"estructura cristalina.\n"
"\n"
"1) Seleccione el elemento, la estructura cristalina y la(s)\n"
"   constante(s) de red. El botón \"Obtener estructura\" \n"
"   encontrará los datos para el elemento seleccionado.\n"
"\n"
"2) Elija si desea especificar el número de capas en cada \n"
"   dirección, o si desea utilizar la construcción de Wulff.\n"
"   En el último caso, se debe especificar las energías de \n"
"   superficie en cada dirección, y el tamaño del cúmulo.\n"
"\n"
"Cómo especificar las direcciones:\n"
"---------------------------------\n"
"\n"
"La primera vez una dirección aparece, la cual es interpretada\n"
"como la familia completa de las direcciones, es decir, (0,0,1)\n"
"también cubre la dirección (1,0,0), (-1,0,0) etc. Si una de estas\n"
"direcciones es especificada nuevamente, la segunda especificación\n"
"reemplaza esa dirección en específico. Debido a esto, el orden\n"
"importa y se puede rearreglar la dirección con los botones Arriba y\n"
"Abajo. También se puede añadir una nueva dirección, recuerde presionar\n"
"el botón Añadir o ésta no será incluida.\n"
"\n"
"Ejemplo: (1,0,0) (1,1,1), (0,0,1) especificará la familia {100} de\n"
"direcciones, la familia {111} y luego la dirección (001), \n"
"sobreescribiendo el valor dado por toda la familia de direcciones.\n"

#: ../nanoparticle.py:87
msgid "Face centered cubic (fcc)"
msgstr "Cúbico centrado en las caras (fcc)"

#: ../nanoparticle.py:88
msgid "Body centered cubic (bcc)"
msgstr "Cúbico centrado en el cuerpo (bcc)"

#: ../nanoparticle.py:89
msgid "Simple cubic (sc)"
msgstr "Cúbico simple (sc)"

#: ../nanoparticle.py:90
msgid "Hexagonal closed-packed (hcp)"
msgstr "Empacamiento hexagonal cerrado (hcp)"

#: ../nanoparticle.py:91
msgid "Graphite"
msgstr "Grafito"

#: ../nanoparticle.py:136
msgid "Nanoparticle"
msgstr "Nanopartícula"

#: ../nanoparticle.py:140
msgid "Get structure"
msgstr "Obtener la estructura"

#: ../nanoparticle.py:155 ../surfaceslab.py:68
msgid "Structure:"
msgstr "Estructura:"

#: ../nanoparticle.py:159
msgid "Lattice constant:  a ="
msgstr "Constante de red: a ="

#: ../nanoparticle.py:163
msgid "Layer specification"
msgstr "Especificación de capas"

#: ../nanoparticle.py:163
msgid "Wulff construction"
msgstr "Construcción de Wulff"

#: ../nanoparticle.py:166
msgid "Method: "
msgstr "Método: "

#: ../nanoparticle.py:174
msgid "Add new direction:"
msgstr "Agregar nueva dirección:"

#. Information
#: ../nanoparticle.py:180
msgid "Information about the created cluster:"
msgstr "Información sobre el cluster creado:"

#: ../nanoparticle.py:181
msgid "Number of atoms: "
msgstr "Número de átomos: "

#: ../nanoparticle.py:183
msgid "   Approx. diameter: "
msgstr "   Diámetro aproximado: "

#: ../nanoparticle.py:192
msgid "Automatic Apply"
msgstr "Aplicar automáticamente"

#: ../nanoparticle.py:195 ../nanotube.py:49
msgid "Creating a nanoparticle."
msgstr "Creando una nanopartícula."

#: ../nanoparticle.py:197 ../nanotube.py:50 ../surfaceslab.py:81
msgid "Apply"
msgstr "Aplicar"

#: ../nanoparticle.py:198 ../nanotube.py:51 ../surfaceslab.py:82
msgid "OK"
msgstr "Aceptar"

#: ../nanoparticle.py:227
msgid "Up"
msgstr "Arriba"

#: ../nanoparticle.py:228
msgid "Down"
msgstr "Abajo"

#: ../nanoparticle.py:229
msgid "Delete"
msgstr "Borrar"

#: ../nanoparticle.py:271
msgid "Number of atoms"
msgstr "Número de átomos"

#: ../nanoparticle.py:271
msgid "Diameter"
msgstr "Diámetro"

#: ../nanoparticle.py:279
msgid "above  "
msgstr "sobre  "

#: ../nanoparticle.py:279
msgid "below  "
msgstr "abajo  "

#: ../nanoparticle.py:279
msgid "closest  "
msgstr "más cercano  "

#: ../nanoparticle.py:282
msgid "Smaller"
msgstr "Mas pequeño"

#: ../nanoparticle.py:283
msgid "Larger"
msgstr "Más largo"

#: ../nanoparticle.py:284
msgid "Choose size using:"
msgstr "Seleccionar tamaño usando:"

#: ../nanoparticle.py:286
msgid "atoms"
msgstr "átomos"

#: ../nanoparticle.py:287
msgid "Å³"
msgstr "Å³"

#: ../nanoparticle.py:289
msgid "Rounding: If exact size is not possible, choose the size:"
msgstr "Redondear: si el tamaño exacto no es posible, elegir el tamaño:"

#: ../nanoparticle.py:317
msgid "Surface energies (as energy/area, NOT per atom):"
msgstr "Energía de superficie (se reporta energía por área, NO por átomo):"

#: ../nanoparticle.py:319
msgid "Number of layers:"
msgstr "Número de capas:"

#: ../nanoparticle.py:347
msgid "At least one index must be non-zero"
msgstr "Al menos un índice debe ser distinto de cero"

#: ../nanoparticle.py:350
msgid "Invalid hexagonal indices"
msgstr "Índices hexagonales inválidos"

#: ../nanoparticle.py:415
msgid "Unsupported or unknown structure"
msgstr "Estructura no soportada o desconocida"

#: ../nanoparticle.py:416
#, python-brace-format
msgid "Element = {0}, structure = {1}"
msgstr "Elemento = {0}, estructura = {1}"

#: ../nanoparticle.py:530 ../nanotube.py:82 ../surfaceslab.py:221
msgid "No valid atoms."
msgstr "Los átomos no son válidos."

#: ../nanoparticle.py:531 ../nanotube.py:83 ../surfaceslab.py:222
#: ../widgets.py:93
msgid "You have not (yet) specified a consistent set of parameters."
msgstr "No ha especificado aún un conjunto consistente de parámetros."

#: ../nanotube.py:10
msgid ""
"Set up a Carbon nanotube by specifying the (n,m) roll-up vector.\n"
"Please note that m <= n.\n"
"\n"
"Nanotubes of other elements can be made by specifying the element\n"
"and bond length."
msgstr ""
"Configure un nanotubo de carbono specificando el vector de roll-up.\n"
"Note que m <= n.\n"
"\n"
"Nanotubos de otros elementos se pueden construir especificando el elemento y "
"largo del enlace."

#: ../nanotube.py:23
#, python-brace-format
msgid ""
"{natoms} atoms, diameter: {diameter:.3f} Å, total length: {total_length:.3f} "
"Å"
msgstr ""
"{natoms} átomos, diámetro: {diameter:.3f} Å, longitud total "
"{total_length:.3f} Å"

#: ../nanotube.py:38
msgid "Nanotube"
msgstr "Nanotubo"

#: ../nanotube.py:41
msgid "Bond length: "
msgstr "Largo del enlace: "

#: ../nanotube.py:43
msgid "Å"
msgstr "Å"

#: ../nanotube.py:44
msgid "Select roll-up vector (n,m) and tube length:"
msgstr "Seleccione vector de roll-up (n,m) y largo del tubo:"

#: ../nanotube.py:47
msgid "Length:"
msgstr "Largo:"

#: ../quickinfo.py:28
msgid "This frame has no atoms."
msgstr "Este cuadro no tiene átomos."

#: ../quickinfo.py:33
msgid "Single image loaded."
msgstr "Una imagen cargada."

#: ../quickinfo.py:35
msgid "Image {} loaded (0–{})."
msgstr "Imagen {} cargada (0–{})."

#: ../quickinfo.py:37
msgid "Number of atoms: {}"
msgstr "Número de átomos: {}"

#: ../quickinfo.py:47
msgid "Unit cell [Å]:"
msgstr "Celda unitaria [Å]:"

#: ../quickinfo.py:49
msgid "no"
msgstr "no"

#: ../quickinfo.py:49
msgid "yes"
msgstr "sí"

#. TRANSLATORS: This has the form Periodic: no, no, yes
#: ../quickinfo.py:52
msgid "Periodic: {}, {}, {}"
msgstr "Periódico: {}, {}, {}"

#: ../quickinfo.py:57
msgid "Lengths [Å]: {:.3f}, {:.3f}, {:.3f}"
msgstr "Longitudes [Å]: {:.3f}, {:.3f}, {:.3f}"

#: ../quickinfo.py:58
msgid "Angles: {:.1f}°, {:.1f}°, {:.1f}°"
msgstr "Ángulos: {:.1f}°, {:.1f}°, {:.1f}°"

#: ../quickinfo.py:61
msgid "Volume: {:.3f} Å³"
msgstr "Volumen: {:.3f} Å³"

#: ../quickinfo.py:67
msgid "Unit cell is fixed."
msgstr "La celda unitaria está fija."

#: ../quickinfo.py:69
msgid "Unit cell varies."
msgstr "La celda unitaria varía."

#: ../quickinfo.py:75
msgid "Could not recognize the lattice type"
msgstr "No pudo reconocer tipo de red"

#: ../quickinfo.py:77
msgid "Unexpected error determining lattice type"
msgstr "Error no esperado al determinar tipo de red"

#: ../quickinfo.py:79
msgid ""
"Reduced Bravais lattice:\n"
"{}"
msgstr ""
"Red Bravais reducida:\n"
"{}"

#: ../quickinfo.py:107
msgid "Calculator: {} (cached)"
msgstr "Calculador: {} (almacenado)"

#: ../quickinfo.py:109
msgid "Calculator: {} (attached)"
msgstr "Calculador: {} (adjunto)"

#: ../quickinfo.py:116
msgid "Energy: {:.3f} eV"
msgstr "Energía: {:.3f} eV"

#: ../quickinfo.py:121
msgid "Max force: {:.3f} eV/Å"
msgstr "Fuerza máxima: {:.3f} eV/Å"

#: ../quickinfo.py:125
msgid "Magmom: {:.3f} µ"
msgstr "Momento magnético: {:.3f} µ"

#: ../render.py:20
msgid "Render current view in povray ... "
msgstr "Dibujar vista actual en povray …"

#: ../render.py:21
#, python-format
msgid "Rendering %d atoms."
msgstr "Dibujando %d átomos."

#: ../render.py:26
msgid "Size"
msgstr "Tamaño"

#: ../render.py:31
msgid "Line width"
msgstr "Ancho de la línea"

#: ../render.py:32
msgid "Ångström"
msgstr "Ångström"

#: ../render.py:34
msgid "Render constraints"
msgstr "Restricciones del dibujo"

#: ../render.py:35
msgid "Render unit cell"
msgstr "Dibujar celda unitaria"

#: ../render.py:41
msgid "Output basename: "
msgstr "Nombre base para el archivo de salida: "

#: ../render.py:43
msgid "POVRAY executable"
msgstr "Ejecutable POVRAY"

#: ../render.py:45
msgid "Output filename: "
msgstr "Nombre de archivo de salida: "

#: ../render.py:50
msgid "Atomic texture set:"
msgstr "Conjunto de texturas atómicas:"

#: ../render.py:57
msgid "Camera type: "
msgstr "Tipo de cámara: "

#: ../render.py:58
msgid "Camera distance"
msgstr "Distancia de la cámara"

#. render current frame/all frames
#: ../render.py:61
msgid "Render current frame"
msgstr "Dibujar el cuadro actual"

#: ../render.py:62
msgid "Render all frames"
msgstr "Dibujar todos los cuadros"

#: ../render.py:67
msgid "Run povray"
msgstr "Ejecutar povray"

#: ../render.py:68
msgid "Keep povray files"
msgstr "Mantener los archivos povray"

#: ../render.py:69
msgid "Show output window"
msgstr "Mostrar ventana de salida"

#: ../render.py:70
msgid "Transparent background"
msgstr "Fondo transparente"

#: ../render.py:74
msgid "Render"
msgstr "Dibujar"

#: ../repeat.py:7
msgid "Repeat"
msgstr "Repetir"

#: ../repeat.py:8
msgid "Repeat atoms:"
msgstr "Repetir átomos:"

#: ../repeat.py:12
msgid "Set unit cell"
msgstr "Fijar la celda unitaria"

#: ../rotate.py:11
msgid "Rotate"
msgstr "Rotar"

#: ../rotate.py:12
msgid "Rotation angles:"
msgstr "Ángulos de rotación:"

#: ../rotate.py:16
msgid "Update"
msgstr "Actualizar"

#: ../rotate.py:17
msgid ""
"Note:\n"
"You can rotate freely\n"
"with the mouse, by holding\n"
"down mouse button 2."
msgstr ""
"Nota:\n"
"Usted puede rotar libremente\n"
"con el ratón, presionando el\n"
"botón número 2 del ratón."

#: ../save.py:15
msgid ""
"Append name with \"@n\" in order to write image\n"
"number \"n\" instead of the current image. Append\n"
"\"@start:stop\" or \"@start:stop:step\" if you want\n"
"to write a range of images. You can leave out\n"
"\"start\" and \"stop\" so that \"name@:\" will give\n"
"you all images. Negative numbers count from the\n"
"last image. Examples: \"name@-1\": last image,\n"
"\"name@-2:\": last two."
msgstr ""
"Agregue \"@n\" al nombre para escribir imágen número \"n\" en vez de la\n"
"imágen actual.  Agregue \"@principio:fin\" o \"@principio:fin:paso\" para\n"
"escribir una secuencia de imágenes.  Puede omitir \"principio\" y \"fin\"\n"
"y así \"nombre@:\" incluirá todas las imágenes.  Números negativos se\n"
"cuentan desde la última imágen. Ejemplos: \"nombre@-1\": última, \"nombre@-2:"
"\": las\n"
"dos últimas."

#: ../save.py:27
msgid "Save ..."
msgstr "Guardar …"

#: ../save.py:85 ../ui.py:32
msgid "Error"
msgstr "Error"

#: ../settings.py:8
msgid "Settings"
msgstr "Ajustes"

#. Constraints
#: ../settings.py:11
msgid "Constraints:"
msgstr "Restricciones:"

#: ../settings.py:14
msgid "release"
msgstr "Soltar"

#: ../settings.py:15 ../settings.py:23
msgid " selected atoms"
msgstr "  átomos seleccionados"

#. Visibility
#: ../settings.py:19
msgid "Visibility:"
msgstr "Visibilidad:"

#: ../settings.py:20
msgid "Hide"
msgstr "Esconder"

#: ../settings.py:22
msgid "show"
msgstr "Mostrar"

#: ../settings.py:24
msgid "View all atoms"
msgstr "Ver todos los átomos"

#. Miscellaneous
#: ../settings.py:27
msgid "Miscellaneous:"
msgstr "Misceláneos:"

#: ../settings.py:30
msgid "Scale atomic radii:"
msgstr "Escala de átomos:"

#: ../settings.py:37
msgid "Scale force vectors:"
msgstr "Escala de fuerzas:"

#: ../settings.py:44
msgid "Scale velocity vectors:"
msgstr "Escala de velocidades:"

#: ../status.py:80
#, python-format
msgid " tag=%(tag)s"
msgstr " etiqueta=%(tag)s"

#. TRANSLATORS: mom refers to magnetic moment
#: ../status.py:84
msgid " mom={:1.2f}"
msgstr " mom={:1.2f}"

#: ../status.py:88
msgid " q={:1.2f}"
msgstr " q={:1.2f}"

#: ../status.py:126
msgid "dihedral"
msgstr "diedral"

#: ../surfaceslab.py:9
msgid ""
"  Use this dialog to create surface slabs.  Select the element by\n"
"writing the chemical symbol or the atomic number in the box.  Then\n"
"select the desired surface structure.  Note that some structures can\n"
"be created with an othogonal or a non-orthogonal unit cell, in these\n"
"cases the non-orthogonal unit cell will contain fewer atoms.\n"
"\n"
"  If the structure matches the experimental crystal structure, you can\n"
"look up the lattice constant, otherwise you have to specify it\n"
"yourself."
msgstr ""
"Use esta ventana para crear un trozo de superficie. Seleccione el\n"
"elemento escribiendo el símbolo químico ó el número atómico en la\n"
"caja. Luego, seleccione la estructura de la superficie deseada. Note\n"
"que algunas estructuras pueden ser creadas con una celda unitaria or-\n"
"togonal u no ortogonal. En estos casos, la celda unitaria no ortogonal\n"
"contendrá menos átomos.\n"
"\n"
"Si la estructura coincide con la estructura cristalina experimental, usted\n"
"podrá buscar la constante de red en la base de datos de ASE. En otro caso, \n"
"tendrá que especificarla manualmente."

#. Name, structure, orthogonal, function
#: ../surfaceslab.py:21
msgid "FCC(100)"
msgstr "FCC(100)"

#: ../surfaceslab.py:21 ../surfaceslab.py:22 ../surfaceslab.py:23
#: ../surfaceslab.py:24
msgid "fcc"
msgstr "fcc"

#: ../surfaceslab.py:22
msgid "FCC(110)"
msgstr "FCC(110)"

#: ../surfaceslab.py:23 ../surfaceslab.py:171
msgid "FCC(111)"
msgstr "FCC(111)"

#: ../surfaceslab.py:24 ../surfaceslab.py:174
msgid "FCC(211)"
msgstr "FCC(211)"

#: ../surfaceslab.py:25
msgid "BCC(100)"
msgstr "BCC(100)"

#: ../surfaceslab.py:25 ../surfaceslab.py:26 ../surfaceslab.py:27
msgid "bcc"
msgstr "bcc"

#: ../surfaceslab.py:26 ../surfaceslab.py:168
msgid "BCC(110)"
msgstr "BCC(110)"

#: ../surfaceslab.py:27 ../surfaceslab.py:165
msgid "BCC(111)"
msgstr "BCC(111)"

#: ../surfaceslab.py:28 ../surfaceslab.py:178
msgid "HCP(0001)"
msgstr "HCP(0001)"

#: ../surfaceslab.py:28 ../surfaceslab.py:29 ../surfaceslab.py:132
#: ../surfaceslab.py:188
msgid "hcp"
msgstr "hcp"

#: ../surfaceslab.py:29 ../surfaceslab.py:181
msgid "HCP(10-10)"
msgstr "HCP(10-10)"

#: ../surfaceslab.py:30
msgid "DIAMOND(100)"
msgstr "Diamante (100)"

#: ../surfaceslab.py:30 ../surfaceslab.py:31
msgid "diamond"
msgstr "diamante"

#: ../surfaceslab.py:31
msgid "DIAMOND(111)"
msgstr "Diamante (111)"

#: ../surfaceslab.py:53
msgid "Get from database"
msgstr "Obtener desde la base de datos"

#: ../surfaceslab.py:65
msgid "Surface"
msgstr "Superficie"

#: ../surfaceslab.py:69
msgid "Orthogonal cell:"
msgstr "Celda unitaria ortogonal:"

#: ../surfaceslab.py:70
msgid "Lattice constant:"
msgstr "Constante de red:"

#: ../surfaceslab.py:71
msgid "\ta"
msgstr "\ta"

#: ../surfaceslab.py:72
msgid "\tc"
msgstr "\tc"

#: ../surfaceslab.py:73
msgid "Size:"
msgstr "Tamaño:"

#: ../surfaceslab.py:74
msgid "\tx: "
msgstr "\tx: "

#: ../surfaceslab.py:74 ../surfaceslab.py:75 ../surfaceslab.py:76
msgid " unit cells"
msgstr " celdas unitarias"

#: ../surfaceslab.py:75
msgid "\ty: "
msgstr "\ty: "

#: ../surfaceslab.py:76
msgid "\tz: "
msgstr "\tz: "

#: ../surfaceslab.py:77
msgid "Vacuum: "
msgstr "Vacío: "

#. TRANSLATORS: This is a title of a window.
#: ../surfaceslab.py:80
msgid "Creating a surface."
msgstr "Crear un trozo de superficie."

#. TRANSLATORS: E.g. "... assume fcc crystal structure for Au"
#: ../surfaceslab.py:108
msgid "Error: Reference values assume {} crystal structure for {}!"
msgstr "Error: Valores de referencia para {0} pertenecen a la estructura {1}"

#: ../surfaceslab.py:162
msgid "Please enter an even value for orthogonal cell"
msgstr "Por favor entre un valor par para celda ortogonal"

#: ../surfaceslab.py:175
msgid "Please enter a value divisible by 3 for orthogonal cell"
msgstr "Por favor entre un valor divisible por 3 para celda ortogonal"

#: ../surfaceslab.py:195
msgid " Vacuum: {} Å."
msgstr " Vacío: {} Å."

#. TRANSLATORS: e.g. "Au fcc100 surface with 2 atoms."
#. or "Au fcc100 surface with 2 atoms. Vacuum: 5 Å."
#: ../surfaceslab.py:203
#, python-brace-format
msgid "{symbol} {surf} surface with one atom.{vacuum}"
msgid_plural "{symbol} {surf} surface with {natoms} atoms.{vacuum}"
msgstr[0] "Superficie {surf} de {symbol} con {natoms} átomo.{vacuum}"
msgstr[1] "Superficie {surf} de {symbol} con {natoms} átomos.{vacuum}"

#: ../ui.py:39
msgid "Version"
msgstr "Versión"

#: ../ui.py:40
msgid "Web-page"
msgstr "Página web"

#: ../ui.py:41
msgid "About"
msgstr "Acerca de ag"

#: ../ui.py:47 ../ui.py:51 ../widgets.py:12
msgid "Help"
msgstr "Ayuda"

#: ../ui.py:560
msgid "Open ..."
msgstr "Abrir …"

#: ../ui.py:567
msgid "Automatic"
msgstr "Automático"

#: ../ui.py:585
msgid "Choose parser:"
msgstr "Elegir parser:"

#: ../ui.py:591
msgid "Read error"
msgstr "Error de lectura"

#: ../ui.py:592
#, python-brace-format
msgid "Could not read {filename}: {err}"
msgstr "No se pudo leer {filename}: {err}"

#: ../view.py:130
msgid "one image loaded"
msgid_plural "{} images loaded"
msgstr[0] "una imagen cargada"
msgstr[1] "{} imágenes cargadas"

#: ../widgets.py:10
msgid "Element:"
msgstr "Elemento:"

#: ../widgets.py:24
msgid "Enter a chemical symbol or the atomic number."
msgstr "Entre un símbolo químico o un número atómico."

#. Title of a popup window
#: ../widgets.py:26
msgid "Info"
msgstr "Información"

#: ../widgets.py:56
msgid "No element specified!"
msgstr "¡No se especifica el elemento!"

#: ../widgets.py:75
msgid "ERROR: Invalid element!"
msgstr "ERROR: ¡elemento inválido!"

#: ../widgets.py:92
msgid "No Python code"
msgstr "No es código de Python"

#~ msgid "Get molecule:"
#~ msgstr "Seleccionar molécula:"

#~ msgid "Constrain"
#~ msgstr "Restricción"

#~ msgid "immobile atoms"
#~ msgstr "átomos inamovibles"

#~ msgid "Unconstrain"
#~ msgstr "Liberar restricciones"

#~ msgid "Clear constraints"
#~ msgstr "Quitar las restricciones"

#~ msgid ""
#~ "Set up a graphene sheet or a graphene nanoribbon.  A nanoribbon may\n"
#~ "optionally be saturated with hydrogen (or another element)."
#~ msgstr ""
#~ "Configure una sábana de grafeno o una nanocinta. Opcionalmente,\n"
#~ "la nanocinta puede ser saturada con hidrógeno u otro elemento."

#~ msgid " %(natoms)i atoms: %(symbols)s, Volume: %(volume).3f A<sup>3</sup>"
#~ msgstr ""
#~ " %(natoms)i átomos: %(symbols)s, Volumen: %(volume).3f A<sup>3</sup>"

#~ msgid "Graphene"
#~ msgstr "Grafeno"

#~ msgid "Structure: "
#~ msgstr "Estructura: "

#~ msgid "Infinite sheet"
#~ msgstr "Sábana infinita"

#~ msgid "Unsaturated ribbon"
#~ msgstr "Cinta no saturada"

#~ msgid "Saturated ribbon"
#~ msgstr "Cinta saturada"

#~ msgid "Orientation: "
#~ msgstr "Orientación: "

#~ msgid "zigzag"
#~ msgstr "Zigzag"

#~ msgid "armchair"
#~ msgstr "Sillón"

#~ msgid "  Bond length: "
#~ msgstr "  Largo del enlace: "

#~ msgid "Saturation: "
#~ msgstr "Saturación: "

#~ msgid "H"
#~ msgstr "H"

#~ msgid "Width: "
#~ msgstr "Ancho: "

#~ msgid "  Length: "
#~ msgstr "  Largo: "

#~ msgid "  No element specified!"
#~ msgstr "  ¡No se especifica el elemento!"

#~ msgid "Please specify a consistent set of atoms. "
#~ msgstr "Por favor, especifique un conjunto consistente de átomos. "

#~ msgid "Expert mode ..."
#~ msgstr "Modo experto …"

#~ msgid "_Bulk Crystal"
#~ msgstr "Cristal en _bulto"

#~ msgid "Constrain immobile atoms"
#~ msgstr "Restringir los átomos inmóbiles"

#~ msgid "Green"
#~ msgstr "Verde"

#~ msgid "Yellow"
#~ msgstr "Amarillo"

#~ msgid "_Move atoms"
#~ msgstr "_Mover los átomos"

#~ msgid "_Rotate atoms"
#~ msgstr "_Rotar los átomos"

#~ msgid ""
#~ "    Textures can be used to highlight different parts of\n"
#~ "    an atomic structure. This window applies the default\n"
#~ "    texture to the entire structure and optionally\n"
#~ "    applies a different texture to subsets of atoms that\n"
#~ "    can be selected using the mouse.\n"
#~ "    An alternative selection method is based on a boolean\n"
#~ "    expression in the entry box provided, using the\n"
#~ "    variables x, y, z, or Z. For example, the expression\n"
#~ "    Z == 11 and x > 10 and y > 10\n"
#~ "    will mark all sodium atoms with x or coordinates\n"
#~ "    larger than 10. In either case, the button labeled\n"
#~ "    `Create new texture from selection` will enable\n"
#~ "    to change the attributes of the current selection.\n"
#~ "    "
#~ msgstr ""
#~ "    Las texturas pueden ser utilizadas para destacar diferentes partes\n"
#~ "    de una estructura atómica. Esta ventana aplica la textura por "
#~ "defecto\n"
#~ "    a la estructura completa. Opcionalmente, aplica una textura distinta\n"
#~ "    a subconjuntos de átomos, los cuales pueden ser seleccionados "
#~ "utilizando\n"
#~ "    el ratón.\n"
#~ "    Además, en esta versión de ASE, se implementa un método de\n"
#~ "    selección alternativo, el cual está basado en expresiones\n"
#~ "    booleanas. Estas se pueden fijar en la caja de entrada, utilizando\n"
#~ "    las variables x, y, z ó Z. Por ejemplo, la expresión\n"
#~ "    Z == 11 and x > 10 and y > 10 marcará todos los átomos de sodio\n"
#~ "    con x o coordenadas mayores que 10.  En cualquier caso, el botón\n"
#~ "    'Crear nueva estructura desde la selección' activará los cambios a\n"
#~ "    los atributos de la selección actual.\n"
#~ "    "

#~ msgid "Width"
#~ msgstr "Ancho"

#~ msgid "     Height"
#~ msgstr "     Altura"

#~ msgid "Angstrom           "
#~ msgstr "Angstrom           "

#~ msgid "Set"
#~ msgstr "Fijar"

#~ msgid "               Filename: "
#~ msgstr "               Nombre de archivo: "

#~ msgid " Default texture for atoms: "
#~ msgstr " Textura por defecto para los átomos: "

#~ msgid "    transparency: "
#~ msgstr "    transparencia: "

#~ msgid "Define atom selection for new texture:"
#~ msgstr "Definir al selección del átomo para la nueva textura:"

#~ msgid "Select"
#~ msgstr "Seleccionar"

#~ msgid "Create new texture from selection"
#~ msgstr "Crear nueva textura desde selección"

#~ msgid "Help on textures"
#~ msgstr "Ayuda en texturas"

#~ msgid "     Camera distance"
#~ msgstr "     Distancia de la cámara"

#~ msgid "Render all %d frames"
#~ msgstr "Dibujar todos los %d cuadros"

#~ msgid "Run povray       "
#~ msgstr "Ejecutar povray       "

#~ msgid "Keep povray files       "
#~ msgstr "Mantener los archivos povray       "

#~ msgid "  transparency: "
#~ msgstr "  transparencia: "

#~ msgid ""
#~ "Can not create new texture! Must have some atoms selected to create a new "
#~ "material!"
#~ msgstr ""
#~ "¡No se puede crear la nueva textura! ¡Se debe seleccionar algunos átomos "
#~ "para crear un nuevo material!"

#~ msgid "Output:"
#~ msgstr "Salida:"

#~ msgid "Save output"
#~ msgstr "Guardar salida"

#~ msgid "Potential energy and forces"
#~ msgstr "Energía potencial y fuerzas"

#~ msgid "Calculate potential energy and the force on all atoms"
#~ msgstr "Calcular la energía potencial y la fuerza en todos los átomos"

#~ msgid "Write forces on the atoms"
#~ msgstr "Escribir las fuerzas en los átomos"

#~ msgid "Potential Energy:\n"
#~ msgstr "Energía potencial:\n"

#~ msgid "  %8.2f eV\n"
#~ msgstr "  %8.2f eV\n"

#~ msgid ""
#~ "  %8.4f eV/atom\n"
#~ "\n"
#~ msgstr ""
#~ "  %8.4f eV/átomo\n"
#~ "\n"

#~ msgid "Forces:\n"
#~ msgstr "Fuerzas:\n"

#~ msgid "Clear"
#~ msgstr "Limpiar"

#~ msgid "_Calculate"
#~ msgstr "_Calcular"

#~ msgid "Set _Calculator"
#~ msgstr "Fijar el _calculador"

#~ msgid "_Energy and Forces"
#~ msgstr "_Energía y Fuerzas"

#~ msgid "Energy Minimization"
#~ msgstr "Minimización de energía"

#~ msgid " (rerun simulation)"
#~ msgstr " (recalcular la simulación)"

#~ msgid " (continue simulation)"
#~ msgstr " (continuar simulación)"

#~ msgid "Select starting configuration:"
#~ msgstr "Seleccione la configuración inicial:"

#~ msgid "There are currently %i configurations loaded."
#~ msgstr "Actualmente hay %i configuraciones cargadas."

# Elegir cual será utilizada como la configuración inicial
#~ msgid "Choose which one to use as the initial configuration"
#~ msgstr "Elegir cual será utilizada como la configuración inicial"

#~ msgid "The first configuration %s."
#~ msgstr "La primera configuración %s."

#~ msgid "Configuration number "
#~ msgstr "Configuración número "

#~ msgid "The last configuration %s."
#~ msgstr "La última configuración %s."

#~ msgid "Run"
#~ msgstr "Calcular"

#~ msgid "No calculator: Use Calculate/Set Calculator on the menu."
#~ msgstr "No hay un calculador. Use Calcular/Fijar Calculador en el menú."

#~ msgid "No atoms present"
#~ msgstr "No hay átomos presentes"

#~ msgid ""
#~ "  Use this dialog to create crystal lattices. First select the "
#~ "structure,\n"
#~ "  either from a set of common crystal structures, or by space group "
#~ "description.\n"
#~ "  Then add all other lattice parameters.\n"
#~ "\n"
#~ "  If an experimental crystal structure is available for an atom, you can\n"
#~ "  look up the crystal type and lattice constant, otherwise you have to "
#~ "specify it\n"
#~ "  yourself.  "
#~ msgstr ""
#~ "  Utilice este diálogo para crear estructuras cristalinas.\n"
#~ "  Seleccione primero la estructura, desde un conjunto de\n"
#~ "  estructuras cristalinas básicas ó desde la descripción del\n"
#~ "  grupo espacial.\n"
#~ "  Luego añada todos los parámetros de red.\n"
#~ "\n"
#~ "  Si dispone de una estructura cristalina experimental para un\n"
#~ "  átomo, puede buscar el tipo de cristal y la constante de red,\n"
#~ "  de otra manera tendrá que especificarlas."

# Crear cristal por grupo espacial
#~ msgid "Create Bulk Crystal by Spacegroup"
#~ msgstr "Crear cristal por grupo espacial"

#~ msgid "Number: 1"
#~ msgstr "Número: 1"

#~ msgid "Lattice: "
#~ msgstr "Red: "

#~ msgid "\tSpace group: "
#~ msgstr "\tGrupo espacial: "

# Tamaño: x: 
#~ msgid "Size: x: "
#~ msgstr "Tamaño: x: "

#~ msgid "  y: "
#~ msgstr "  y: "

#~ msgid "  z: "
#~ msgstr "  z: "

#~ msgid "free"
#~ msgstr "libre"

#~ msgid "equals b"
#~ msgstr "igual a b"

#~ msgid "equals c"
#~ msgstr "igual a c"

#~ msgid "fixed"
#~ msgstr "fijo"

#~ msgid "equals a"
#~ msgstr "igual a a"

#~ msgid "equals beta"
#~ msgstr "igual a beta"

#~ msgid "equals gamma"
#~ msgstr "igual a gama"

#~ msgid "equals alpha"
#~ msgstr "igual a alfa"

#~ msgid "Lattice parameters"
#~ msgstr "Parámetros de red"

#~ msgid "\t\ta:\t"
#~ msgstr "\t\ta:\t"

#~ msgid "\talpha:\t"
#~ msgstr "\talfa:\t"

#~ msgid "\t\tb:\t"
#~ msgstr "\t\tb:\t"

#~ msgid "\tbeta:\t"
#~ msgstr "\tbeta:\t"

#~ msgid "\t\tc:\t"
#~ msgstr "\t\tc:\t"

#~ msgid "\tgamma:\t"
#~ msgstr "\tgamma:\t"

#~ msgid "Basis: "
#~ msgstr "Base: "

#~ msgid "  Element:\t"
#~ msgstr "  Elemento:%t"

#~ msgid "Creating a crystal."
#~ msgstr "Creando un cristal."

#~ msgid "Symbol: %s"
#~ msgstr "Símbolo: %s"

#~ msgid "Number: %s"
#~ msgstr "Número: %s"

#~ msgid "Invalid Spacegroup!"
#~ msgstr "¡Grupo espacial inválido!"

#~ msgid "Please specify a consistent set of atoms."
#~ msgstr "Por favor, especifique un conjunto consistente de átomos."

#~ msgid "Can't find lattice definition!"
#~ msgstr "¡No puedo encontrar la definición de red!"

#~ msgid "Absolute position:"
#~ msgstr "Posición absoluta:"

#~ msgid "Relative to average position (of selection):"
#~ msgstr "Relativo a posición media (de la selección):"

#~ msgid ""
#~ "%s\n"
#~ "\n"
#~ "Number of atoms: %d.\n"
#~ "\n"
#~ "Unit cell:\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "\n"
#~ "%s\n"
#~ "%s\n"
#~ msgstr ""
#~ "%s\n"
#~ "\n"
#~ "Número de átomos: %d.\n"
#~ "\n"
#~ "Celda unitaria:\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "\n"
#~ "%s\n"
#~ "%s\n"

#~ msgid "Volume: "
#~ msgstr "Volumen: "

#~ msgid "Size: \tx: "
#~ msgstr "Tamaño en\tx: "

#~ msgid ""
#~ "To make most calculations on the atoms, a Calculator object must first\n"
#~ "be associated with it.  ASE supports a number of calculators, supporting\n"
#~ "different elements, and implementing different physical models for the\n"
#~ "interatomic interactions."
#~ msgstr ""
#~ "Para realizar la mayoría de los calculos sobre los átomos,\n"
#~ "se debe primero definir y asociar un objeto calculador \n"
#~ "(Calculator) con éstos. ASE soporta un número de calculadores, \n"
#~ "los cuales además de poder realizar cálculos sobre distintos \n"
#~ "elementos, implementan también modelos físicos diferentes para \n"
#~ "las interacciones interatómicas."

#~ msgid ""
#~ "The Lennard-Jones pair potential is one of the simplest\n"
#~ "possible models for interatomic interactions, mostly\n"
#~ "suitable for noble gasses and model systems.\n"
#~ "\n"
#~ "Interactions are described by an interaction length and an\n"
#~ "interaction strength."
#~ msgstr ""
#~ "El potencial de pares de Lennard-Jones es uno de los \n"
#~ "modelos más simples para las interacciones atómicas. \n"
#~ "Éste es adecuado para describir y modelar sistemas \n"
#~ "compuestos de gases nobles.\n"
#~ "\n"
#~ "Las interacciones en este potencial son descritas por \n"
#~ "un largo de interacción y una fuerza de interacción."

#~ msgid ""
#~ "The EMT potential is a many-body potential, giving a\n"
#~ "good description of the late transition metals crystalling\n"
#~ "in the FCC crystal structure.  The elements described by the\n"
#~ "main set of EMT parameters are Al, Ni, Cu, Pd, Ag, Pt, and\n"
#~ "Au, the Al potential is however not suitable for materials\n"
#~ "science application, as the stacking fault energy is wrong.\n"
#~ "\n"
#~ "A number of parameter sets are provided.\n"
#~ "\n"
#~ "<b>Default parameters:</b>\n"
#~ "\n"
#~ "The default EMT parameters, as published in K. W. Jacobsen,\n"
#~ "P. Stoltze and J. K. Nørskov, <i>Surf. Sci.</i> <b>366</b>, 394 (1996).\n"
#~ "\n"
#~ "<b>Alternative Cu, Ag and Au:</b>\n"
#~ "\n"
#~ "An alternative set of parameters for Cu, Ag and Au,\n"
#~ "reoptimized to experimental data including the stacking\n"
#~ "fault energies by Torben Rasmussen (partly unpublished).\n"
#~ "\n"
#~ "<b>Ruthenium:</b>\n"
#~ "\n"
#~ "Parameters for Ruthenium, as published in J. Gavnholt and\n"
#~ "J. Schiøtz, <i>Phys. Rev. B</i> <b>77</b>, 035404 (2008).\n"
#~ "\n"
#~ "<b>Metallic glasses:</b>\n"
#~ "\n"
#~ "Parameters for MgCu and CuZr metallic glasses. MgCu\n"
#~ "parameters are in N. P. Bailey, J. Schiøtz and\n"
#~ "K. W. Jacobsen, <i>Phys. Rev. B</i> <b>69</b>, 144205 (2004).\n"
#~ "CuZr in A. Paduraru, A. Kenoufi, N. P. Bailey and\n"
#~ "J. Schiøtz, <i>Adv. Eng. Mater.</i> <b>9</b>, 505 (2007).\n"
#~ msgstr ""
#~ "El potencial EMT es un potencial de muchos cuerpos, el cual describe\n"
#~ "de manera correcta a los metales de transición que cristalizan en una\n"
#~ "estructura tipo FCC. Los elementos descritos por el conjunto de pará-\n"
#~ "metros del EMT son Al, Ni, Cu, Pd, Ag, Pt y Au. Sin embargo, la "
#~ "descripción\n"
#~ "de este potencial para el aluminio no es adecuado para su uso en la\n"
#~ "ciencia de materiales, ya que la energía de apilamiento es incorrecta.\n"
#~ "\n"
#~ "Con ASE se provee un conjunto de parámetros para este potencial.\n"
#~ "\n"
#~ "<b>Parámetros por defecto:</b>\n"
#~ "\n"
#~ "Los parámetros por defecto de este potencial son extraídos de la "
#~ "siguiente\n"
#~ "publicación:  K. W. Jacobsen, P. Stoltze y J. K. Nørskov, <i>Surf. Sci.</"
#~ "i> \n"
#~ "<b>366</b>, 394 (1996).\n"
#~ "\n"
#~ "<b>Parámetros alternativos para Cu, Ag y Au:</b>\n"
#~ "\n"
#~ "Un conjunto de parámetros alternativo para Cu, Ag y Au fueron "
#~ "reoptimizados\n"
#~ "con datos experimentales incluyendo la energía de apilamiento "
#~ "(parcialmente no\n"
#~ "publicada) por Torben Rasmussen.\n"
#~ "\n"
#~ "<b>Rutenio:</b>\n"
#~ "\n"
#~ "Los parámetros para Rutenio fueron extraídos de la publicación J. "
#~ "Gavnholt y\n"
#~ "J. Schiøtz, <i>Phys. Rev. B</i> <b>77</b>, 035404 (2008).\n"
#~ "\n"
#~ "<b>Vidrios metálicos:</b>\n"
#~ "\n"
#~ "Conjunto de parámetros para vidrios metálicos compuestos de MgCu y CuZr. "
#~ "Los\n"
#~ "parámetros para MgCu fueron extraídos desde N. P. Bailey, J. Schiøtz y \n"
#~ "K. W. Jacobsen, <i>Phys. Rev. B</i> <b>69</b>, 144205 (2004). Para CuZr "
#~ "los\n"
#~ "parámetros fueron extraídos de la publicación A. Paduraru, A. Kenoufi, \n"
#~ "N. P. Bailey y J. Schiøtz, <i>Adv. Eng. Mater.</i> <b>9</b>, 505 (2007).\n"

#~ msgid ""
#~ "The EMT potential is a many-body potential, giving a\n"
#~ "good description of the late transition metals crystalling\n"
#~ "in the FCC crystal structure.  The elements described by the\n"
#~ "main set of EMT parameters are Al, Ni, Cu, Pd, Ag, Pt, and\n"
#~ "Au.  In addition, this implementation allows for the use of\n"
#~ "H, N, O and C adatoms, although the description of these is\n"
#~ "most likely not very good.\n"
#~ "\n"
#~ "<b>This is the ASE implementation of EMT.</b> For large\n"
#~ "simulations the ASAP implementation is more suitable; this\n"
#~ "implementation is mainly to make EMT available when ASAP is\n"
#~ "not installed.\n"
#~ msgstr ""
#~ "El potencial EMT es un potencial de muchos cuerpos, el\n"
#~ "cual describe de manera correcta a los metales de tran-\n"
#~ "sición que cristalizan en una estructura tipo FCC. Los \n"
#~ "elementos descritos por el conjunto de parámetros del \n"
#~ "EMT son Al, Ni, Cu, Pd, Ag, Pt y Au. Adicionalmente, esta\n"
#~ "implementación permite el uso de H, N, O y C. Sin embargo, \n"
#~ "la descripción de estos no es muy buena.\n"
#~ "\n"
#~ "<b>Esta es la implementación de ASE de EMT.</b> Para simu-\n"
#~ "laciones más grandes la implementación ASAP es más confiable.\n"
#~ "Esta implemetación es para tener un EMT cuando ASAP no está\n"
#~ "instalado.\n"

#~ msgid ""
#~ "The EAM/ADP potential is a many-body potential\n"
#~ "implementation of the Embedded Atom Method and\n"
#~ "equipotential plus the Angular Dependent Potential,\n"
#~ "which is an extension of the EAM to include\n"
#~ "directional bonds. EAM is suited for FCC metallic\n"
#~ "bonding while the ADP is suited for metallic bonds\n"
#~ "with some degree of directionality.\n"
#~ "\n"
#~ "For EAM see M.S. Daw and M.I. Baskes,\n"
#~ "Phys. Rev. Letters 50 (1983) 1285.\n"
#~ "\n"
#~ "For ADP see Y. Mishin, M.J. Mehl, and\n"
#~ "D.A. Papaconstantopoulos, Acta Materialia 53 2005\n"
#~ "4029--4041.\n"
#~ "\n"
#~ "Data for the potential is contained in a file in either LAMMPS Alloy\n"
#~ "or ADP format which need to be loaded before use. The Interatomic\n"
#~ "Potentials Repository Project at http://www.ctcms.nist.gov/potentials/\n"
#~ "contains many suitable potential files.\n"
#~ "\n"
#~ "For large simulations the LAMMPS calculator is more\n"
#~ "suitable; this implementation is mainly to make EAM\n"
#~ "available when LAMMPS is not installed or to develop\n"
#~ "new EAM/ADP poentials by matching results using ab\n"
#~ "initio.\n"
#~ msgstr ""
#~ "El potencial EAM/ADP es una implementación del método «embedded atom»\n"
#~ "(EAM) y equipotencial más el potencial direccional (ADP) como\n"
#~ "potencial de muchos cuerpos.  ADP es una extensión del EAM para\n"
#~ "incluir enlaces direccionales.  El EAM es adecuado para enlaces\n"
#~ "metálicos FCC mientras ADP es adecuado para enlaces metálicos con\n"
#~ "cierto carácter direccional.\n"
#~ "\n"
#~ "Para EAM véase M.S. Daw y M.I. Baskes,\n"
#~ "Phys. Rev. Letters 50 (1983) 1285.\n"
#~ "\n"
#~ "Para ADP véase Y. Mishin, M.J. Mehl, y\n"
#~ "D.A. Papaconstantopoulos, Acta Materialia 53 2005\n"
#~ "4029--4041.\n"
#~ "\n"
#~ "Los datos del potencial se encuentran en un fichero con formato LAMMPS\n"
#~ "Alloy o ADP, que necesita ser cargado antes de usarse.  Muchos ficheros\n"
#~ "de potenciales adecuados están disponibles en el proyecto de\n"
#~ "potenciales interatómicos en http://www.ctcms.nist.gov/potentials/.\n"
#~ "\n"
#~ "Para simulaciones grandes el calculador LAMMPS es más eficiente; esta\n"
#~ "implementación sirve principalmente para que EAM esté disponible cuando\n"
#~ "LAMMPS no se haya instalado o para desarrollar nuevos potenciales\n"
#~ "EAM/ADP comparando los resultados con aquellos obtenidos con métodos\n"
#~ "ab-initio.\n"

#~ msgid ""
#~ "The Brenner potential is a reactive bond-order potential for\n"
#~ "carbon and hydrocarbons.  As a bond-order potential, it takes\n"
#~ "into account that carbon orbitals can hybridize in different\n"
#~ "ways, and that carbon can form single, double and triple\n"
#~ "bonds.  That the potential is reactive means that it can\n"
#~ "handle gradual changes in the bond order as chemical bonds\n"
#~ "are formed or broken.\n"
#~ "\n"
#~ "The Brenner potential is implemented in Asap, based on a\n"
#~ "C implentation published at http://www.rahul.net/pcm/brenner/ .\n"
#~ "\n"
#~ "The potential is documented here:\n"
#~ "  Donald W Brenner, Olga A Shenderova, Judith A Harrison,\n"
#~ "  Steven J Stuart, Boris Ni and Susan B Sinnott:\n"
#~ "  \"A second-generation reactive empirical bond order (REBO)\n"
#~ "  potential energy expression for hydrocarbons\",\n"
#~ "  J. Phys.: Condens. Matter 14 (2002) 783-802.\n"
#~ "  doi: 10.1088/0953-8984/14/4/312\n"
#~ msgstr ""
#~ "El potencial de Brenner es un potencial reactivo al orden\n"
#~ "del enlace para carbón e hidrocarbones. Como es un potencial\n"
#~ "con orden de enlace, toma en cuenta que los orbitales de \n"
#~ "carbono pueden hibridizarse de diferentes maneras, y que el\n"
#~ "carbon puede formar enlaces simples, dobles y triples. \n"
#~ "Que el potencial sea reactivo significa que puede manejar \n"
#~ "cambios graduales en el orden del enlace, como por ejemplo\n"
#~ " que los enlaces químicos se pueden crear y destruir.\n"
#~ "\n"
#~ "El potencial de Brenner está implementado en ASAP, basado \n"
#~ "en una implementación de C publicada en \n"
#~ "\n"
#~ "http://www.rahul.net/pcm/brenner/ .\n"
#~ "\n"
#~ "El potencial está documentado aquí:\n"
#~ "  Donald W Brenner, Olga A Shenderova, Judith A Harrison,\n"
#~ "  Steven J Stuart, Boris Ni and Susan B Sinnott:\n"
#~ "  \"A second-generation reactive empirical bond order (REBO)\n"
#~ "  potential energy expression for hydrocarbons\",\n"
#~ "  J. Phys.: Condens. Matter 14 (2002) 783-802. \n"
#~ "  doi: 10.1088/0953-8984/14/4/312\n"

#~ msgid ""
#~ "GPAW implements Density Functional Theory using a\n"
#~ "<b>G</b>rid-based real-space representation of the wave\n"
#~ "functions, and the <b>P</b>rojector <b>A</b>ugmented <b>W</b>ave\n"
#~ "method for handling the core regions.\n"
#~ msgstr ""
#~ "GPAW implementa la teoría del funcional de la densidad\n"
#~ "utilizando una representación en el espacio real basado\n"
#~ "en grillas de las funciones de onda, y el método de las\n"
#~ "ondas aumentadas proyectadas para manejar las regiones\n"
#~ "del núcleo.\n"

#~ msgid ""
#~ "FHI-aims is an external package implementing density\n"
#~ "functional theory and quantum chemical methods using\n"
#~ "all-electron methods and a numeric local orbital basis set.\n"
#~ "For full details, see http://www.fhi-berlin.mpg.de/aims/\n"
#~ "or Comp. Phys. Comm. v180 2175 (2009). The ASE\n"
#~ "documentation contains information on the keywords and\n"
#~ "functionalities available within this interface.\n"
#~ msgstr ""
#~ "FHI-aims es un paquete externo que implementa la teoría del \n"
#~ "funcional de la densidad y métodos químicos cuánticos utilizando\n"
#~ "métodos con todos los electrones y bases numéricas locales. \n"
#~ "\n"
#~ "Para mayores detalles, visite\n"
#~ "\n"
#~ "http://www.fhi-berlin.mpg.de/aims/ \n"
#~ "\n"
#~ "o revise la referencia Comp. Phys. Comm. v180 2175 (2009). \n"
#~ "La documentación de ASE contiene información sobre las \n"
#~ "palabras clave y las funcionalidades disponibles en esta \n"
#~ "interfaz.\n"

#~ msgid ""
#~ "WARNING:\n"
#~ "Your system seems to have more than zero but less than\n"
#~ "three periodic dimensions. Please check that this is\n"
#~ "really what you want to compute. Assuming full\n"
#~ "3D periodicity for this calculator."
#~ msgstr ""
#~ "ADVERTENCIA: al parecer su sistema tiene más de una pero menos\n"
#~ "de tres dimensiones periódicas. Por favor, compruebe que esto es\n"
#~ "realmente lo que desea calcular. Asumiendo periodicidad 3D completa\n"
#~ "para éste cálculo."

#~ msgid ""
#~ "VASP is an external package implementing density\n"
#~ "functional functional theory using pseudopotentials\n"
#~ "or the projector-augmented wave method together\n"
#~ "with a plane wave basis set. For full details, see\n"
#~ "http://cms.mpi.univie.ac.at/vasp/vasp/\n"
#~ msgstr ""
#~ "VASP es un paquete externo, el cual implementa la\n"
#~ "teoría del funcional de la densidad utilizando\n"
#~ "pseudopotenciales ó el método de las ondas proyectadas\n"
#~ "y aumentadas en conjunto con un conjunto de ondas planas.\n"
#~ "Para más detalles, visite\n"
#~ "\n"
#~ "http://cms.mpi.univie.ac.at/vasp/vasp/\n"

#~ msgid "Default (Al, Ni, Cu, Pd, Ag, Pt, Au)"
#~ msgstr "Por defecto (Al, Ni, Cu, Pd, Ag, Pt, Au)"

#~ msgid "Alternative Cu, Ag and Au"
#~ msgstr "Parámetros alternativos para Cu, Ag y Au"

#~ msgid "Ruthenium"
#~ msgstr "Rutenio"

#~ msgid "CuMg and CuZr metallic glass"
#~ msgstr "Vidrios metálicos de CuMg y CuZr"

#~ msgid "Select calculator"
#~ msgstr "Seleccione Calculador (Calculator)"

#~ msgid "None"
#~ msgstr "Ninguno"

#~ msgid "Lennard-Jones (ASAP)"
#~ msgstr "Lennard-Jones (ASAP)"

#~ msgid "Setup"
#~ msgstr "Configuración"

# EMT - Effective Medium Theory (ASAP)
#~ msgid "EMT - Effective Medium Theory (ASAP)"
#~ msgstr "EMT - Teoría Media Efectiva (ASAP)"

#~ msgid "EMT - Effective Medium Theory (ASE)"
#~ msgstr "EMT - Teoría Media Efectiva (ASE)"

#~ msgid "EAM - Embedded Atom Method/Angular Dependent Potential (ASE)"
#~ msgstr "EAM - Método «embedded atom»/potencial direccional (ASE)"

#~ msgid "Brenner Potential (ASAP)"
#~ msgstr "Potencial de Brenner (ASAP)"

#~ msgid "Density Functional Theory (GPAW)"
#~ msgstr "Teoría del funcional de la densidad (GPAW)"

#~ msgid "Density Functional Theory (FHI-aims)"
#~ msgstr "Teoría del funcional de la densidad (FHI-aims)"

#~ msgid "Density Functional Theory (VASP)"
#~ msgstr "Teoría del funcional de la densidad (VASP)"

#~ msgid "Check that the calculator is reasonable."
#~ msgstr "Compruebe que el calculador sea razonable."

#~ msgid "ASAP is not installed. (Failed to import asap3)"
#~ msgstr "ASAP no está instalado. (Error al importar asap3)"

#~ msgid "You must set up the Lennard-Jones parameters"
#~ msgstr "Usted debe establecer los parámetros del potencial de Lennard-Jones"

#~ msgid "Could not create useful Lennard-Jones calculator."
#~ msgstr "No se pudo crear un calculador Lennard-Jones útil."

#~ msgid "Could not attach EMT calculator to the atoms."
#~ msgstr "No se pudo adjuntar el calculador EMT a los átomos."

#~ msgid "You must set up the EAM parameters"
#~ msgstr "Debe establecer los parámetros para EAM"

#~ msgid "GPAW is not installed. (Failed to import gpaw)"
#~ msgstr "GPAW no está instalado (Error al importar gpaw)"

#~ msgid "You must set up the GPAW parameters"
#~ msgstr "Debe establecer los parámetros para GPAW"

#~ msgid "You must set up the FHI-aims parameters"
#~ msgstr "Debe establecer los parámetros para FHI-aims"

#~ msgid "You must set up the VASP parameters"
#~ msgstr "Debe establecer los parámetros para VASP"

#~ msgid "Element %(sym)s not allowed by the '%(name)s' calculator"
#~ msgstr "El elemento %(sym)s no está permitido para el calculador '%(name)s'"

#~ msgid "Lennard-Jones parameters"
#~ msgstr "Parámetros de Lennard-Jones"

#~ msgid "Specify the Lennard-Jones parameters here"
#~ msgstr "Especifique los parámetros de Lennard-Jones aquí"

# Epsilon (eV)
#~ msgid "Epsilon (eV):"
#~ msgstr "Epsilon (eV):"

#~ msgid "Sigma (Å):"
#~ msgstr "Sigma (Å):"

#~ msgid "Shift to make smooth at cutoff"
#~ msgstr "Cambie para que el corte sea suave"

#~ msgid "EAM parameters"
#~ msgstr "Parámetros de EAM"

#~ msgid "Import Potential"
#~ msgstr "Importar potencial"

#~ msgid "You need to import the potential file"
#~ msgstr "Se necesita importar el fichero del potencial"

#~ msgid "Import .alloy or .adp potential file ... "
#~ msgstr "Importar fichero potencial .alloy o .adp …"

#~ msgid "GPAW parameters"
#~ msgstr "Parámetros de GPAW"

#~ msgid "%i atoms.\n"
#~ msgstr "átomos %i.\n"

#~ msgid "Orthogonal unit cell: %.2f x %.2f x %.2f Å."
#~ msgstr "Celda unitaria ortogonal:  %.2f x %.2f x %.2f Å."

#~ msgid "Non-orthogonal unit cell:\n"
#~ msgstr "Celda unitaria no ortogonal:\n"

#~ msgid "Exchange-correlation functional: "
#~ msgstr "Funcional de intercambio y correlación: "

#~ msgid "Grid spacing"
#~ msgstr "Espacio de grilla"

#~ msgid "Grid points"
#~ msgstr "Puntos de grilla"

#~ msgid "h<sub>eff</sub> = (%.3f, %.3f, %.3f) Å"
#~ msgstr "h<sub>eff</sub> = (%.3f, %.3f, %.3f) Å"

#~ msgid "k-points  k = ("
#~ msgstr "puntos k k=("

#~ msgid "k-points x size:  (%.1f, %.1f, %.1f) Å"
#~ msgstr "Tamaño de los puntos k: (%.1f, %.1f, %.1f) Å"

#~ msgid "Spin polarized"
#~ msgstr "Polarizado de espín"

#~ msgid "FD - Finite Difference (grid) mode"
#~ msgstr "Modo de grilla tipo diferencias finitas"

#~ msgid "LCAO - Linear Combination of Atomic Orbitals"
#~ msgstr "LCAO - Combinaciones lineales de orbitales atómicos"

#~ msgid "Mode: "
#~ msgstr "Modo: "

#~ msgid "sz - Single Zeta"
#~ msgstr "sz - Single Zeta"

#~ msgid "szp - Single Zeta polarized"
#~ msgstr "szp - Single Zeta polarizado"

#~ msgid "dzp - Double Zeta polarized"
#~ msgstr "dzp - Doble Zeta polarizado"

#~ msgid "Basis functions: "
#~ msgstr "Funciones base: "

#~ msgid "Non-standard mixer parameters"
#~ msgstr "Parámetros combinados no estándar"

#~ msgid "FHI-aims parameters"
#~ msgstr "Parámetros de FHI-aims"

#~ msgid "Periodic geometry, unit cell is:\n"
#~ msgstr "Geometría periódica, la celda unitaria es:\n"

#~ msgid "Non-periodic geometry.\n"
#~ msgstr "Geometría no periódica\n"

#~ msgid "Hirshfeld-based dispersion correction"
#~ msgstr "Corrección a la dispersión basada en el método de Hirshfeld"

#~ msgid "Spin / initial moment "
#~ msgstr "Spin / momento inicial"

#~ msgid "   Charge"
#~ msgstr "   Carga"

#~ msgid "   Relativity"
#~ msgstr "   Relatividad"

#~ msgid " Threshold"
#~ msgstr " Umbral"

#~ msgid "Self-consistency convergence:"
#~ msgstr "Convergencia auto-consistente:"

#~ msgid "Compute forces"
#~ msgstr "Calcule las fuerzas"

#~ msgid "Energy:                 "
#~ msgstr "Energía:                "

#~ msgid " eV   Sum of eigenvalues:  "
#~ msgstr " eV   Suma de los autovalores:  "

#~ msgid " eV"
#~ msgstr " eV"

#~ msgid "Electron density: "
#~ msgstr "Densidad electrónica: "

#~ msgid "        Force convergence:  "
#~ msgstr "       Convergencia de la fuerza:  "

#~ msgid " eV/Ang  "
#~ msgstr " eV/Å    "

#~ msgid "Additional keywords: "
#~ msgstr "Palabras clave adicionales: "

#~ msgid "FHI-aims execution command: "
#~ msgstr "Comando de ejecución por FHI-aims: "

#~ msgid "Directory for species defaults: "
#~ msgstr "Directorio para las especies por defecto: "

#~ msgid "Set Defaults"
#~ msgstr "Establecer por defecto"

#~ msgid "Import control.in"
#~ msgstr "Importar control.in"

#~ msgid "Export control.in"
#~ msgstr "Exportar control.in"

#~ msgid "Export parameters ... "
#~ msgstr "Exportar parámetros … "

#~ msgid "Import control.in file ... "
#~ msgstr "Importar el archivo control.in … "

#~ msgid ""
#~ "Please use the facilities provided in this window to manipulate the "
#~ "keyword: %s!"
#~ msgstr ""
#~ "Por favor use las interfases previstas en esta ventana para manipular la "
#~ "palabra clave: \n"
#~ "%s!"

#~ msgid ""
#~ "Don't know this keyword: %s\n"
#~ "\n"
#~ "Please check!\n"
#~ "\n"
#~ "If you really think it should be available, please add it to the top of "
#~ "ase/calculators/aims.py."
#~ msgstr ""
#~ "No conozco la palabra clave %s\n"
#~ "\n"
#~ "Por favor, compruebe que la palabra clave esté correcta!\n"
#~ "\n"
#~ "Si usted realmente piensa que debería estar disponible, por favor "
#~ "agrégela al inicio de\n"
#~ "\n"
#~ "ase/calculators/aims.py."

#~ msgid "VASP parameters"
#~ msgstr "Parámetros de VASP"

#~ msgid "Periodic geometry, unit cell is: \n"
#~ msgstr "Geometría periódica, la celda unitaria es: \n"

#~ msgid ")    Cutoff: "
#~ msgstr ") radio de corte: "

#~ msgid "    Precision: "
#~ msgstr "    Precisión: "

#~ msgid "k-points x size:  (%.1f, %.1f, %.1f) Å       "
#~ msgstr "Tamaño de los puntos k:  (%.1f, %.1f, %.1f) Å       "

# Smearing: 
#~ msgid "Smearing: "
#~ msgstr "Smearing: "

#~ msgid " order: "
#~ msgstr " orden: "

#~ msgid " width: "
#~ msgstr " ancho: "

#~ msgid "Self-consistency convergence: "
#~ msgstr "Convergencia auto-consistente: "

#~ msgid "VASP execution command: "
#~ msgstr "Comando de ejecución de VASP: "

#~ msgid "Import VASP files"
#~ msgstr "Importar archivos de VASP"

#~ msgid "Export VASP files"
#~ msgstr "Exportar archivos de VASP"

#~ msgid "<b>WARNING:</b> cutoff energy is lower than recommended minimum!"
#~ msgstr ""
#~ "<b>ADVERTENCIA:</b> ¡La energía de corte es más baja que el mínimo "
#~ "recomendado!"

#~ msgid "Import VASP input files: choose directory ... "
#~ msgstr "Importando archivos de entrada de VASP: elija directorio … "

#~ msgid "Export VASP input files: choose directory ... "
#~ msgstr "Exportando archivos de salida de VASP: elija directorio … "

#~ msgid ""
#~ "Don't know this keyword: %s\n"
#~ "Please check!\n"
#~ "\n"
#~ "If you really think it should be available, please add it to the top of "
#~ "ase/calculators/vasp.py."
#~ msgstr ""
#~ "No conozco esta palabra clave: %s\n"
#~ "¡Por favor, revísela!\n"
#~ "\n"
#~ "Si usted realmente cree que debería estar disponible, por favor\n"
#~ "agrégela al inicio del archivo\n"
#~ "calculators/vasp.py."

#~ msgid ""
#~ "\n"
#~ "    Global commands work on all frames or only on the current frame\n"
#~ "    - Assignment of a global variable may not reference a local one\n"
#~ "    - use 'Current frame' switch to switch off application to all frames\n"
#~ "    <c>e</c>:\t\ttotal energy of one frame\n"
#~ "    <c>fmax</c>:\tmaximal force in one frame\n"
#~ "    <c>A</c>:\tunit cell\n"
#~ "    <c>E</c>:\t\ttotal energy array of all frames\n"
#~ "    <c>F</c>:\t\tall forces in one frame\n"
#~ "    <c>M</c>:\tall magnetic moments\n"
#~ "    <c>R</c>:\t\tall atomic positions\n"
#~ "    <c>S</c>:\tall selected atoms (boolean array)\n"
#~ "    <c>D</c>:\tall dynamic atoms (boolean array)\n"
#~ "    examples: <c>frame = 1</c>, <c>A[0][1] += 4</c>, <c>e-E[-1]</c>\n"
#~ "\n"
#~ "    Atom commands work on each atom (or a selection) individually\n"
#~ "    - these can use global commands on the RHS of an equation\n"
#~ "    - use 'selected atoms only' to restrict application of command\n"
#~ "    <c>x,y,z</c>:\tatomic coordinates\n"
#~ "    <c>r,g,b</c>:\tatom display color, range is [0..1]\n"
#~ "    <c>rad</c>:\tatomic radius for display\n"
#~ "    <c>s</c>:\t\tatom is selected\n"
#~ "    <c>d</c>:\t\tatom is movable\n"
#~ "    <c>f</c>:\t\tforce\n"
#~ "    <c>Z</c>:\tatomic number\n"
#~ "    <c>m</c>:\tmagnetic moment\n"
#~ "    examples: <c>x -= A[0][0], s = z > 5, Z = 6</c>\n"
#~ "\n"
#~ "    Special commands and objects:\n"
#~ "    <c>sa,cf</c>:\t(un)restrict to selected atoms/current frame\n"
#~ "    <c>frame</c>:\tframe number\n"
#~ "    <c>center</c>:\tcenters the system in its existing unit cell\n"
#~ "    <c>del S</c>:\tdelete selection\n"
#~ "    <c>CM</c>:\tcenter of mass\n"
#~ "    <c>ans[-i]</c>:\tith last calculated result\n"
#~ "    <c>exec file</c>: executes commands listed in file\n"
#~ "    <c>cov[Z]</c>:(read only): covalent radius of atomic number Z\n"
#~ "    <c>gui</c>:\tadvanced: gui window python object\n"
#~ "    <c>img</c>:\tadvanced: gui images object\n"
#~ "    "
#~ msgstr ""
#~ "\n"
#~ "   Los comandos globales funcionan tanto en todos los cuadros como\n"
#~ "   en el cuadro actual\n"
#~ "   -  La asignación de una variable global puede no ser refernciada\n"
#~ "      a una local.\n"
#~ "   -  Utilice el interruptor 'Cuadro actual' para apagar la aplicación\n"
#~ "      a todos los cuadros.\n"
#~ "      <c>e</c>: energía total de un cuadro\n"
#~ "      <c>fmáx</c>: fuerza máxima en un cuadro\n"
#~ "      <c>A</c>: celda unitaria\n"
#~ "      <c>E</c>: arreglo con las energías totales en todos los cuadros\n"
#~ "      <c>F</c>: todas las fuerzas en un cuadro\n"
#~ "      <c>M</c>: todos los momentos magnéticos\n"
#~ "      <c>R</c>: todas las posiciones atómicas\n"
#~ "      <c>S</c>: arreglo booleano, todos los átomos seleccionados\n"
#~ "      <c>D</c>: arreglo booleano, todos los átomos dinámicos\n"
#~ "      Ejemplos: <c>cuadro = 1</c>, <c>A[0][1] += 4</c>, <c>e-E[-1]</c>\n"
#~ "\n"
#~ "      Los comandos atómicos funcionan en una selección o en cada uno de\n"
#~ "      los átomos.\n"
#~ "      - Éstos pueden utilizar comandos globales en el lado derecho de\n"
#~ "        una ecuación.\n"
#~ "      - Utilice 'Sólo los átomos seleccionados' para restringir la\n"
#~ "        aplicación del comando.\n"
#~ "        <c>x,y,z</c>: coordenadas atómicas\n"
#~ "        <c>r,g,b</c>: color del átomo, el rango es [0..1]\n"
#~ "        <c>rad</c>: radio atómico a mostrar\n"
#~ "        <c>s</c>: átomo es seleccionado\n"
#~ "        <c>d</c>: átomo es movible\n"
#~ "        <c>f</c>: fuerza\n"
#~ "        <c>Z</c>: número atómico\n"
#~ "        <c>m</c>: momento magnético\n"
#~ "        ejemplos: <c>x -= A[0][0], s = z > 5, Z = 6</c>\n"
#~ "\n"
#~ "      Comandos especiales y objetos:\n"
#~ "      <c>sa,cf</c>:(un)restrict to selected atoms/current frame\n"
#~ "      <c>cuadro</c>: número del cuadro\n"
#~ "      <c>centrar</c>: centra el sistema con respecto a su celda unitaria\n"
#~ "      <c>borra S</c>: borra la selección\n"
#~ "      <c>CM</c>: centro de masa\n"
#~ "      <c>ans[-i]</c>: el i-ésimo resultado calculado\n"
#~ "      <c>exec archivo</c>: ejecuta el comando listado en archivo\n"
#~ "      <c>cov[Z]</c>:(sólo lectura): radio covalente del número atómico Z\n"
#~ "      <c>gui</c>:avanzado: objeto de Python, ventana de ase-gui\n"
#~ "      <c>img</c>:avanzado: objeto de imágenes de ase-gui\n"
#~ "    "

#~ msgid "Expert user mode"
#~ msgstr "Modo de usuario experto"

#~ msgid "Welcome to the ASE Expert user mode"
#~ msgstr "Bienvenido al modo de usuario experto de ASE"

#~ msgid "Only selected atoms (sa)   "
#~ msgstr "Sólo los átomos seleccionados (sa)   "

#~ msgid "Only current frame (cf)  "
#~ msgstr "Sólo el cuadro actual (cf)  "

#~ msgid ""
#~ "Global: Use A, D, E, M, N, R, S, n, frame; Atoms: Use a, f, m, s, x, y, "
#~ "z, Z     "
#~ msgstr ""
#~ "Global: utilice los cuadros A, D, E, M, N, R, S y n; Átomos: utilice a, "
#~ "f, m, s, x, y, z y Z"

#~ msgid "*** WARNING: file does not exist - %s"
#~ msgstr "*** ADVERTENCIA: el archivo no existe - %s"

#~ msgid "*** WARNING: No atoms selected to work with"
#~ msgstr "***ADVERTENCIA: No hay átomos seleccionados para trabajar"

#~ msgid "*** Only working on selected atoms"
#~ msgstr "*** Trabajando sólo en los átomos seleccionados"

#~ msgid "*** Working on all atoms"
#~ msgstr "*** Trabajando en todos los átomos"

#~ msgid "*** Only working on current image"
#~ msgstr "*** Trabajando solamente en la imagen actual"

#~ msgid "*** Working on all images"
#~ msgstr "*** Trabajando en todas las imágenes"

#~ msgid "Save Terminal text ..."
#~ msgstr "Guarde texto a Terminal …"

#~ msgid "Cancel"
#~ msgstr "Cancelar"

#~ msgid "Algorithm: "
#~ msgstr "Algoritmo: "

#~ msgid "Convergence criterion: F<sub>max</sub> = "
#~ msgstr "Criterio de convergencia: F<sub>máx</sub> = "

#~ msgid "Max. number of steps: "
#~ msgstr "Número máximo de pasos: "

#~ msgid "Pseudo time step: "
#~ msgstr "Paso de pseudotiempo: "

#~ msgid "Energy minimization"
#~ msgstr "Minimización de energía"

#~ msgid "Minimize the energy with respect to the positions."
#~ msgstr "Minimize la energía con respecto a las posiciones."

#~ msgid "Running ..."
#~ msgstr "Calculando …"

#~ msgid "Minimization CANCELLED after %i steps."
#~ msgstr "Minimización CANCELADO después de %i iteraciones."

#~ msgid "Out of memory, consider using LBFGS instead"
#~ msgstr "No hay más memoria, considere usar el algoritmo LBFGS"

#~ msgid "Minimization completed in %i steps."
#~ msgstr "Minimización hecha en %i pasos."

#~ msgid "Progress"
#~ msgstr "Progreso"

#~ msgid "Scaling deformation:"
#~ msgstr "Escala de deformación:"

#~ msgid "Step number %s of %s."
#~ msgstr "Paso número %s de %s."

#~ msgid "Energy minimization:"
#~ msgstr "Minimización de energía:"

#~ msgid "Step number: "
#~ msgstr "Paso número: "

#~ msgid "F<sub>max</sub>: "
#~ msgstr "F<sub>máx</sub>: "

#~ msgid "unknown"
#~ msgstr "desconocido"

#~ msgid "Status: "
#~ msgstr "Estado: "

#~ msgid "Iteration: "
#~ msgstr "Iteración: "

#~ msgid "log<sub>10</sub>(change):"
#~ msgstr "log<sub>10</sub> (cambio):"

#~ msgid "Wave functions: "
#~ msgstr "Funciones de onda: "

#~ msgid "Density: "
#~ msgstr "Densidad: "

#~ msgid "GPAW version: "
#~ msgstr "Versión de GPAW: "

#~ msgid "N/A"
#~ msgstr "No disponible"

#~ msgid "Memory estimate: "
#~ msgstr "Memoria estimada: "

#~ msgid "No info"
#~ msgstr "No hay información"

#~ msgid "Initializing"
#~ msgstr "Iniciando"

#~ msgid "Positions:"
#~ msgstr "Posiciones:"

#~ msgid "Starting calculation"
#~ msgstr "Comenzando cálculo"

#~ msgid "unchanged"
#~ msgstr "sin cambios"

#~ msgid "Self-consistency loop"
#~ msgstr "Bucle de auto consistencia"

#~ msgid "Calculating forces"
#~ msgstr "Calculando fuerzas"

#~ msgid " (converged)"
#~ msgstr " (convergido)"

#~ msgid "To get a full traceback, use: ase-gui --verbose"
#~ msgstr "Para ver el traceback entero, use ase-gui --verbose"

#~ msgid "No atoms loaded."
#~ msgstr "No hay átomos seleccionados."

#~ msgid "FCC(111) non-orthogonal"
#~ msgstr "FCC (111) no ortogonal"

#~ msgid "FCC(111) orthogonal"
#~ msgstr "FCC (111) ortogonal"

#~ msgid "BCC(110) non-orthogonal"
#~ msgstr "BCC (110) no ortogonal"

#~ msgid "BCC(110) orthogonal"
#~ msgstr "BCC (110) ortogonal"

#~ msgid "BCC(111) non-orthogonal"
#~ msgstr "BCC (111) no ortogonal"

#~ msgid "BCC(111) orthogonal"
#~ msgstr "BCC (111) ortogonal"

#~ msgid "HCP(0001) non-orthogonal"
#~ msgstr "HCP (0001) no ortogonal"

#~ msgid "Element: "
#~ msgstr "Elemento: "

#~ msgid "a:"
#~ msgstr "a:"

#~ msgid "(%.1f %% of ideal)"
#~ msgstr "(%.1f %% de ideal)"

#~ msgid "      \t\tz: "
#~ msgstr "      \t\tz: "

#~ msgid " layers,  "
#~ msgstr " capas,  "

#~ msgid " Å vacuum"
#~ msgstr " vacío en Å"

#~ msgid "\t\tNo size information yet."
#~ msgstr "\t\tNo hay información sobre el tamaño aún."

#~ msgid "%i atoms."
#~ msgstr "Átomos %i."

#~ msgid "Invalid element."
#~ msgstr "Elemento inválido."

#~ msgid "No structure specified!"
#~ msgstr "¡No se especificó la estructura!"

#~ msgid "%(struct)s lattice constant unknown for %(element)s."
#~ msgstr ""
#~ "La constante de red %(struct)s es desconocida para el elemento "
#~ "%(element)s."

#~ msgid "By atomic number, user specified"
#~ msgstr "Por número atómico, especificado por el usuario"

#~ msgid "By coordination"
#~ msgstr "Por coordinación"

#~ msgid "Manually specified"
#~ msgstr "Especificado manualmente"

#~ msgid "All the same color"
#~ msgstr "Todos del mismo color"

#~ msgid "This should not be displayed in forces!"
#~ msgstr "¡Esto no debería ser mostrado en fuerzas!"

#~ msgid "Min: "
#~ msgstr "Mín: "

#~ msgid "  Max: "
#~ msgstr "  Máx: "

#~ msgid "  Steps: "
#~ msgstr "  Pasos: "

#~ msgid "This should not be displayed!"
#~ msgstr "¡Esto no debería ser mostrado!"

#~ msgid "Create a color scale:"
#~ msgstr "Crear una escala de colores:"

#~ msgid "Black - white"
#~ msgstr "Negro - blanco"

#~ msgid "Black - red - yellow - white"
#~ msgstr "Negro - rojo - amarillo - blanco"

#~ msgid "Black - green - white"
#~ msgstr "Negro - verde - blanco"

#~ msgid "Black - blue - cyan"
#~ msgstr "Negro - azul - cian"

#~ msgid "Blue - white - red"
#~ msgstr "Azul - blanco - rojo"

#~ msgid "Hue"
#~ msgstr "Tonalidad"

#~ msgid "Named colors"
#~ msgstr "Colores con nombre"

#~ msgid "Create"
#~ msgstr "Crear"

#~ msgid "ERROR"
#~ msgstr "ERROR"

#~ msgid "ERR"
#~ msgstr "ERR"

#~ msgid "Incorrect color specification"
#~ msgstr "Especificación de color incorrecta"

#~ msgid " selected atoms:"
#~ msgstr " átomos seleccionados:"

#~ msgid "Close"
#~ msgstr "Cerrar"

#~ msgid "Debug"
#~ msgstr "Depurar"

#~ msgid "Bug Detected"
#~ msgstr "Error detectado"

#~ msgid "A programming error has been detected."
#~ msgstr "Un error de programación ha sido detectado."

#~ msgid ""
#~ "It probably isn't fatal, but the details should be reported to the "
#~ "developers nonetheless."
#~ msgstr ""
#~ "Probablemente no es fatal. Sin embargo, los detalles deberían\n"
#~ "ser reportados a los desarrolladores."

#~ msgid "Report..."
#~ msgstr "Reporte …"

#~ msgid "Details..."
#~ msgstr "Detalles …"

#~ msgid ""
#~ "From: buggy_application\"\n"
#~ "To: bad_programmer\n"
#~ "Subject: Exception feedback\n"
#~ "\n"
#~ "%s"
#~ msgstr ""
#~ "Desde:  buggy_application\"\n"
#~ "A: bad_programmer\n"
#~ "Asunto: Retroalimentación de un error\n"
#~ "\n"
#~ "%s"

#~ msgid "Bug Details"
#~ msgstr "Detalles del error"

#~ msgid "Create a new file"
#~ msgstr "Crear un archivo nuevo"

#~ msgid "New ase.gui window"
#~ msgstr "Nueva ventana ase.gui"

#~ msgid "Save current file"
#~ msgstr "Guardar archivo actual"

#~ msgid "Quit"
#~ msgstr "Salir"

#~ msgid "Copy current selection and its orientation to clipboard"
#~ msgstr "Copiar la selección actual y su orientación"

#~ msgid "Insert current clipboard selection"
#~ msgstr "Insertar selección actual"

#~ msgid "Change tags, moments and atom types of the selected atoms"
#~ msgstr ""
#~ "Cambiar etiquetas, momentos magnéticos y tipo de los átomos seleccionados"

#~ msgid "Insert or import atoms and molecules"
#~ msgstr "Insertar o importar átomos y moléculas"

#~ msgid "Delete the selected atoms"
#~ msgstr "Borrar los átomos seleccionados"

#~ msgid "'xy' Plane"
#~ msgstr "Plano 'xy'"

#~ msgid "'yz' Plane"
#~ msgstr "Plano 'yz'"

#~ msgid "'zx' Plane"
#~ msgstr "Plano 'zx'"

#~ msgid "'yx' Plane"
#~ msgstr "Plano 'yx'"

#~ msgid "'zy' Plane"
#~ msgstr "Plano 'zy'"

#~ msgid "'xz' Plane"
#~ msgstr "Plano 'xz'"

#~ msgid "Create a bulk crystal with arbitrary orientation"
#~ msgstr "Crear un cristal en bulto con orientación arbitraria"

#~ msgid "Create the most common surfaces"
#~ msgstr "Crear las superficies más comunes"

#~ msgid "Create a crystalline nanoparticle"
#~ msgstr "Crear una nanoparticula cristalina"

#~ msgid "Create a nanotube"
#~ msgstr "Crear un nanotubo"

#~ msgid "Create a graphene sheet or nanoribbon"
#~ msgstr "Crear una sábana de grafeno o una nanocinta"

#~ msgid "Set a calculator used in all calculation modules"
#~ msgstr "Fijar un calculador utilizado en todos los módulos de cálculo"

#~ msgid "Calculate energy and forces"
#~ msgstr "Calcular energía y fuerzas"

#~ msgid "Minimize the energy"
#~ msgstr "Minimize la energía"

#~ msgid "Scale system"
#~ msgstr "Escale el sistema"

#~ msgid "Deform system by scaling it"
#~ msgstr "Deforme el sistema escalándolo"

#~ msgid "Debug ..."
#~ msgstr "Quitar errores …"

#~ msgid "Orien_t atoms"
#~ msgstr "Orien_tar los átomos"

#~ msgid "<<filename>>"
#~ msgstr "<<Nombre de archivo>>"

#~ msgid "Paste"
#~ msgstr "Pegar"

#~ msgid "Insert atom or molecule"
#~ msgstr "Insertar átomo o molécula"

#~ msgid "_Cancel"
#~ msgstr "_Cancelar"

#~ msgid "Atom"
#~ msgstr "Átomo"

#~ msgid "Confirmation"
#~ msgstr "Confirmación"

#~ msgid "File type:"
#~ msgstr "Tipo de archivo:"

#~ msgid "Not implemented!"
#~ msgstr "No implementado!"

#~ msgid "do you really need it?"
#~ msgstr "¿realmente necesita esto?"

#~ msgid "Dummy placeholder object"
#~ msgstr "Objeto marcador de posición ficticia"

#~ msgid "Set all directions to default values"
#~ msgstr "Fijar en todas las direcciones los valores por defecto"

#~ msgid "Particle size: "
#~ msgstr "Tamaño de la partícula: "

#~ msgid "%.1f Å"
#~ msgstr "%.1f Å"

#~ msgid "Python"
#~ msgstr "Python"

#~ msgid ""
#~ "\n"
#~ "Title: %(title)s\n"
#~ "Time: %(time)s\n"
#~ msgstr ""
#~ "\n"
#~ "Título: %(title)s\n"
#~ "Tiempo:  %(time)s\n"

#~ msgid "ag: Python code"
#~ msgstr "ag: código en Python"

#~ msgid "Information:"
#~ msgstr "Información:"

#~ msgid "Python code:"
#~ msgstr "Código en Python:"

#~ msgid "Homogeneous scaling"
#~ msgstr "Escala homogénea"

#~ msgid "3D deformation   "
#~ msgstr "Deformación en tres dimensiones   "

#~ msgid "2D deformation   "
#~ msgstr "Deformación en dos dimensiones   "

#~ msgid "1D deformation   "
#~ msgstr "Deformación en una dimensión   "

#~ msgid "Bulk"
#~ msgstr "Bulto"

#~ msgid "x-axis"
#~ msgstr "eje x"

#~ msgid "y-axis"
#~ msgstr "eje y"

#~ msgid "z-axis"
#~ msgstr "eje z"

#~ msgid "Allow deformation along non-periodic directions."
#~ msgstr "Permitir deformaciones a lo largo de direcciones no periódicas."

#~ msgid "Deformation:"
#~ msgstr "Deformación:"

#~ msgid "Maximal scale factor: "
#~ msgstr "Factor de escala máximo: "

#~ msgid "Scale offset: "
#~ msgstr "Compensación de escala: "

#~ msgid "Number of steps: "
#~ msgstr "Número de pasos: "

#~ msgid "Only positive deformation"
#~ msgstr "Sólo deformaciones positivas"

#~ msgid "On   "
#~ msgstr "Encendido   "

#~ msgid "Off"
#~ msgstr "Apagado"

#~ msgid "Results:"
#~ msgstr "Resultados:"

#~ msgid "Keep original configuration"
#~ msgstr "Mantener la configuración original"

#~ msgid "Load optimal configuration"
#~ msgstr "Cargar la configuración óptima"

#~ msgid "Load all configurations"
#~ msgstr "Cargar todas las configuraciones"

#~ msgid "Strain\t\tEnergy [eV]"
#~ msgstr "Energía de deformación [eV]"

#~ msgid "Fit:"
#~ msgstr "Ajuste:"

#~ msgid "2nd"
#~ msgstr "Segundo"

#~ msgid "3rd"
#~ msgstr "Tercero"

#~ msgid "Order of fit: "
#~ msgstr "Grado del ajuste: "

#~ msgid "Calculation CANCELLED."
#~ msgstr "Cálculo CANCELADO."

#~ msgid "Calculation completed."
#~ msgstr "Cálculo terminado."

#~ msgid "No trustworthy minimum: Old configuration kept."
#~ msgstr "El mínimo no es confiable: se mantiene la configuración anterior."

#~ msgid ""
#~ "Insufficent data for a fit\n"
#~ "(only %i data points)\n"
#~ msgstr ""
#~ "Datos insuficientes para un ajuste\n"
#~ "(sólo hay %i puntos)\n"

#~ msgid ""
#~ "REVERTING TO 2ND ORDER FIT\n"
#~ "(only 3 data points)\n"
#~ "\n"
#~ msgstr ""
#~ "VOLVIENDO A UN AJUSTE DE SEGUNDO ORDEN\n"
#~ "(sólo con 3 puntos)\n"
#~ "\n"

#~ msgid "No minimum found!"
#~ msgstr "¡No se encontró el mínimo!"

#~ msgid ""
#~ "\n"
#~ "WARNING: Minimum is outside interval\n"
#~ msgstr ""
#~ "\n"
#~ "ADVERTENCIA: El mínimo está fuera del intervalo\n"

#~ msgid "It is UNRELIABLE!\n"
#~ msgstr "¡Esto NO es confiable!\n"

#~ msgid "\n"
#~ msgstr "\n"

#~ msgid "No crystal structure data"
#~ msgstr "No existen los datos de estructura cristalina"

#~ msgid "Tip for status box ..."
#~ msgstr "Consejo para la ventana de estado …"

#~ msgid "Clear constraint"
#~ msgstr "Borrar restricción"

#~ msgid "DFT"
#~ msgstr "DFT"

#~ msgid "XC-functional: "
#~ msgstr "Funcional de XC: "

#~ msgid "DFT ..."
#~ msgstr "DFT …"

#~ msgid "building menus failed: %s"
#~ msgstr "La construcción de los menús ha fallado: %s"

#~ msgid "Dacapo netCDF output file"
#~ msgstr "Archivo de salida Dacapo (.netCDF)"

#~ msgid "Virtual Nano Lab file"
#~ msgstr "Archivo Virtual Nano Lab"

#~ msgid "ASE pickle trajectory"
#~ msgstr "Trajectoria ASE pickle"

#~ msgid "ASE bundle trajectory"
#~ msgstr "Trajectoria ASE ligada"

#~ msgid "GPAW text output"
#~ msgstr "Archivo de salida de texto (GPAW)"

#~ msgid "CUBE file"
#~ msgstr "Archivo CUBE"

#~ msgid "XCrySDen Structure File"
#~ msgstr "Archivo de estructura XCrySDen"

#~ msgid "Dacapo text output"
#~ msgstr "Archivo de salida de texto (Dacapo)"

#~ msgid "XYZ-file"
#~ msgstr "Archivo XYZ"

#~ msgid "VASP POSCAR/CONTCAR file"
#~ msgstr "Archivo POSCAR/CONTCAR (VASP)"

#~ msgid "VASP OUTCAR file"
#~ msgstr "Archivo OUTCAR (VASP)"

#~ msgid "Protein Data Bank"
#~ msgstr "Banco de datos de proteínas"

#~ msgid "CIF-file"
#~ msgstr "Archivo CIF"

#~ msgid "FHI-aims geometry file"
#~ msgstr "Archivo de geometría (FHI-aims)"

#~ msgid "FHI-aims output file"
#~ msgstr "Archivo de salida (FHI-aims)"

#~ msgid "TURBOMOLE coord file"
#~ msgstr "Archivo de coordenadas (TURBOMOLE)"

#~ msgid "exciting input"
#~ msgstr "Archivo de entrada (exciting)"

#~ msgid "WIEN2k structure file"
#~ msgstr "Archivo de estructura (WIEN2k)"

#~ msgid "DftbPlus input file"
#~ msgstr "Archivo de entrada (DFTBPlus)"

#~ msgid "ETSF format"
#~ msgstr "Formato ETSF"

#~ msgid "CASTEP geom file"
#~ msgstr "Archivo de geometría (CASTEP)"

#~ msgid "CASTEP output file"
#~ msgstr "Archivo de salida (CASTEP)"

#~ msgid "CASTEP trajectory file"
#~ msgstr "Archivo de trajectoria (CASTEP)"

#~ msgid "DFTBPlus GEN format"
#~ msgstr "Formato GEN (DFTBPlus)"

#~ msgid ""
#~ "\n"
#~ "An exception occurred!  Please report the issue to\n"
#~ "<EMAIL> - thanks!  Please also report this "
#~ "if\n"
#~ "it was a user error, so that a better error message can be provided\n"
#~ "next time."
#~ msgstr ""
#~ "\n"
#~ "Se produjo un error! Por favor, reporte el problema a\n"
#~ "\n"
#~ "<EMAIL> \n"
#~ "\n"
#~ "Muchas gracias! Por favor, reporte también el problema si\n"
#~ "fue un error de usuario, de esta forma podemos proveer un\n"
#~ "mejor mensaje de error la próxima vez."

#~ msgid "Max force: %.2f (this frame), %.2f (all frames)"
#~ msgstr "Fuerza máx: %.2f (este cuadro), %.2f (todos los cuadros)"

#~ msgid "Max velocity: %.2f (this frame), %.2f (all frames)"
#~ msgstr "Velocidad máxima: %.2f (este cuadro), %.2f (todos los cuadros)"

#~ msgid "Max velocity: %.2f."
#~ msgstr "Velocidad máxima: %.2f."

#~ msgid "Min, max charge: %.2f, %.2f (this frame),"
#~ msgstr "Carga mín, máx: %.2f, %.2f (este cuadro),"

#~ msgid "Min, max charge: %.2f, %.2f."
#~ msgstr "Carga mín, máx: %.2f, %.2f."

#~ msgid "XYZ file"
#~ msgstr "Archivo XYZ"

#~ msgid "ASE trajectory"
#~ msgstr "Trajectoria ASE"

#~ msgid "PDB file"
#~ msgstr "Archivo PDB"

#~ msgid "Gaussian cube file"
#~ msgstr "Archivo cube (Gaussian)"

#~ msgid "Python script"
#~ msgstr "Script de Python"

#~ msgid "VNL file"
#~ msgstr "Archivo VNL"

#~ msgid "Portable Network Graphics"
#~ msgstr "Archivo PNG"

#~ msgid "Persistence of Vision"
#~ msgstr "Archivo POV"

#~ msgid "Encapsulated PostScript"
#~ msgstr "Archivo EPS"

#~ msgid "FHI-aims geometry input"
#~ msgstr "Geometría de entrada (FHI-aims)"

#~ msgid "VASP geometry input"
#~ msgstr "Geometría de entrada (VASP)"

#~ msgid "cif file"
#~ msgstr "Archivo cif"

#~ msgid "Save current image only (#%d)"
#~ msgstr "Guardar solamente la imagen actual (#%d)"

#~ msgid "Slice: "
#~ msgstr "Trozo: "

#~ msgid "Help for slice ..."
#~ msgstr "Ayuda para trozar …"

#~ msgid "ase-gui INTERNAL ERROR: strange response in Save,"
#~ msgstr "ERROR INTERNO DE ase-gui: respuesta extraña en guardar,"

#~ msgid "Unknown output format!"
#~ msgstr "¡Formato de salida desconocido!"

#~ msgid "Use one of: %s"
#~ msgstr "Use uno de: %s"

#~ msgid "  %8.3f, %8.3f, %8.3f eV/Å\n"
#~ msgstr "  %8.3f, %8.3f, %8.3f eV/Å\n"

#~ msgid "%s (a=%.3f Å)"
#~ msgstr "%s (a=%.3f Å)"

#~ msgid "  %s: %s, Z=%i, %s"
#~ msgstr "  %s: %s, Z=%i, %s"

#~ msgid " #%d %s (%s): %.3f Å, %.3f Å, %.3f Å "
#~ msgstr " #%d %s (%s): %.3f Å, %.3f Å, %.3f Å "

#~ msgid " %s-%s: %.3f Å"
#~ msgstr " %s-%s: %.3f Å"

#~ msgid " %s-%s-%s: %.1f°, %.1f°, %.1f°"
#~ msgstr " %s-%s-%s: %.1f°, %.1f°, %.1f°"

#~ msgid "c:"
#~ msgstr "c:"

#~ msgid "FILE"
#~ msgstr "ARCHIVO"

#~ msgid "%prog [options] [file[, file2, ...]]"
#~ msgstr "%prog [opciones] [archivo[, archivo2, …]]"

#~ msgid "NUMBER"
#~ msgstr "NÚMERO"

#~ msgid "I"
#~ msgstr "I"

#~ msgid "Examples: \"-R -90x\", \"-R 90z,-30x\"."
#~ msgstr "Ejemplos: \"-R -90x\", \"-R 90z,-30x\"."

#~ msgid "EXPR"
#~ msgstr "EXPR"
