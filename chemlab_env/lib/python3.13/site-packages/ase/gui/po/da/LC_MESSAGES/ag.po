# Danish translations for ASE package
# <PERSON><PERSON> oversættelser for pakken ASE.
# Copyright (C) 2011-2022 ASE developers
# This file is distributed under the same license as the ASE package.
#
# Ask <PERSON><PERSON><PERSON> <<EMAIL>>, 2011-2022.
#
msgid ""
msgstr ""
"Project-Id-Version: ase-3.5.2\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-10-23 20:47-0400\n"
"PO-Revision-Date: 2024-10-23 20:51-0400\n"
"Last-Translator: Ask <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Danish <<EMAIL>>\n"
"Language: da\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: ../add.py:10
msgid "(selection)"
msgstr "(markering)"

#: ../add.py:16
msgid "Add atoms"
msgstr "Tilføj atomer"

#: ../add.py:17
msgid "Specify chemical symbol, formula, or filename."
msgstr "Angiv kemisk symbol, formel eller filnavn."

#: ../add.py:44
msgid "Add:"
msgstr "Tilføj:"

#: ../add.py:45
msgid "File ..."
msgstr "Fil …"

#: ../add.py:54
msgid "Coordinates:"
msgstr "Koordinater:"

#: ../add.py:56
msgid ""
"Coordinates are relative to the center of the selection, if any, else "
"absolute."
msgstr ""
"Koordinaterne er relative til markeringens midpunkt, hvis der er en "
"markering, og ellers absolutte."

#: ../add.py:58
msgid "Check positions"
msgstr "Kontrollér position"

#: ../add.py:59 ../nanoparticle.py:264
msgid "Add"
msgstr "Tilføj"

#. May show UI error
#: ../add.py:104
msgid "Cannot add atoms"
msgstr "Kan ikke tilføje atomer"

#: ../add.py:105
msgid "{} is neither atom, molecule, nor file"
msgstr "{} er hverken et atom, et molekyle eller en fil"

#: ../add.py:143
msgid "Bad positions"
msgstr "Ugyldige positioner"

#: ../add.py:144
msgid ""
"Atom would be less than 0.5 Å from an existing atom.  To override, uncheck "
"the check positions option."
msgstr ""
"Atomet ville befinde sig mindre end 0,5 Å fra et andet atom. Fjern kryds ved "
"indstillingen Kontrollér position for at tilsidesætte denne advarsel."

#. TRANSLATORS: This is a title of a window.
#: ../celleditor.py:49
msgid "Cell Editor"
msgstr "Celleredigering"

#: ../celleditor.py:53
msgid "A:"
msgstr "A:"

#: ../celleditor.py:53
msgid "||A||:"
msgstr "||A||:"

#: ../celleditor.py:54 ../celleditor.py:56 ../celleditor.py:58
msgid "periodic:"
msgstr "periodisk:"

#: ../celleditor.py:55
msgid "B:"
msgstr "B:"

#: ../celleditor.py:55
msgid "||B||:"
msgstr "||B||:"

#: ../celleditor.py:57
msgid "C:"
msgstr "C:"

#: ../celleditor.py:57
msgid "||C||:"
msgstr "||C||:"

#: ../celleditor.py:59
msgid "∠BC:"
msgstr "∠BC:"

#: ../celleditor.py:59
msgid "∠AC:"
msgstr "∠AC:"

#: ../celleditor.py:60
msgid "∠AB:"
msgstr "∠AB:"

#: ../celleditor.py:61
msgid "Scale atoms with cell:"
msgstr "Skalér atomer med celle:"

#: ../celleditor.py:62
msgid "Apply Vectors"
msgstr "Anvend vektorer"

#: ../celleditor.py:63
msgid "Apply Magnitudes"
msgstr "Anvend længder"

#: ../celleditor.py:64
msgid "Apply Angles"
msgstr "Anvend vinkler"

#: ../celleditor.py:65
msgid ""
"Pressing 〈Enter〉 as you enter values will automatically apply correctly"
msgstr ""
"Tryk 〈Enter〉 mens du indtaster værdier for automatisk at anvende disse"

#. TRANSLATORS: verb
#: ../celleditor.py:68
msgid "Center"
msgstr "Centrér"

#: ../celleditor.py:69
msgid "Wrap"
msgstr "Ombryd"

#: ../celleditor.py:70
msgid "Vacuum:"
msgstr "Vakuum:"

#: ../celleditor.py:71
msgid "Apply Vacuum"
msgstr "Anvend vakuum"

#: ../colors.py:17
msgid "Colors"
msgstr "Farver"

#: ../colors.py:19
msgid "Choose how the atoms are colored:"
msgstr "Vælg hvordan atomerne farves:"

#: ../colors.py:22
msgid "By atomic number, default \"jmol\" colors"
msgstr "Efter atomnummer; \"jmol\"-farver som standard"

#: ../colors.py:23
msgid "By tag"
msgstr "Efter mærke"

#: ../colors.py:24
msgid "By force"
msgstr "Efter kraft"

#: ../colors.py:25
msgid "By velocity"
msgstr "Efter hastighed"

#: ../colors.py:26
msgid "By initial charge"
msgstr "Efter startladning"

#: ../colors.py:27
msgid "By magnetic moment"
msgstr "Efter magnetisk moment"

#: ../colors.py:28
msgid "By number of neighbors"
msgstr "Efter antal naboer"

#: ../colors.py:98
msgid "cmap:"
msgstr "farver:"

#: ../colors.py:100
msgid "N:"
msgstr "N:"

#. XXX what are optimal allowed range and steps ?
#: ../colors.py:116
msgid "min:"
msgstr "min:"

#: ../colors.py:119
msgid "max:"
msgstr "maks:"

#: ../constraints.py:7
msgid "Constraints"
msgstr "Begrænsninger"

#: ../constraints.py:8 ../settings.py:12
msgid "Fix"
msgstr "Fasthold"

#: ../constraints.py:9 ../constraints.py:11
msgid "selected atoms"
msgstr "markerede atomer"

#: ../constraints.py:10
msgid "Release"
msgstr "Frigiv"

#: ../constraints.py:12 ../settings.py:16
msgid "Clear all constraints"
msgstr "Ryd alle begrænsninger"

#: ../graphs.py:9
msgid ""
"Symbols:\n"
"<c>e</c>: total energy\n"
"<c>epot</c>: potential energy\n"
"<c>ekin</c>: kinetic energy\n"
"<c>fmax</c>: maximum force\n"
"<c>fave</c>: average force\n"
"<c>R[n,0-2]</c>: position of atom number <c>n</c>\n"
"<c>d(n<sub>1</sub>,n<sub>2</sub>)</c>: distance between two atoms "
"<c>n<sub>1</sub></c> and <c>n<sub>2</sub></c>\n"
"<c>i</c>: current image number\n"
"<c>E[i]</c>: energy of image number <c>i</c>\n"
"<c>F[n,0-2]</c>: force on atom number <c>n</c>\n"
"<c>V[n,0-2]</c>: velocity of atom number <c>n</c>\n"
"<c>M[n]</c>: magnetic moment of atom number <c>n</c>\n"
"<c>A[0-2,0-2]</c>: unit-cell basis vectors\n"
"<c>s</c>: path length\n"
"<c>a(n1,n2,n3)</c>: angle between atoms <c>n<sub>1</sub></c>, <c>n<sub>2</"
"sub></c> and <c>n<sub>3</sub></c>, centered on <c>n<sub>2</sub></c>\n"
"<c>dih(n1,n2,n3,n4)</c>: dihedral angle between <c>n<sub>1</sub></c>, "
"<c>n<sub>2</sub></c>, <c>n<sub>3</sub></c> and <c>n<sub>4</sub></c>\n"
"<c>T</c>: temperature (K)"
msgstr ""
"Symboler:\n"
"<c>e</c>: total energi\n"
"<c>epot</c>: potentiel energi\n"
"<c>ekin</c>: kinetisk energi\n"
"<c>fmax</c>: maksimal kraft\n"
"<c>fave</c>: gennemsnitlig kraft\n"
"<c>R[n,0-2]</c>: position af atom nummer <c>n</c>\n"
"<c>d(n<sub>1</sub>,n<sub>1</sub>)</c>: afstand mellem to atomer <c>n<sub>1</"
"sub></c> og <c>n<sub>2</sub></c>\n"
"<c>i</c>: nuværende billedes nummer\n"
"<c>E[i]</c>: energi af billede nummer <c>i</c>\n"
"<c>F[n,0-2]</c>: kraft på atom nummer <c>n</c>\n"
"<c>V[n,0-2]</c>: hastighed af atom nummer <c>n</c>\n"
"<c>M[n]</c>: magnetisk moment af atom nummer <c>n</c>\n"
"<c>A[0-2,0-2]</c>: enhedscellevektorer\n"
"<c>s</c>: vejlængde\n"
"<c>a(n1,n2,n3)</c>: vinkel mellem atomerne <c>n<sub>1</sub></c>, <c>n<sub>2</"
"sub></c> and <c>n<sub>3</sub></c>, centreret på <c>n<sub>2</sub></c>\n"
"<c>dih(n1,n2,n3,n4)</c>: dihedral vinkel mellem <c>n<sub>1</sub></c>, "
"<c>n<sub>2</sub></c>, <c>n<sub>3</sub></c> og <c>n<sub>4</sub></c>\n"
"<c>T</c>: temperatur (K)"

#: ../graphs.py:40 ../graphs.py:42
msgid "Plot"
msgstr "Graf"

#: ../graphs.py:44
msgid "Save"
msgstr "Gem"

#: ../graphs.py:67
msgid "Save data to file ... "
msgstr "Gem data til fil …"

#: ../gui.py:208
msgid "Delete atoms"
msgstr "Slet atomer"

#: ../gui.py:209
msgid "Delete selected atoms?"
msgstr "Slet de valgte atomer?"

#. Subprocess probably crashed
#: ../gui.py:266
msgid "Failure in subprocess"
msgstr "Fejl i underproces"

#: ../gui.py:273
msgid "Plotting failed"
msgstr "Fejl ved tegning af graf"

#: ../gui.py:280
msgid "Images must have energies and forces, and atoms must not be stationary."
msgstr "Atomerne skal have energier og kræfter, og atomerne skal bevæge sig."

#: ../gui.py:293
msgid "Images must have energies and varying cell."
msgstr "Atomerne skal have energier og varierende celle."

#: ../gui.py:300
msgid "Requires 3D cell."
msgstr "Kræver 3D-celle."

#: ../gui.py:334
msgid "Quick Info"
msgstr "Hurtig info"

#: ../gui.py:471
msgid "_File"
msgstr "_Fil"

#: ../gui.py:472
msgid "_Open"
msgstr "_Åbn"

#: ../gui.py:473
msgid "_New"
msgstr "_Ny"

#: ../gui.py:474
msgid "_Save"
msgstr "_Gem"

#: ../gui.py:476
msgid "_Quit"
msgstr "_Afslut"

#: ../gui.py:478
msgid "_Edit"
msgstr "_Redigér"

#: ../gui.py:479
msgid "Select _all"
msgstr "Vælg _alle"

#: ../gui.py:480
msgid "_Invert selection"
msgstr "_Omvend markering"

#: ../gui.py:481
msgid "Select _constrained atoms"
msgstr "Vælg _fastgjorte atomer"

#: ../gui.py:482
msgid "Select _immobile atoms"
msgstr "Vælg _immobile atomer"

#. M('---'),
#: ../gui.py:484
msgid "_Cut"
msgstr "_Klip"

#: ../gui.py:485
msgid "_Copy"
msgstr "_Kopiér"

#: ../gui.py:486
msgid "_Paste"
msgstr "_Indsæt"

#: ../gui.py:488
msgid "Hide selected atoms"
msgstr "Skjul markerede atomer"

#: ../gui.py:489
msgid "Show selected atoms"
msgstr "Vis markerede atomer"

#: ../gui.py:491
msgid "_Modify"
msgstr "_Ændr"

#: ../gui.py:492
msgid "_Add atoms"
msgstr "_Tilføj atomer"

#: ../gui.py:493
msgid "_Delete selected atoms"
msgstr "_Slet markerede atomer"

#: ../gui.py:495
msgid "Edit _cell"
msgstr "Redigér _celle"

#: ../gui.py:497
msgid "_First image"
msgstr "Første _billede"

#: ../gui.py:498
msgid "_Previous image"
msgstr "_Forrige billede"

#: ../gui.py:499
msgid "_Next image"
msgstr "_Næste billede"

#: ../gui.py:500
msgid "_Last image"
msgstr "_Sidste billede"

#: ../gui.py:501
msgid "Append image copy"
msgstr "Tilføj kopi af billede"

#: ../gui.py:503
msgid "_View"
msgstr "_Vis"

#: ../gui.py:504
msgid "Show _unit cell"
msgstr "Vis _enhedscelle"

#: ../gui.py:506
msgid "Show _axes"
msgstr "Vis _akser"

#: ../gui.py:508
msgid "Show _bonds"
msgstr "Vis _bindinger"

#: ../gui.py:510
msgid "Show _velocities"
msgstr "Vis _hastigheder"

#: ../gui.py:512
msgid "Show _forces"
msgstr "Vis _kræfter"

#: ../gui.py:514
msgid "Show _Labels"
msgstr "Vis _etiketter"

#: ../gui.py:515
msgid "_None"
msgstr "_Ingen"

#: ../gui.py:516
msgid "Atom _Index"
msgstr "Atom_indeks"

#: ../gui.py:517
msgid "_Magnetic Moments"
msgstr "_Magnetiske momenter"

#. XXX check if exist
#: ../gui.py:518
msgid "_Element Symbol"
msgstr "_Kemisk symbol"

#: ../gui.py:519
msgid "_Initial Charges"
msgstr "_Startladninger"

#: ../gui.py:522
msgid "Quick Info ..."
msgstr "Hurtig info …"

#: ../gui.py:523
msgid "Repeat ..."
msgstr "Gentag …"

#: ../gui.py:524
msgid "Rotate ..."
msgstr "Rotér …"

#: ../gui.py:525
msgid "Colors ..."
msgstr "Farver …"

#. TRANSLATORS: verb
#: ../gui.py:527
msgid "Focus"
msgstr "Fokusér"

#: ../gui.py:528
msgid "Zoom in"
msgstr "Zoom ind"

#: ../gui.py:529
msgid "Zoom out"
msgstr "Zoom ud"

#: ../gui.py:530
msgid "Change View"
msgstr "Skift perspektiv"

#: ../gui.py:532
msgid "Reset View"
msgstr "Nulstil perspektiv"

#: ../gui.py:533
msgid "xy-plane"
msgstr "xy-plan"

#: ../gui.py:534
msgid "yz-plane"
msgstr "yz-plan"

#: ../gui.py:535
msgid "zx-plane"
msgstr "zx-plan"

#: ../gui.py:536
msgid "yx-plane"
msgstr "yx-plan"

#: ../gui.py:537
msgid "zy-plane"
msgstr "zy-plan"

#: ../gui.py:538
msgid "xz-plane"
msgstr "xz-plan"

#: ../gui.py:539
msgid "a2,a3-plane"
msgstr "a2,a3-plan"

#: ../gui.py:540
msgid "a3,a1-plane"
msgstr "a3,a1-plan"

#: ../gui.py:541
msgid "a1,a2-plane"
msgstr "a1,a2-plan"

#: ../gui.py:542
msgid "a3,a2-plane"
msgstr "a3,a2-plan"

#: ../gui.py:543
msgid "a1,a3-plane"
msgstr "a1,a3-plan"

#: ../gui.py:544
msgid "a2,a1-plane"
msgstr "a2,a1-plan"

#: ../gui.py:545
msgid "Settings ..."
msgstr "Indstillinger …"

#: ../gui.py:547
msgid "VMD"
msgstr "VMD"

#: ../gui.py:548
msgid "RasMol"
msgstr "RasMol"

#: ../gui.py:549
msgid "xmakemol"
msgstr "xmakemol"

#: ../gui.py:550
msgid "avogadro"
msgstr "avogadro"

#: ../gui.py:552
msgid "_Tools"
msgstr "_Værktøjer"

#: ../gui.py:553
msgid "Graphs ..."
msgstr "Grafer …"

#: ../gui.py:554
msgid "Movie ..."
msgstr "Film …"

#: ../gui.py:555
msgid "Constraints ..."
msgstr "Begrænsninger …"

# gemmer et billede af atomerne
#: ../gui.py:556
msgid "Render scene ..."
msgstr "Tegn struktur …"

#: ../gui.py:557
msgid "_Move selected atoms"
msgstr "_Flyt markerede atomer"

#: ../gui.py:558
msgid "_Rotate selected atoms"
msgstr "_Rotér markerede atomer"

#: ../gui.py:560
msgid "NE_B plot"
msgstr "NE_B-graf"

#: ../gui.py:561
msgid "B_ulk Modulus"
msgstr "K_ompressibilitetsmodul"

#: ../gui.py:562
msgid "Reciprocal space ..."
msgstr "Reciprokrum …"

#. TRANSLATORS: Set up (i.e. build) surfaces, nanoparticles, ...
#: ../gui.py:565
msgid "_Setup"
msgstr "_Byg"

#: ../gui.py:566
msgid "_Surface slab"
msgstr "_Overflade"

#: ../gui.py:567
msgid "_Nanoparticle"
msgstr "_Nanopartikel"

#: ../gui.py:569
msgid "Nano_tube"
msgstr "Nano_rør"

#. (_('_Calculate'),
#. [M(_('Set _Calculator'), self.calculator_window, disabled=True),
#. M(_('_Energy and Forces'), self.energy_window, disabled=True),
#. M(_('Energy Minimization'), self.energy_minimize_window,
#. disabled=True)]),
#: ../gui.py:577
msgid "_Help"
msgstr "_Hjælp"

#: ../gui.py:578
msgid "_About"
msgstr "_Om"

#: ../gui.py:582
msgid "Webpage ..."
msgstr "Webside …"

#. Host window will never be shown
#: ../images.py:259
msgid "Constraints discarded"
msgstr "Begrænsninger fjernet"

#: ../images.py:260
msgid "Constraints other than FixAtoms have been discarded."
msgstr "Begrænsninger på nær FixAtoms er blevet fjernet."

#: ../modify.py:20
msgid "No atoms selected!"
msgstr "Ingen atomer markeret!"

#: ../modify.py:23
msgid "Modify"
msgstr "Ændr"

#: ../modify.py:26
msgid "Change element"
msgstr "Skift grundstof"

#: ../modify.py:29
msgid "Tag"
msgstr "Mærke"

#: ../modify.py:31
msgid "Moment"
msgstr "Moment"

#: ../movie.py:10
msgid "Movie"
msgstr "Film"

#: ../movie.py:11
msgid "Image number:"
msgstr "Billednummer:"

#: ../movie.py:17
msgid "First"
msgstr "Første"

#: ../movie.py:18
msgid "Back"
msgstr "Tilbage"

#: ../movie.py:19
msgid "Forward"
msgstr "Fremad"

#: ../movie.py:20
msgid "Last"
msgstr "Sidste"

#: ../movie.py:22
msgid "Play"
msgstr "Afspil"

#: ../movie.py:23
msgid "Stop"
msgstr "Stop"

#. TRANSLATORS: This function plays an animation forwards and backwards
#. alternatingly, e.g. for displaying vibrational movement
#: ../movie.py:27
msgid "Rock"
msgstr "Pendul"

#: ../movie.py:40
msgid " Frame rate: "
msgstr " Billedrate: "

#: ../movie.py:40
msgid " Skip frames: "
msgstr " Overspring billeder: "

#. Delayed imports:
#. ase.cluster.data
#: ../nanoparticle.py:20
msgid ""
"Create a nanoparticle either by specifying the number of layers, or using "
"the\n"
"Wulff construction.  Please press the [Help] button for instructions on how "
"to\n"
"specify the directions.\n"
"WARNING: The Wulff construction currently only works with cubic crystals!\n"
msgstr ""
"Opret en nanopartikel enten ved at angive antallet af lag, eller ved\n"
"brug af Wulffkonstruktion.  Tryk på knappen [Hjælp] for at få\n"
"instruktioner om hvordan retninger angives.\n"
"\n"
"ADVARSEL: Wulffkonstruktion fungerer i øjeblikket kun med kubiske "
"krystaller!\n"

#: ../nanoparticle.py:27
#, python-brace-format
msgid ""
"\n"
"The nanoparticle module sets up a nano-particle or a cluster with a given\n"
"crystal structure.\n"
"\n"
"1) Select the element, the crystal structure and the lattice constant(s).\n"
"   The [Get structure] button will find the data for a given element.\n"
"\n"
"2) Choose if you want to specify the number of layers in each direction, or "
"if\n"
"   you want to use the Wulff construction.  In the latter case, you must\n"
"   specify surface energies in each direction, and the size of the cluster.\n"
"\n"
"How to specify the directions:\n"
"------------------------------\n"
"\n"
"First time a direction appears, it is interpreted as the entire family of\n"
"directions, i.e. (0,0,1) also covers (1,0,0), (-1,0,0) etc.  If one of "
"these\n"
"directions is specified again, the second specification overrules that "
"specific\n"
"direction.  For this reason, the order matters and you can rearrange the\n"
"directions with the [Up] and [Down] keys.  You can also add a new "
"direction,\n"
"remember to press [Add] or it will not be included.\n"
"\n"
"Example: (1,0,0) (1,1,1), (0,0,1) would specify the {100} family of "
"directions,\n"
"the {111} family and then the (001) direction, overruling the value given "
"for\n"
"the whole family of directions.\n"
msgstr ""
"\n"
"Nanopartikelmodulet konstruerer en nanopartikel eller klynge med en\n"
"given krystalstruktur.\n"
"\n"
"1) Vælg grundstoffet, krystalstrukturen og gitterkonstanterne.\n"
"   Knappen [Hent struktur] vil finde data for et givet grundstof.\n"
"\n"
"2) Vælg om du vil angive antallet af lag i hver retning, eller om du\n"
"   vil benytte en Wulffkonstruktion.  I sidstnævnte tilfælde skal du\n"
"   angive overfladeenergier for hver retning samt klyngens størrelse.\n"
"\n"
"Hvordan retninger angives\n"
"-------------------------\n"
"\n"
"Første gang en retning dukker op, fortolkes den som en hel familie af\n"
"retninger – f.eks. dækker (0,0,1) også (1,0,0), (-1,0,0) osv.  Hvis en af\n"
"disse retninger angives igen, vil anden specifikation særligt gælde\n"
"denne specifikke retning.  Derfor er rækkefølgen ikke ligegyldig, og du kan\n"
"omarrangere retningerne med knapperne [Op] og [Ned].  Du kan også tilføje in "
"ny retning – husk at trykke [Tilføj], eller den vil ikke blive inkluderet.\n"
"\n"
"Eksempel: (1,0,0), (1,1,1), (0,0,1) ville angive familien {100} af "
"retninger,\n"
"{111}-familien og så (001) retningen, der tilsidesætter værdien givet til\n"
"selve familien af retninger.\n"

#: ../nanoparticle.py:87
msgid "Face centered cubic (fcc)"
msgstr "Face centered cubic (fcc)"

#: ../nanoparticle.py:88
msgid "Body centered cubic (bcc)"
msgstr "Body centered cubic (bcc)"

#: ../nanoparticle.py:89
msgid "Simple cubic (sc)"
msgstr "Simpel kubisk (sc)"

#: ../nanoparticle.py:90
msgid "Hexagonal closed-packed (hcp)"
msgstr "Heksagonal tætpakket (hcp)"

#: ../nanoparticle.py:91
msgid "Graphite"
msgstr "Grafit"

#: ../nanoparticle.py:136
msgid "Nanoparticle"
msgstr "Nanopartikel"

#: ../nanoparticle.py:140
msgid "Get structure"
msgstr "Hent struktur"

#: ../nanoparticle.py:155 ../surfaceslab.py:68
msgid "Structure:"
msgstr "Struktur:"

#: ../nanoparticle.py:159
msgid "Lattice constant:  a ="
msgstr "Gitterkonstant:  a ="

#: ../nanoparticle.py:163
msgid "Layer specification"
msgstr "Lagspecifikation"

#: ../nanoparticle.py:163
msgid "Wulff construction"
msgstr "Wulffkonstruktion"

#: ../nanoparticle.py:166
msgid "Method: "
msgstr "Metode: "

#: ../nanoparticle.py:174
msgid "Add new direction:"
msgstr "Tilføj ny retning:"

#. Information
#: ../nanoparticle.py:180
msgid "Information about the created cluster:"
msgstr "Information om den konstruerede klynge:"

#: ../nanoparticle.py:181
msgid "Number of atoms: "
msgstr "Antal atomer: "

#: ../nanoparticle.py:183
msgid "   Approx. diameter: "
msgstr "   Diameter omtrent: "

#: ../nanoparticle.py:192
msgid "Automatic Apply"
msgstr "Anvend automatisk"

#: ../nanoparticle.py:195 ../nanotube.py:49
msgid "Creating a nanoparticle."
msgstr "Konstruktion af nanopartikel."

#: ../nanoparticle.py:197 ../nanotube.py:50 ../surfaceslab.py:81
msgid "Apply"
msgstr "Anvend"

#: ../nanoparticle.py:198 ../nanotube.py:51 ../surfaceslab.py:82
msgid "OK"
msgstr "OK"

#: ../nanoparticle.py:227
msgid "Up"
msgstr "Op"

#: ../nanoparticle.py:228
msgid "Down"
msgstr "Ned"

#: ../nanoparticle.py:229
msgid "Delete"
msgstr "Slet"

#: ../nanoparticle.py:271
msgid "Number of atoms"
msgstr "Antal atomer"

#: ../nanoparticle.py:271
msgid "Diameter"
msgstr "Diameter"

#: ../nanoparticle.py:279
msgid "above  "
msgstr "over  "

#: ../nanoparticle.py:279
msgid "below  "
msgstr "under  "

#: ../nanoparticle.py:279
msgid "closest  "
msgstr "tættest på  "

#: ../nanoparticle.py:282
msgid "Smaller"
msgstr "Mindre"

#: ../nanoparticle.py:283
msgid "Larger"
msgstr "Større"

#: ../nanoparticle.py:284
msgid "Choose size using:"
msgstr "Vælg størrelse med:"

#: ../nanoparticle.py:286
msgid "atoms"
msgstr "atomer"

#: ../nanoparticle.py:287
msgid "Å³"
msgstr "Å³"

#: ../nanoparticle.py:289
msgid "Rounding: If exact size is not possible, choose the size:"
msgstr "Afrunding: Hvis eksakt størrelse ikke kan opnås, så vælg størrelsen:"

#: ../nanoparticle.py:317
msgid "Surface energies (as energy/area, NOT per atom):"
msgstr "Overfladeenergier (som energi/areal, IKKE per atom):"

#: ../nanoparticle.py:319
msgid "Number of layers:"
msgstr "Antal lag:"

#: ../nanoparticle.py:347
msgid "At least one index must be non-zero"
msgstr "Mindst et indeks skal være forskelligt fra nul"

#: ../nanoparticle.py:350
msgid "Invalid hexagonal indices"
msgstr "Ugyldige heksagonale indeks"

#: ../nanoparticle.py:415
msgid "Unsupported or unknown structure"
msgstr "Uunderstøttet eller ukendt struktur"

#: ../nanoparticle.py:416
#, python-brace-format
msgid "Element = {0}, structure = {1}"
msgstr "Grundstof = {0}, struktur = {1}"

#: ../nanoparticle.py:530 ../nanotube.py:82 ../surfaceslab.py:221
msgid "No valid atoms."
msgstr "Ingen gyldige atomer."

#: ../nanoparticle.py:531 ../nanotube.py:83 ../surfaceslab.py:222
#: ../widgets.py:93
msgid "You have not (yet) specified a consistent set of parameters."
msgstr "Du har (endnu) ikke angivet konsistente parametre."

#: ../nanotube.py:10
msgid ""
"Set up a Carbon nanotube by specifying the (n,m) roll-up vector.\n"
"Please note that m <= n.\n"
"\n"
"Nanotubes of other elements can be made by specifying the element\n"
"and bond length."
msgstr ""
"Byg et kulstofnanorør ved at angive uprulningsvektoren (n, m).\n"
"Bemærk at m <= n.\n"
"\n"
"Nanorør af andre grundstoffer kan bygges ved at angive hvilket grundstof,\n"
"samt bindingslængde."

#: ../nanotube.py:23
#, python-brace-format
msgid ""
"{natoms} atoms, diameter: {diameter:.3f} Å, total length: {total_length:.3f} "
"Å"
msgstr ""
"{natoms} atomer, diameter: {diameter:.3f} Å, samlet længde: "
"{total_length:.3f} Å"

#: ../nanotube.py:38
msgid "Nanotube"
msgstr "Nanorør"

#: ../nanotube.py:41
msgid "Bond length: "
msgstr "Bindingslængde: "

#: ../nanotube.py:43
msgid "Å"
msgstr "Å"

#: ../nanotube.py:44
msgid "Select roll-up vector (n,m) and tube length:"
msgstr "Vælg oprulningsvektor (n,m) og rørlængde:"

#: ../nanotube.py:47
msgid "Length:"
msgstr "Længde:"

#: ../quickinfo.py:28
msgid "This frame has no atoms."
msgstr "Dette billede har ingen atomer."

#: ../quickinfo.py:33
msgid "Single image loaded."
msgstr "Enkelt billede indlæst."

#: ../quickinfo.py:35
msgid "Image {} loaded (0–{})."
msgstr "Billede {} indlæst (0–{})."

#: ../quickinfo.py:37
msgid "Number of atoms: {}"
msgstr "Antal atomer: {}"

#: ../quickinfo.py:47
msgid "Unit cell [Å]:"
msgstr "Enhedscelle [Å]:"

#: ../quickinfo.py:49
msgid "no"
msgstr "nej"

#: ../quickinfo.py:49
msgid "yes"
msgstr "ja"

#. TRANSLATORS: This has the form Periodic: no, no, yes
#: ../quickinfo.py:52
msgid "Periodic: {}, {}, {}"
msgstr "Periodisk: {}, {}, {}"

#: ../quickinfo.py:57
msgid "Lengths [Å]: {:.3f}, {:.3f}, {:.3f}"
msgstr "Længder [Å]: {:.3f}, {:.3f}, {:.3f}"

#: ../quickinfo.py:58
msgid "Angles: {:.1f}°, {:.1f}°, {:.1f}°"
msgstr "Vinkler: {:.1f}°, {:.1f}°, {:.1f}°"

#: ../quickinfo.py:61
msgid "Volume: {:.3f} Å³"
msgstr "Volumen: {:.3f} Å³"

#: ../quickinfo.py:67
msgid "Unit cell is fixed."
msgstr "Enhedscelle fastholdes."

#: ../quickinfo.py:69
msgid "Unit cell varies."
msgstr "Enhedscelle varierer."

#: ../quickinfo.py:75
msgid "Could not recognize the lattice type"
msgstr "Kunne ikke genkende gittertype"

#: ../quickinfo.py:77
msgid "Unexpected error determining lattice type"
msgstr "Uventet fejl ved bestemmelse af gittertype"

#: ../quickinfo.py:79
msgid ""
"Reduced Bravais lattice:\n"
"{}"
msgstr ""
"Reduceret Bravaisgitter:\n"
"{}"

#: ../quickinfo.py:107
msgid "Calculator: {} (cached)"
msgstr "Beregner: {} (gemt)"

#: ../quickinfo.py:109
msgid "Calculator: {} (attached)"
msgstr "Beregner: {} (tilknyttet)"

#: ../quickinfo.py:116
msgid "Energy: {:.3f} eV"
msgstr "Energi: {:.3f} eV"

#: ../quickinfo.py:121
msgid "Max force: {:.3f} eV/Å"
msgstr "Maks. kraft: {:.3f} eV/Å"

#: ../quickinfo.py:125
msgid "Magmom: {:.3f} µ"
msgstr "Magmom: {:.3f} µ"

# gemmer et billede af atomerne
#: ../render.py:20
msgid "Render current view in povray ... "
msgstr "Tegn nuværende struktur i povray …"

#: ../render.py:21
#, python-format
msgid "Rendering %d atoms."
msgstr "Tegner %d atomer."

#: ../render.py:26
msgid "Size"
msgstr "Størrelse"

#: ../render.py:31
msgid "Line width"
msgstr "Linjebredde"

#: ../render.py:32
msgid "Ångström"
msgstr "Ångström"

#: ../render.py:34
msgid "Render constraints"
msgstr "Tegn begrænsninger"

#: ../render.py:35
msgid "Render unit cell"
msgstr "Tegn _enhedscelle"

#: ../render.py:41
msgid "Output basename: "
msgstr "Basisnavn for output: "

#: ../render.py:43
msgid "POVRAY executable"
msgstr "POVRAY-programfil"

#: ../render.py:45
msgid "Output filename: "
msgstr "Outputfilnavn: "

#: ../render.py:50
msgid "Atomic texture set:"
msgstr "Atomtekstursæt:"

#: ../render.py:57
msgid "Camera type: "
msgstr "Kameratype: "

#: ../render.py:58
msgid "Camera distance"
msgstr "Kameraafstand"

#. render current frame/all frames
#: ../render.py:61
msgid "Render current frame"
msgstr "Tegn det aktuelle billede"

#: ../render.py:62
msgid "Render all frames"
msgstr "Tegn alle billeder"

#: ../render.py:67
msgid "Run povray"
msgstr "Kør povray"

#: ../render.py:68
msgid "Keep povray files"
msgstr "Behold povray-filer"

#: ../render.py:69
msgid "Show output window"
msgstr "Vis outputvindue"

#: ../render.py:70
msgid "Transparent background"
msgstr "Gennemsigtig baggrund"

#: ../render.py:74
msgid "Render"
msgstr "Tegn"

#: ../repeat.py:7
msgid "Repeat"
msgstr "Gentag"

#: ../repeat.py:8
msgid "Repeat atoms:"
msgstr "Gentag atomer:"

#: ../repeat.py:12
msgid "Set unit cell"
msgstr "Angiv enhedscelle"

#: ../rotate.py:11
msgid "Rotate"
msgstr "Rotér"

#: ../rotate.py:12
msgid "Rotation angles:"
msgstr "Rotationsvinkler:"

#: ../rotate.py:16
msgid "Update"
msgstr "Opdatér"

#: ../rotate.py:17
msgid ""
"Note:\n"
"You can rotate freely\n"
"with the mouse, by holding\n"
"down mouse button 2."
msgstr ""
"Bemærk:\n"
"Du kan frit rotere med\n"
"musen ved at holde\n"
"musetast 2 nede."

#: ../save.py:15
msgid ""
"Append name with \"@n\" in order to write image\n"
"number \"n\" instead of the current image. Append\n"
"\"@start:stop\" or \"@start:stop:step\" if you want\n"
"to write a range of images. You can leave out\n"
"\"start\" and \"stop\" so that \"name@:\" will give\n"
"you all images. Negative numbers count from the\n"
"last image. Examples: \"name@-1\": last image,\n"
"\"name@-2:\": last two."
msgstr ""
"Tilføj \"@n\" i navnet for at skrive billede nummer \"n\" frem for\n"
"nuværende billede.  Tilføj \"@start:stop\" eller \"@start:stop:trin\" hvis\n"
"du vil skrive et interval af billeder.  Du kan udelade \"start\" og\n"
"\"stop\", så \"navn@:\" vil give dig alle billeder.  Negative tal regnes\n"
"fra sidste billede. Eksempler: \"navn@-1\": sidste billede, \"name@-2:\": de "
"to\n"
"sidste."

#: ../save.py:27
msgid "Save ..."
msgstr "Gem …"

#: ../save.py:85 ../ui.py:32
msgid "Error"
msgstr "Fejl"

#: ../settings.py:8
msgid "Settings"
msgstr "Indstillinger"

#. Constraints
#: ../settings.py:11
msgid "Constraints:"
msgstr "Begrænsninger:"

#: ../settings.py:14
msgid "release"
msgstr "frigiv"

#: ../settings.py:15 ../settings.py:23
msgid " selected atoms"
msgstr " markerede atomer"

#. Visibility
#: ../settings.py:19
msgid "Visibility:"
msgstr "Synlighed:"

#: ../settings.py:20
msgid "Hide"
msgstr "Skjul"

#: ../settings.py:22
msgid "show"
msgstr "vis"

#: ../settings.py:24
msgid "View all atoms"
msgstr "Vis alle atomer"

#. Miscellaneous
#: ../settings.py:27
msgid "Miscellaneous:"
msgstr "Diverse:"

#: ../settings.py:30
msgid "Scale atomic radii:"
msgstr "Skalér atomradier:"

#: ../settings.py:37
msgid "Scale force vectors:"
msgstr "Skalér kraftvektorer:"

#: ../settings.py:44
msgid "Scale velocity vectors:"
msgstr "Skalér hastighedsvektorer:"

#: ../status.py:80
#, python-format
msgid " tag=%(tag)s"
msgstr " mærke=%(tag)s"

#. TRANSLATORS: mom refers to magnetic moment
#: ../status.py:84
msgid " mom={:1.2f}"
msgstr " mom={:1.2f}"

#: ../status.py:88
msgid " q={:1.2f}"
msgstr " q={:1.2f}"

#: ../status.py:126
msgid "dihedral"
msgstr "dihedral"

#: ../surfaceslab.py:9
msgid ""
"  Use this dialog to create surface slabs.  Select the element by\n"
"writing the chemical symbol or the atomic number in the box.  Then\n"
"select the desired surface structure.  Note that some structures can\n"
"be created with an othogonal or a non-orthogonal unit cell, in these\n"
"cases the non-orthogonal unit cell will contain fewer atoms.\n"
"\n"
"  If the structure matches the experimental crystal structure, you can\n"
"look up the lattice constant, otherwise you have to specify it\n"
"yourself."
msgstr ""
"  Brug denne dialog til at oprette overflader.  Vælg grundstoffet ved at \n"
"skrive det kemiske symbol eller atomnummeret i boksen. Vælg så den ønskede\n"
"overfladestruktur.  Bemærk at visse strukturer kan oprettes med både en\n"
"ortogonal og en ikke-ortogonal enhedscelle; i disse tilfælde vil\n"
"den ikke-ortogonale enhedscelle indeholde færre atomer.\n"
"\n"
"  Hvis strukturen svarer til den eksperimentelle krystalstruktur, kan\n"
"du slå gitterkonstanten op. Ellers skal du angive den selv."

#. Name, structure, orthogonal, function
#: ../surfaceslab.py:21
msgid "FCC(100)"
msgstr "FCC(100)"

#: ../surfaceslab.py:21 ../surfaceslab.py:22 ../surfaceslab.py:23
#: ../surfaceslab.py:24
msgid "fcc"
msgstr "fcc"

#: ../surfaceslab.py:22
msgid "FCC(110)"
msgstr "FCC(110)"

#: ../surfaceslab.py:23 ../surfaceslab.py:171
msgid "FCC(111)"
msgstr "FCC(111)"

#: ../surfaceslab.py:24 ../surfaceslab.py:174
msgid "FCC(211)"
msgstr "FCC(211)"

#: ../surfaceslab.py:25
msgid "BCC(100)"
msgstr "BCC(100)"

#: ../surfaceslab.py:25 ../surfaceslab.py:26 ../surfaceslab.py:27
msgid "bcc"
msgstr "bcc"

#: ../surfaceslab.py:26 ../surfaceslab.py:168
msgid "BCC(110)"
msgstr "BCC(110)"

#: ../surfaceslab.py:27 ../surfaceslab.py:165
msgid "BCC(111)"
msgstr "BCC(111)"

#: ../surfaceslab.py:28 ../surfaceslab.py:178
msgid "HCP(0001)"
msgstr "HCP(0001)"

#: ../surfaceslab.py:28 ../surfaceslab.py:29 ../surfaceslab.py:132
#: ../surfaceslab.py:188
msgid "hcp"
msgstr "hcp"

#: ../surfaceslab.py:29 ../surfaceslab.py:181
msgid "HCP(10-10)"
msgstr "HCP(10–10)"

#: ../surfaceslab.py:30
msgid "DIAMOND(100)"
msgstr "DIAMANT(100)"

#: ../surfaceslab.py:30 ../surfaceslab.py:31
msgid "diamond"
msgstr "diamant"

#: ../surfaceslab.py:31
msgid "DIAMOND(111)"
msgstr "DIAMANT(111)"

#: ../surfaceslab.py:53
msgid "Get from database"
msgstr "Hent fra database"

#: ../surfaceslab.py:65
msgid "Surface"
msgstr "Overflade"

#: ../surfaceslab.py:69
msgid "Orthogonal cell:"
msgstr "Ortogonal celle:"

#: ../surfaceslab.py:70
msgid "Lattice constant:"
msgstr "Gitterkonstant:"

#: ../surfaceslab.py:71
msgid "\ta"
msgstr "\ta"

#: ../surfaceslab.py:72
msgid "\tc"
msgstr "\tc"

#: ../surfaceslab.py:73
msgid "Size:"
msgstr "Størrelse:"

#: ../surfaceslab.py:74
msgid "\tx: "
msgstr "\tx: "

#: ../surfaceslab.py:74 ../surfaceslab.py:75 ../surfaceslab.py:76
msgid " unit cells"
msgstr " enhedsceller"

#: ../surfaceslab.py:75
msgid "\ty: "
msgstr "\ty: "

#: ../surfaceslab.py:76
msgid "\tz: "
msgstr "\tz: "

#: ../surfaceslab.py:77
msgid "Vacuum: "
msgstr "Vakuum: "

#. TRANSLATORS: This is a title of a window.
#: ../surfaceslab.py:80
msgid "Creating a surface."
msgstr "Oprettelse af overflade."

#. TRANSLATORS: E.g. "... assume fcc crystal structure for Au"
#: ../surfaceslab.py:108
msgid "Error: Reference values assume {} crystal structure for {}!"
msgstr "Fejl: Referenceværdierne antager {}-krystralstruktur for {}!"

#: ../surfaceslab.py:162
msgid "Please enter an even value for orthogonal cell"
msgstr "Indtast venligst en lige værdi for ortogonal celle"

#: ../surfaceslab.py:175
msgid "Please enter a value divisible by 3 for orthogonal cell"
msgstr "Indtast venligst en værdi delelig med 3 for ortogonal celle"

#: ../surfaceslab.py:195
msgid " Vacuum: {} Å."
msgstr " Vakuum: {} Å."

#. TRANSLATORS: e.g. "Au fcc100 surface with 2 atoms."
#. or "Au fcc100 surface with 2 atoms. Vacuum: 5 Å."
#: ../surfaceslab.py:203
#, python-brace-format
msgid "{symbol} {surf} surface with one atom.{vacuum}"
msgid_plural "{symbol} {surf} surface with {natoms} atoms.{vacuum}"
msgstr[0] "{symbol} {surf}-overflade med {natoms} atom.{vacuum}"
msgstr[1] "{symbol} {surf}-overflade med {natoms} atomer.{vacuum}"

#: ../ui.py:39
msgid "Version"
msgstr "Version"

#: ../ui.py:40
msgid "Web-page"
msgstr "Webside"

#: ../ui.py:41
msgid "About"
msgstr "Om"

#: ../ui.py:47 ../ui.py:51 ../widgets.py:12
msgid "Help"
msgstr "Hjælp"

#: ../ui.py:560
msgid "Open ..."
msgstr "Åbn …"

#: ../ui.py:567
msgid "Automatic"
msgstr "Automatisk"

#: ../ui.py:585
msgid "Choose parser:"
msgstr "Vælg fortolker:"

#: ../ui.py:591
msgid "Read error"
msgstr "Læsefejl"

#: ../ui.py:592
#, python-brace-format
msgid "Could not read {filename}: {err}"
msgstr "Kunne ikke læse {filename}: {err}"

#: ../view.py:130
msgid "one image loaded"
msgid_plural "{} images loaded"
msgstr[0] "et billede indlæst"
msgstr[1] "{} billeder indlæst"

#: ../widgets.py:10
msgid "Element:"
msgstr "Grundstof:"

#: ../widgets.py:24
msgid "Enter a chemical symbol or the atomic number."
msgstr "Indtast kemisk symbol eller atomnummer."

#. Title of a popup window
#: ../widgets.py:26
msgid "Info"
msgstr "Info"

#: ../widgets.py:56
msgid "No element specified!"
msgstr "Intet grundstof angivet!"

#: ../widgets.py:75
msgid "ERROR: Invalid element!"
msgstr "FEJL: ugyldigt grundstof!"

#: ../widgets.py:92
msgid "No Python code"
msgstr "Ingen Pythonkode"

#~ msgid "Get molecule:"
#~ msgstr "Hent molekyle:"

#~ msgid "Constrain"
#~ msgstr "Begræns"

#~ msgid "immobile atoms"
#~ msgstr "immobile atomer"

#~ msgid "Unconstrain"
#~ msgstr "Fjern begrænsninger"

#~ msgid "Clear constraints"
#~ msgstr "Ryd begrænsninger"

#~ msgid ""
#~ "Set up a graphene sheet or a graphene nanoribbon.  A nanoribbon may\n"
#~ "optionally be saturated with hydrogen (or another element)."
#~ msgstr ""
#~ "Konstruér et grafénlag eller et grafénnanobånd.  Et nanobånd kan "
#~ "eventuelt\n"
#~ "mættes med hydrogen (eller et andet grundstof)."

#~ msgid " %(natoms)i atoms: %(symbols)s, Volume: %(volume).3f A<sup>3</sup>"
#~ msgstr ""
#~ " %(natoms)i atomer: %(symbols)s, Volumen: %(volume).3f A<sup>3</sup>"

#~ msgid "Graphene"
#~ msgstr "Grafén"

#~ msgid "Structure: "
#~ msgstr "Struktur: "

#~ msgid "Infinite sheet"
#~ msgstr "Uendeligt lag"

#~ msgid "Unsaturated ribbon"
#~ msgstr "Umættet bånd"

#~ msgid "Saturated ribbon"
#~ msgstr "Mættet bånd"

#~ msgid "Orientation: "
#~ msgstr "Orientering: "

#~ msgid "zigzag"
#~ msgstr "zigzag"

#~ msgid "armchair"
#~ msgstr "lænestol"

#~ msgid "  Bond length: "
#~ msgstr "  Bindingslængde: "

#~ msgid "Saturation: "
#~ msgstr "Mætning: "

#~ msgid "H"
#~ msgstr "H"

#~ msgid "Width: "
#~ msgstr "Bredde: "

#~ msgid "  Length: "
#~ msgstr "  Længde: "

#~ msgid "  No element specified!"
#~ msgstr "  Intet grundstof angivet!"

#~ msgid "Please specify a consistent set of atoms. "
#~ msgstr "Angiv venligst en konsistent samling atomer. "

#~ msgid "Expert mode ..."
#~ msgstr "Eksperttilstand …"

#~ msgid "_Bulk Crystal"
#~ msgstr "_Krystal"

# I dette tilfælde er constrain = fastgøre
#~ msgid "Constrain immobile atoms"
#~ msgstr "Fastgør immobile atomer"

#~ msgid "Green"
#~ msgstr "Grøn"

#~ msgid "Yellow"
#~ msgstr "Gul"

#~ msgid "_Move atoms"
#~ msgstr "_Flyt atomer"

#~ msgid "_Rotate atoms"
#~ msgstr "_Rotér atomer"

#~ msgid ""
#~ "    Textures can be used to highlight different parts of\n"
#~ "    an atomic structure. This window applies the default\n"
#~ "    texture to the entire structure and optionally\n"
#~ "    applies a different texture to subsets of atoms that\n"
#~ "    can be selected using the mouse.\n"
#~ "    An alternative selection method is based on a boolean\n"
#~ "    expression in the entry box provided, using the\n"
#~ "    variables x, y, z, or Z. For example, the expression\n"
#~ "    Z == 11 and x > 10 and y > 10\n"
#~ "    will mark all sodium atoms with x or coordinates\n"
#~ "    larger than 10. In either case, the button labeled\n"
#~ "    `Create new texture from selection` will enable\n"
#~ "    to change the attributes of the current selection.\n"
#~ "    "
#~ msgstr ""
#~ "    Teksturer kan bruges til at fremhæve forskellige dele af en\n"
#~ "    atomar struktur. Dette vindue anvender standardteksturen på hele\n"
#~ "    strukturen, og anvender valgfrit en anden tekstur til bestemte\n"
#~ "    atomer som kan markeres med musen.\n"
#~ "    En alternativ markeringsmetode baseret på booleske udtryk\n"
#~ "    i et tekstfelt kan bruges med variabelnavnene x, y, z eller Z.\n"
#~ "    For eksempel vil udtrykket Z == 11 and x > 10 and y > 10\n"
#~ "    markere alle natriumatomer med x- eller y-koordinater\n"
#~ "    større end 10. I begge tilfælde vil knappen med teksten\n"
#~ "    \"Opret ny tekstur fra markering\" tillade ændring af\n"
#~ "    attributterne for den nuværende markering.\n"
#~ "    "

#~ msgid "Width"
#~ msgstr "Bredde"

#~ msgid "     Height"
#~ msgstr "     Højde"

#~ msgid "Angstrom           "
#~ msgstr "Ångström           "

#~ msgid "Set"
#~ msgstr "Angiv"

#~ msgid "               Filename: "
#~ msgstr "               Filnavn: "

#~ msgid " Default texture for atoms: "
#~ msgstr " Standardtekstur for atomer: "

#~ msgid "    transparency: "
#~ msgstr "    gennemsigtighed: "

#~ msgid "Define atom selection for new texture:"
#~ msgstr "Definér atommarkering til ny tekstur:"

#~ msgid "Select"
#~ msgstr "Vælg"

#~ msgid "Create new texture from selection"
#~ msgstr "Opret ny tekstur fra markering"

#~ msgid "Help on textures"
#~ msgstr "Hjælp til teksturer"

#~ msgid "     Camera distance"
#~ msgstr "     Kameraafstand"

#~ msgid "Render all %d frames"
#~ msgstr "Tegn alle %d billeder"

#~ msgid "Run povray       "
#~ msgstr "Kør povray       "

#~ msgid "Keep povray files       "
#~ msgstr "Behold povray-filer       "

#~ msgid "  transparency: "
#~ msgstr "  gennemsigtighed: "

#~ msgid ""
#~ "Can not create new texture! Must have some atoms selected to create a new "
#~ "material!"
#~ msgstr ""
#~ "Kan ikke oprette ny tekstur! Der skal være atomer markeret for at kunne "
#~ "oprette nyt materiale!"

#~ msgid "Output:"
#~ msgstr "Uddata:"

#~ msgid "Save output"
#~ msgstr "Gem uddata"

#~ msgid "Potential energy and forces"
#~ msgstr "Potentiel energi og kræfter"

#~ msgid "Calculate potential energy and the force on all atoms"
#~ msgstr "Beregn potentiel energi og kræfter på alle atomer"

#~ msgid "Write forces on the atoms"
#~ msgstr "Skriv kræfter på atomerne"

#~ msgid "Potential Energy:\n"
#~ msgstr "Potentiel energi:\n"

#~ msgid "  %8.2f eV\n"
#~ msgstr "  %8.2f eV\n"

#~ msgid ""
#~ "  %8.4f eV/atom\n"
#~ "\n"
#~ msgstr ""
#~ "  %8.4f eV/atom\n"
#~ "\n"

#~ msgid "Forces:\n"
#~ msgstr "Kræfter:\n"

#~ msgid "Clear"
#~ msgstr "Ryd"

#~ msgid "_Calculate"
#~ msgstr "_Beregn"

#~ msgid "Set _Calculator"
#~ msgstr "Angiv _beregner"

#~ msgid "_Energy and Forces"
#~ msgstr "_Energi og kræfter"

#~ msgid "Energy Minimization"
#~ msgstr "Energiminimering"

#~ msgid " (rerun simulation)"
#~ msgstr " (kør simulation igen)"

#~ msgid " (continue simulation)"
#~ msgstr " (fortsæt simulation)"

#~ msgid "Select starting configuration:"
#~ msgstr "Vælg startkonfiguration:"

#~ msgid "There are currently %i configurations loaded."
#~ msgstr "Der er i øjeblikket indlæst %i konfigurationer."

#~ msgid "Choose which one to use as the initial configuration"
#~ msgstr "Vælg hvilken, der skal bruges som begyndelseskonfiguration"

#~ msgid "The first configuration %s."
#~ msgstr "Første konfiguration %s."

#~ msgid "Configuration number "
#~ msgstr "Konfiguration nummer "

#~ msgid "The last configuration %s."
#~ msgstr "Sidste konfiguration %s."

#~ msgid "Run"
#~ msgstr "Kør"

#~ msgid "No calculator: Use Calculate/Set Calculator on the menu."
#~ msgstr "Ingen beregner: Brug Beregn/Angiv beregner i menuen."

#~ msgid "No atoms present"
#~ msgstr "Ingen atomer til stede"

#~ msgid ""
#~ "  Use this dialog to create crystal lattices. First select the "
#~ "structure,\n"
#~ "  either from a set of common crystal structures, or by space group "
#~ "description.\n"
#~ "  Then add all other lattice parameters.\n"
#~ "\n"
#~ "  If an experimental crystal structure is available for an atom, you can\n"
#~ "  look up the crystal type and lattice constant, otherwise you have to "
#~ "specify it\n"
#~ "  yourself.  "
#~ msgstr ""
#~ "  Brug denne dialog til at oprette krystalstrukturer. Vælg først "
#~ "strukturen,\n"
#~ "  enten fra en samling almindelige krystalstrukturer eller ud fra en\n"
#~ "  rumgruppebeskrivelse. Tilføj så alle andre gitterparametre.\n"
#~ "\n"
#~ "  Hvis der er en eksperimentel krystalstruktur tilgængelig for at\n"
#~ "  atom, kan du slå krystaltypen samt gitterkonstanten op – ellers skal\n"
#~ "  du angive den selv.  "

#~ msgid "Create Bulk Crystal by Spacegroup"
#~ msgstr "Opret krystalstruktur fra rumgruppe"

#~ msgid "Number: 1"
#~ msgstr "Nummer: 1"

# slice ~ opdel
#~ msgid "Lattice: "
#~ msgstr "Gitter: "

#~ msgid "\tSpace group: "
#~ msgstr "\tRumgruppe: "

#~ msgid "Size: x: "
#~ msgstr "Størrelse: x: "

#~ msgid "  y: "
#~ msgstr "  y: "

#~ msgid "  z: "
#~ msgstr "  z: "

#~ msgid "free"
#~ msgstr "fri"

#~ msgid "equals b"
#~ msgstr "lig med b"

#~ msgid "equals c"
#~ msgstr "lig med c"

#~ msgid "fixed"
#~ msgstr "fast"

#~ msgid "equals a"
#~ msgstr "lig med a"

#~ msgid "equals beta"
#~ msgstr "lig med beta"

#~ msgid "equals gamma"
#~ msgstr "lig med gamma"

#~ msgid "equals alpha"
#~ msgstr "lig med alfa"

#~ msgid "Lattice parameters"
#~ msgstr "Gitterparametre"

#~ msgid "\t\ta:\t"
#~ msgstr "\t\ta:\t"

#~ msgid "\talpha:\t"
#~ msgstr "\talfa:\t"

#~ msgid "\t\tb:\t"
#~ msgstr "\t\tb:\t"

#~ msgid "\tbeta:\t"
#~ msgstr "\tbeta:\t"

#~ msgid "\t\tc:\t"
#~ msgstr "\t\tc:\t"

#~ msgid "\tgamma:\t"
#~ msgstr "\tgamma:\t"

#~ msgid "Basis: "
#~ msgstr "Basis: "

#~ msgid "  Element:\t"
#~ msgstr "  Grundstof:\t"

#~ msgid "Creating a crystal."
#~ msgstr "Oprettelse af krystal."

#~ msgid "Symbol: %s"
#~ msgstr "Symbol: %s"

#~ msgid "Number: %s"
#~ msgstr "Nummer: %s"

#~ msgid "Invalid Spacegroup!"
#~ msgstr "Ugyldig rumgruppe!"

#~ msgid "Please specify a consistent set of atoms."
#~ msgstr "Angiv venligst en konsistent samling atomer."

#~ msgid "Can't find lattice definition!"
#~ msgstr "Kan ikke finde gitterdefinition!"

#~ msgid "Absolute position:"
#~ msgstr "Absolut position:"

#~ msgid "Relative to average position (of selection):"
#~ msgstr "Relativ til middelposition (af markering):"

#~ msgid ""
#~ "%s\n"
#~ "\n"
#~ "Number of atoms: %d.\n"
#~ "\n"
#~ "Unit cell:\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "\n"
#~ "%s\n"
#~ "%s\n"
#~ msgstr ""
#~ "%s\n"
#~ "\n"
#~ "Antal atomer: %d.\n"
#~ "\n"
#~ "Enhedscelle:\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "  %8.3f  %8.3f  %8.3f\n"
#~ "\n"
#~ "%s\n"
#~ "%s\n"

#~ msgid "Volume: "
#~ msgstr "Volumen: "

#~ msgid "Size: \tx: "
#~ msgstr "Størr.:\tx: "

#~ msgid ""
#~ "To make most calculations on the atoms, a Calculator object must first\n"
#~ "be associated with it.  ASE supports a number of calculators, supporting\n"
#~ "different elements, and implementing different physical models for the\n"
#~ "interatomic interactions."
#~ msgstr ""
#~ "For at kunne foretage de fleste typer atomare beregninger, skal der\n"
#~ "først tilknyttes et beregnerobject (Calculator).  ASE tilbyder\n"
#~ "adskillige beregnere, som understøtter forskellige grundstoffer, og\n"
#~ "implementerer forskellige fysiske modeller for atomernes vekselvirkning."

#~ msgid ""
#~ "The Lennard-Jones pair potential is one of the simplest\n"
#~ "possible models for interatomic interactions, mostly\n"
#~ "suitable for noble gasses and model systems.\n"
#~ "\n"
#~ "Interactions are described by an interaction length and an\n"
#~ "interaction strength."
#~ msgstr ""
#~ "Lennard–Jones-parpotentialet er en af de simpleste mulige modeller for\n"
#~ "atomare interaktioner, og er især nyttigt til ædelgasser og\n"
#~ "modelsystemer.\n"
#~ "\n"
#~ "Interaktionerne beskrives ved en interaktionslængde og en\n"
#~ "interaktionsstyrke."

#~ msgid ""
#~ "The EMT potential is a many-body potential, giving a\n"
#~ "good description of the late transition metals crystalling\n"
#~ "in the FCC crystal structure.  The elements described by the\n"
#~ "main set of EMT parameters are Al, Ni, Cu, Pd, Ag, Pt, and\n"
#~ "Au, the Al potential is however not suitable for materials\n"
#~ "science application, as the stacking fault energy is wrong.\n"
#~ "\n"
#~ "A number of parameter sets are provided.\n"
#~ "\n"
#~ "<b>Default parameters:</b>\n"
#~ "\n"
#~ "The default EMT parameters, as published in K. W. Jacobsen,\n"
#~ "P. Stoltze and J. K. Nørskov, <i>Surf. Sci.</i> <b>366</b>, 394 (1996).\n"
#~ "\n"
#~ "<b>Alternative Cu, Ag and Au:</b>\n"
#~ "\n"
#~ "An alternative set of parameters for Cu, Ag and Au,\n"
#~ "reoptimized to experimental data including the stacking\n"
#~ "fault energies by Torben Rasmussen (partly unpublished).\n"
#~ "\n"
#~ "<b>Ruthenium:</b>\n"
#~ "\n"
#~ "Parameters for Ruthenium, as published in J. Gavnholt and\n"
#~ "J. Schiøtz, <i>Phys. Rev. B</i> <b>77</b>, 035404 (2008).\n"
#~ "\n"
#~ "<b>Metallic glasses:</b>\n"
#~ "\n"
#~ "Parameters for MgCu and CuZr metallic glasses. MgCu\n"
#~ "parameters are in N. P. Bailey, J. Schiøtz and\n"
#~ "K. W. Jacobsen, <i>Phys. Rev. B</i> <b>69</b>, 144205 (2004).\n"
#~ "CuZr in A. Paduraru, A. Kenoufi, N. P. Bailey and\n"
#~ "J. Schiøtz, <i>Adv. Eng. Mater.</i> <b>9</b>, 505 (2007).\n"
#~ msgstr ""
#~ "EMT-potentialet er et mangepartikelpotential, som giver en god\n"
#~ "beskrivelse af de sene overgangsmetaller som danner FCC-strukturer.\n"
#~ "Grundstofferne som beskrives af hoveddelen af EMT-parametrene er Al,\n"
#~ "Ni, Cu, Pd, Ag, Pt og Au.  Dog er Al-potentialet ikke egnet til\n"
#~ "anvendelse i materialevidenskab, da energien for fejl i "
#~ "krystalstrukturen\n"
#~ "er forkert.\n"
#~ "\n"
#~ "Der medfølger en række standardparametre.\n"
#~ "\n"
#~ "<b>Standardparametre:</b>\n"
#~ "\n"
#~ "Standardparametrene som udgivet i K. W. Jacobsen,\n"
#~ "P. Stoltze og J. K. Nørskov, <i>Surf. Sci.</i> <b>366</b>, 394 (1996).\n"
#~ "\n"
#~ "<b>Alternativ Cu, Ag og Au:</b>\n"
#~ "\n"
#~ "Et alternativt sæt parametre for Cu, Ag og Au, genoptimeret til\n"
#~ "eksperimentelle data inklusive energier for krystalfejl af Torben\n"
#~ "Rasmussen (delvis upubliceret).\n"
#~ "\n"
#~ "<b>Ruthenium:</b>\n"
#~ "\n"
#~ "Parametre for ruthenium som udgivet i  J. Gavnholt og\n"
#~ "J. Schiøtz, <i>Phys. Rev. B</i> <b>77</b>, 035404 (2008).\n"
#~ "\n"
#~ "<b>Metalglas:</b>\n"
#~ "\n"
#~ "Parametre for MgCu- og CuZr-metalglas. MgCu-parametrene findes i\n"
#~ "N. P. Bailey, J. Schiøtz anog K. W. Jacobsen, <i>Phys. Rev. B</i> <b>69</"
#~ "b>, \n"
#~ "144205 (2004).\n"
#~ "CuZr findes i A. Paduraru, A. Kenoufi, N. P. Bailey og\n"
#~ "J. Schiøtz, <i>Adv. Eng. Mater.</i> <b>9</b>, 505 (2007).\n"

#~ msgid ""
#~ "The EMT potential is a many-body potential, giving a\n"
#~ "good description of the late transition metals crystalling\n"
#~ "in the FCC crystal structure.  The elements described by the\n"
#~ "main set of EMT parameters are Al, Ni, Cu, Pd, Ag, Pt, and\n"
#~ "Au.  In addition, this implementation allows for the use of\n"
#~ "H, N, O and C adatoms, although the description of these is\n"
#~ "most likely not very good.\n"
#~ "\n"
#~ "<b>This is the ASE implementation of EMT.</b> For large\n"
#~ "simulations the ASAP implementation is more suitable; this\n"
#~ "implementation is mainly to make EMT available when ASAP is\n"
#~ "not installed.\n"
#~ msgstr ""
#~ "EMT-potentialet er et mangepartikelpotential, som giver en god\n"
#~ "beskrivelse af de sene overgangsmetaller som danner FCC-strukturer.\n"
#~ "Grundstofferne som beskrives af hoveddelen af EMT-parametrene er Al,\n"
#~ "Ni, Cu, Pd, Ag, Pt og Au.  Yderligere tillader denne implementation\n"
#~ "brugen af H-, N-, O- og C-adatomer, selvom beskrivelsen af disse\n"
#~ "sandsynligvis er dårlig.\n"
#~ "\n"
#~ "<b>Dette er ASE's implementation af EMT.</b> For støre simulationer er\n"
#~ "ASAP-implementationen bedre; denne implementation bruges hovedsageligt\n"
#~ "for at tilbyde en EMT-beskrivelse når ASAP ikke er installeret.\n"

#~ msgid ""
#~ "The EAM/ADP potential is a many-body potential\n"
#~ "implementation of the Embedded Atom Method and\n"
#~ "equipotential plus the Angular Dependent Potential,\n"
#~ "which is an extension of the EAM to include\n"
#~ "directional bonds. EAM is suited for FCC metallic\n"
#~ "bonding while the ADP is suited for metallic bonds\n"
#~ "with some degree of directionality.\n"
#~ "\n"
#~ "For EAM see M.S. Daw and M.I. Baskes,\n"
#~ "Phys. Rev. Letters 50 (1983) 1285.\n"
#~ "\n"
#~ "For ADP see Y. Mishin, M.J. Mehl, and\n"
#~ "D.A. Papaconstantopoulos, Acta Materialia 53 2005\n"
#~ "4029--4041.\n"
#~ "\n"
#~ "Data for the potential is contained in a file in either LAMMPS Alloy\n"
#~ "or ADP format which need to be loaded before use. The Interatomic\n"
#~ "Potentials Repository Project at http://www.ctcms.nist.gov/potentials/\n"
#~ "contains many suitable potential files.\n"
#~ "\n"
#~ "For large simulations the LAMMPS calculator is more\n"
#~ "suitable; this implementation is mainly to make EAM\n"
#~ "available when LAMMPS is not installed or to develop\n"
#~ "new EAM/ADP poentials by matching results using ab\n"
#~ "initio.\n"
#~ msgstr ""
#~ "EAM/ADP potentialet er ee mangelegme-potential\n"
#~ "implementering af Embedded Atom Method og\n"
#~ "equipotential plus Angular Dependent Potential,\n"
#~ "hvilket er en udvidelse til EAM de inkluderer\n"
#~ "retningsafhængige bindinger. EAM er velegnet til FCC metalliske\n"
#~ "bindinger og ADP er velegnet til metalliske bindinger\n"
#~ "med nogen grad af retningsafhængighed.\n"
#~ "\n"
#~ "For EAM se M.S. Daw and M.I. Baskes,\n"
#~ "Phys. Rev. Letters 50 (1983) 1285.\n"
#~ "\n"
#~ "For ADP se Y. Mishin, M.J. Mehl, and\n"
#~ "D.A. Papaconstantopoulos, Acta Materialia 53 2005\n"
#~ "4029–4041.\n"
#~ "\n"
#~ "Data for potentialet er indeholdt i en fil i enten LAMMPS Alloy\n"
#~ "eller ADP formatet som skal indlæses før brug. Interatomic\n"
#~ "Potentials Repository Project (http://www.ctcms.nist.gov/potentials/)\n"
#~ "indeholder mange passende potential filer.\n"
#~ "\n"
#~ "For store simulationer er LAMMPS beregneren mere\n"
#~ "passende; denne implementation er hovedsageligis inkluderet for at\n"
#~ "gøre EAM tilgængelig når LAMMPS ikke er installeret eller for at udvikle\n"
#~ "nye EAM/ADP poentialer ved at matche ab initio resultater.\n"

#~ msgid ""
#~ "The Brenner potential is a reactive bond-order potential for\n"
#~ "carbon and hydrocarbons.  As a bond-order potential, it takes\n"
#~ "into account that carbon orbitals can hybridize in different\n"
#~ "ways, and that carbon can form single, double and triple\n"
#~ "bonds.  That the potential is reactive means that it can\n"
#~ "handle gradual changes in the bond order as chemical bonds\n"
#~ "are formed or broken.\n"
#~ "\n"
#~ "The Brenner potential is implemented in Asap, based on a\n"
#~ "C implentation published at http://www.rahul.net/pcm/brenner/ .\n"
#~ "\n"
#~ "The potential is documented here:\n"
#~ "  Donald W Brenner, Olga A Shenderova, Judith A Harrison,\n"
#~ "  Steven J Stuart, Boris Ni and Susan B Sinnott:\n"
#~ "  \"A second-generation reactive empirical bond order (REBO)\n"
#~ "  potential energy expression for hydrocarbons\",\n"
#~ "  J. Phys.: Condens. Matter 14 (2002) 783-802.\n"
#~ "  doi: 10.1088/0953-8984/14/4/312\n"
#~ msgstr ""
#~ "Brennerpotentialet er et reaktivt bindingsordenspotential til kulstof og "
#~ "kulbrinter.  Som et bindingsordenspotential tager det højde for at "
#~ "kulstoforbitaler kan hybridisere på forskellige måder, og at kulstof kan "
#~ "danne enkelt- dobbelt- og tripelbindinger.  At potentialet er reaktivt "
#~ "betyder, at det kan beskrive gradvise ændringer i bindingsorden "
#~ "efterhånden som kemiske bindinger dannes eller brydes.\n"
#~ "\n"
#~ "Brennerpotentialet er implementeret i ASAP baseret på en C-implementation "
#~ "publiceret på siden http://www.rahul.net/pcm/brenner/ .\n"
#~ "\n"
#~ "Potentialet dokumenteres her:\n"
#~ "  Donald W Brenner, Olga A Shenderova, Judith A Harrison,\n"
#~ "  Steven J Stuart, Boris Ni and Susan B Sinnott:\n"
#~ "  \"A second-generation reactive empirical bond order (REBO)\n"
#~ "  potential energy expression for hydrocarbons\",\n"
#~ "  J. Phys.: Condens. Matter 14 (2002) 783–802.\n"
#~ "  doi: 10.1088/0953-8984/14/4/312\n"

#~ msgid ""
#~ "GPAW implements Density Functional Theory using a\n"
#~ "<b>G</b>rid-based real-space representation of the wave\n"
#~ "functions, and the <b>P</b>rojector <b>A</b>ugmented <b>W</b>ave\n"
#~ "method for handling the core regions.\n"
#~ msgstr ""
#~ "GPAW implementerer tæthedsfunktionalteori med en <b>G</b>itterbaseret\n"
#~ "repræsentation af bølgefunktioner i det reelle rum, samt\n"
#~ "<b>P</b>rojector <b>A</b>ugmented <b>W</b>ave-metoden til behandling\n"
#~ "af regionen omkring atomkerner.\n"

#~ msgid ""
#~ "FHI-aims is an external package implementing density\n"
#~ "functional theory and quantum chemical methods using\n"
#~ "all-electron methods and a numeric local orbital basis set.\n"
#~ "For full details, see http://www.fhi-berlin.mpg.de/aims/\n"
#~ "or Comp. Phys. Comm. v180 2175 (2009). The ASE\n"
#~ "documentation contains information on the keywords and\n"
#~ "functionalities available within this interface.\n"
#~ msgstr ""
#~ "FHI-aims er en ekstern pakke, der implementerer tæthedsfunktionalteori\n"
#~ "og kvantekemiske metoder ved brug af \"all-electron\"-metoder og et\n"
#~ "numerisk lokaliseret atomart basissæt. De fulde detaljer kan findes på\n"
#~ "http://www.fhi-berlin.mpg.de/aims/ eller i Comp. Phys. Comm. v180 2175\n"
#~ "(2009). ASE-dokumentationen indeholder oplysninger om nøgleord og\n"
#~ "funktioner, som er tilgængelige i denne grænseflade.\n"

#~ msgid ""
#~ "WARNING:\n"
#~ "Your system seems to have more than zero but less than\n"
#~ "three periodic dimensions. Please check that this is\n"
#~ "really what you want to compute. Assuming full\n"
#~ "3D periodicity for this calculator."
#~ msgstr ""
#~ "ADVARSEL:\n"
#~ "Dit system skal have flere end nul men mindre end tre periodiske\n"
#~ "dimensioner.  Kontrollér venligst at dette virkelig er hvad du godt\n"
#~ "vil beregne. Antager fuld 3D-periodicitet for denne beregner."

#~ msgid ""
#~ "VASP is an external package implementing density\n"
#~ "functional functional theory using pseudopotentials\n"
#~ "or the projector-augmented wave method together\n"
#~ "with a plane wave basis set. For full details, see\n"
#~ "http://cms.mpi.univie.ac.at/vasp/vasp/\n"
#~ msgstr ""
#~ "VASP er en ekstern pakke, der implementerer tæthedsfunktionalteori med\n"
#~ "pseudopotentialer eller PAW-metoden (projector augmented wave method)\n"
#~ "sammen med en planbølgebasis. De fulde detaljer kan findes på\n"
#~ "http://cms.mpi.univie.ac.at/vasp/vasp/\n"

#~ msgid "Default (Al, Ni, Cu, Pd, Ag, Pt, Au)"
#~ msgstr "Standard (Al, Ni, Cu, Pd, Ag, Pt, Au)"

#~ msgid "Alternative Cu, Ag and Au"
#~ msgstr "Alternativ Cu, Ag og Au"

#~ msgid "Ruthenium"
#~ msgstr "Ruthenium"

#~ msgid "CuMg and CuZr metallic glass"
#~ msgstr "Metallisk glas med CuMg og CuZr"

#~ msgid "Select calculator"
#~ msgstr "Vælg beregner"

#~ msgid "None"
#~ msgstr "Ingen"

#~ msgid "Lennard-Jones (ASAP)"
#~ msgstr "Lennard–Jones (ASAP)"

#~ msgid "Setup"
#~ msgstr "Opsætning"

#~ msgid "EMT - Effective Medium Theory (ASAP)"
#~ msgstr "EMT – Effective Medium Theory (ASAP)"

#~ msgid "EMT - Effective Medium Theory (ASE)"
#~ msgstr "EMT – Effective Medium Theory (ASE)"

#~ msgid "EAM - Embedded Atom Method/Angular Dependent Potential (ASE)"
#~ msgstr "EAM – Embedded Atom Method/Angular Dependent Potential (ASE)"

#~ msgid "Brenner Potential (ASAP)"
#~ msgstr "Brenner-potentialet (ASAP)"

#~ msgid "Density Functional Theory (GPAW)"
#~ msgstr "Tæthedsfunktionalteori (GPAW)"

#~ msgid "Density Functional Theory (FHI-aims)"
#~ msgstr "Tæthedsfunktionalteori (FHI-aims)"

#~ msgid "Density Functional Theory (VASP)"
#~ msgstr "Tæthedsfunktionalteori (VASP)"

#~ msgid "Check that the calculator is reasonable."
#~ msgstr "Kontrollér at beregneren er rimelig."

#~ msgid "ASAP is not installed. (Failed to import asap3)"
#~ msgstr "ASAP er ikke installeret. (Kunne ikke importere asap3)"

#~ msgid "You must set up the Lennard-Jones parameters"
#~ msgstr "Du skal indstille Lennard–Jones-parametrene"

#~ msgid "Could not create useful Lennard-Jones calculator."
#~ msgstr "Kunne ikke oprette en nyttig Lennard–Jones-beregner."

#~ msgid "Could not attach EMT calculator to the atoms."
#~ msgstr "Kunne ikke knytte EMT-beregner til atomerne."

#~ msgid "You must set up the EAM parameters"
#~ msgstr "Du skal angive EAM-parametrene"

#~ msgid "GPAW is not installed. (Failed to import gpaw)"
#~ msgstr "GPAW er ikke installeret. (Kunne ikke importere gpaw)"

#~ msgid "You must set up the GPAW parameters"
#~ msgstr "Du skal angive GPAW-parametrene"

#~ msgid "You must set up the FHI-aims parameters"
#~ msgstr "Du skal angive FHI-aims-parametrene"

#~ msgid "You must set up the VASP parameters"
#~ msgstr "Du skal angive VASP-parametrene"

#~ msgid "Element %(sym)s not allowed by the '%(name)s' calculator"
#~ msgstr "Grundstoffet %(sym)s tillades ikke af \"%(name)s\"-beregneren"

#~ msgid "Lennard-Jones parameters"
#~ msgstr "Lennard–Jones-parametre"

#~ msgid "Specify the Lennard-Jones parameters here"
#~ msgstr "Angiv Lennard–Jones-parametrene her"

#~ msgid "Epsilon (eV):"
#~ msgstr "Epsilon (eV):"

#~ msgid "Sigma (Å):"
#~ msgstr "Sigma (Å):"

#~ msgid "Shift to make smooth at cutoff"
#~ msgstr "Skift for at blødgøre ved afskæring"

#~ msgid "EAM parameters"
#~ msgstr "EAM-parametre"

#~ msgid "Import Potential"
#~ msgstr "Importér potential"

#~ msgid "You need to import the potential file"
#~ msgstr "Du skal importere potentialfilen"

#~ msgid "Import .alloy or .adp potential file ... "
#~ msgstr "Importér .alloy- eller .adp-potentialfil …"

#~ msgid "GPAW parameters"
#~ msgstr "GPAW-parametre"

#~ msgid "%i atoms.\n"
#~ msgstr "%i atomer.\n"

#~ msgid "Orthogonal unit cell: %.2f x %.2f x %.2f Å."
#~ msgstr "Ortogonal enhedscelle: %.2f x %.2f x %.2f Å."

#~ msgid "Non-orthogonal unit cell:\n"
#~ msgstr "Ikke-ortogonal enhedscelle:\n"

#~ msgid "Exchange-correlation functional: "
#~ msgstr "Udvekslings- og korrelationsfunktional: "

#~ msgid "Grid spacing"
#~ msgstr "Gitterafstand"

#~ msgid "Grid points"
#~ msgstr "Gitterpunkter"

#~ msgid "h<sub>eff</sub> = (%.3f, %.3f, %.3f) Å"
#~ msgstr "h<sub>eff</sub> = (%.3f, %.3f, %.3f) Å"

#~ msgid "k-points  k = ("
#~ msgstr "k-punkter  k = ("

#~ msgid "k-points x size:  (%.1f, %.1f, %.1f) Å"
#~ msgstr "k-punkter x størrelse:  (%.1f, %.1f, %.1f) Å"

#~ msgid "Spin polarized"
#~ msgstr "Spinpolariseret"

#~ msgid "FD - Finite Difference (grid) mode"
#~ msgstr "FD – finite difference-tilstand (gitter)"

#~ msgid "LCAO - Linear Combination of Atomic Orbitals"
#~ msgstr "LCAO – linearkombination af atomare orbitaler"

#~ msgid "Mode: "
#~ msgstr "Tilstand: "

#~ msgid "sz - Single Zeta"
#~ msgstr "sz – enkelt-zeta"

#~ msgid "szp - Single Zeta polarized"
#~ msgstr "szp – enkelt-zeta polariseret"

#~ msgid "dzp - Double Zeta polarized"
#~ msgstr "dzp – dobbelt-zeta polariseret"

#~ msgid "Basis functions: "
#~ msgstr "Basisfunktioner: "

#~ msgid "Non-standard mixer parameters"
#~ msgstr "Særlige mikserparametre"

#~ msgid "FHI-aims parameters"
#~ msgstr "FHI-aims-parametre"

#~ msgid "Periodic geometry, unit cell is:\n"
#~ msgstr "Periodisk geometri; enhedscellen er:\n"

#~ msgid "Non-periodic geometry.\n"
#~ msgstr "Ikke-periodisk geometri.\n"

# XXX ikke Hirschfeld?
#~ msgid "Hirshfeld-based dispersion correction"
#~ msgstr "Hirshfeld-baseret dispersionskorrektion"

#~ msgid "Spin / initial moment "
#~ msgstr "Spin / startmoment "

#~ msgid "   Charge"
#~ msgstr "   Ladning"

#~ msgid "   Relativity"
#~ msgstr "   Relativitet"

#~ msgid " Threshold"
#~ msgstr " Tærskel"

#~ msgid "Self-consistency convergence:"
#~ msgstr "Selfkonsistenskonvergens:"

#~ msgid "Compute forces"
#~ msgstr "Beregn kræfter"

#~ msgid "Energy:                 "
#~ msgstr "Energi:                 "

#~ msgid " eV   Sum of eigenvalues:  "
#~ msgstr " eV   Sum af egenværdier:  "

#~ msgid " eV"
#~ msgstr " eV"

#~ msgid "Electron density: "
#~ msgstr "Elektrontæthed: "

#~ msgid "        Force convergence:  "
#~ msgstr "        Kraftkonvergens:  "

#~ msgid " eV/Ang  "
#~ msgstr " eV/Å  "

#~ msgid "Additional keywords: "
#~ msgstr "Yderligere nøgleord: "

#~ msgid "FHI-aims execution command: "
#~ msgstr "Kørselskommando til FHI-aims: "

# ??
#~ msgid "Directory for species defaults: "
#~ msgstr "Mappe for grundstofstandarder: "

#~ msgid "Set Defaults"
#~ msgstr "Brug standardværdier"

#~ msgid "Import control.in"
#~ msgstr "Importér control.in"

#~ msgid "Export control.in"
#~ msgstr "Eksportér control.in"

#~ msgid "Export parameters ... "
#~ msgstr "Eksportér parametre …"

#~ msgid "Import control.in file ... "
#~ msgstr "Importér control.in-fil …"

#~ msgid ""
#~ "Please use the facilities provided in this window to manipulate the "
#~ "keyword: %s!"
#~ msgstr ""
#~ "Brug venligst faciliteterne i dette vindue til at manipulere nøgleordet: "
#~ "%s!"

#~ msgid ""
#~ "Don't know this keyword: %s\n"
#~ "\n"
#~ "Please check!\n"
#~ "\n"
#~ "If you really think it should be available, please add it to the top of "
#~ "ase/calculators/aims.py."
#~ msgstr ""
#~ "Kender ikke dette nøgleord: %s\n"
#~ "\n"
#~ "Kontrollér venligst!\n"
#~ "\n"
#~ "Hvis du virkelig mener det børe være tilgængeligt, så tilføj det venligst "
#~ "øverst i ase/calculators/aims.py."

#~ msgid "VASP parameters"
#~ msgstr "VASP-parametre"

#~ msgid "Periodic geometry, unit cell is: \n"
#~ msgstr "Periodisk geometri; enhedscelle er: \n"

#~ msgid ")    Cutoff: "
#~ msgstr ")    Afskæring: "

#~ msgid "    Precision: "
#~ msgstr "    Præcision: "

#~ msgid "k-points x size:  (%.1f, %.1f, %.1f) Å       "
#~ msgstr "k-punkter x størrelse:  (%.1f, %.1f, %.1f) Å       "

#~ msgid "Smearing: "
#~ msgstr "Udjævning: "

#~ msgid " order: "
#~ msgstr " orden: "

#~ msgid " width: "
#~ msgstr " bredde: "

#~ msgid "Self-consistency convergence: "
#~ msgstr "Selfkonsistenskonvergens: "

#~ msgid "VASP execution command: "
#~ msgstr "Kørselskommando til VASP: "

#~ msgid "Import VASP files"
#~ msgstr "Importér VASP-filer"

#~ msgid "Export VASP files"
#~ msgstr "Eksportér VASP-filer"

#~ msgid "<b>WARNING:</b> cutoff energy is lower than recommended minimum!"
#~ msgstr ""
#~ "<b>ADVARSEL:</b> afskæringsenergi er lavere end det anbefalede minimum!"

#~ msgid "Import VASP input files: choose directory ... "
#~ msgstr "Importér VASP-inputfiler: vælg mappe …"

#~ msgid "Export VASP input files: choose directory ... "
#~ msgstr "Eksportér VASP-inputfiler: vælg mappe …"

#~ msgid ""
#~ "Don't know this keyword: %s\n"
#~ "Please check!\n"
#~ "\n"
#~ "If you really think it should be available, please add it to the top of "
#~ "ase/calculators/vasp.py."
#~ msgstr ""
#~ "Kender ikke dette nøgleord:: %s\n"
#~ "Kontrollér venligst!\n"
#~ "\n"
#~ "Hvis du virkelig tror det bør være tilgængeligt, så tilføj det venligst "
#~ "øverst i ase/calculators/vasp.py."

#~ msgid ""
#~ "\n"
#~ "    Global commands work on all frames or only on the current frame\n"
#~ "    - Assignment of a global variable may not reference a local one\n"
#~ "    - use 'Current frame' switch to switch off application to all frames\n"
#~ "    <c>e</c>:\t\ttotal energy of one frame\n"
#~ "    <c>fmax</c>:\tmaximal force in one frame\n"
#~ "    <c>A</c>:\tunit cell\n"
#~ "    <c>E</c>:\t\ttotal energy array of all frames\n"
#~ "    <c>F</c>:\t\tall forces in one frame\n"
#~ "    <c>M</c>:\tall magnetic moments\n"
#~ "    <c>R</c>:\t\tall atomic positions\n"
#~ "    <c>S</c>:\tall selected atoms (boolean array)\n"
#~ "    <c>D</c>:\tall dynamic atoms (boolean array)\n"
#~ "    examples: <c>frame = 1</c>, <c>A[0][1] += 4</c>, <c>e-E[-1]</c>\n"
#~ "\n"
#~ "    Atom commands work on each atom (or a selection) individually\n"
#~ "    - these can use global commands on the RHS of an equation\n"
#~ "    - use 'selected atoms only' to restrict application of command\n"
#~ "    <c>x,y,z</c>:\tatomic coordinates\n"
#~ "    <c>r,g,b</c>:\tatom display color, range is [0..1]\n"
#~ "    <c>rad</c>:\tatomic radius for display\n"
#~ "    <c>s</c>:\t\tatom is selected\n"
#~ "    <c>d</c>:\t\tatom is movable\n"
#~ "    <c>f</c>:\t\tforce\n"
#~ "    <c>Z</c>:\tatomic number\n"
#~ "    <c>m</c>:\tmagnetic moment\n"
#~ "    examples: <c>x -= A[0][0], s = z > 5, Z = 6</c>\n"
#~ "\n"
#~ "    Special commands and objects:\n"
#~ "    <c>sa,cf</c>:\t(un)restrict to selected atoms/current frame\n"
#~ "    <c>frame</c>:\tframe number\n"
#~ "    <c>center</c>:\tcenters the system in its existing unit cell\n"
#~ "    <c>del S</c>:\tdelete selection\n"
#~ "    <c>CM</c>:\tcenter of mass\n"
#~ "    <c>ans[-i]</c>:\tith last calculated result\n"
#~ "    <c>exec file</c>: executes commands listed in file\n"
#~ "    <c>cov[Z]</c>:(read only): covalent radius of atomic number Z\n"
#~ "    <c>gui</c>:\tadvanced: gui window python object\n"
#~ "    <c>img</c>:\tadvanced: gui images object\n"
#~ "    "
#~ msgstr ""
#~ "\n"
#~ "    Globale kommandoer virker på alle billeder, eller kun på nuværende "
#~ "billede\n"
#~ "    – tildeling af en global variabel refererer måske ikke til en lokal\n"
#~ "    – brug \"Nuværende billede\"-knappen til at slå anvendelse på alle "
#~ "billeder\n"
#~ "      til eller fra\n"
#~ "    <c>e</c>:\t\ttotalenergi af et billede\n"
#~ "    <c>fmax</c>:\tmaksimal kraft i et billede\n"
#~ "    <c>A</c>:\tenhedscelle\n"
#~ "    <c>E</c>:\t\ttotalenergi som array for alle billeder\n"
#~ "    <c>F</c>:\t\talle kræfter i et billede\n"
#~ "    <c>M</c>:\talle magnetiske momenter\n"
#~ "    <c>R</c>:\t\talle atompositioner\n"
#~ "    <c>S</c>:\talle markerede atoms (boolesk array)\n"
#~ "    <c>D</c>:\talle dynamiske atomer (boolesk array)\n"
#~ "    eksempler: <c>billede = 1</c>, <c>A[0][1] += 4</c>, <c>e-E[-1]</c>\n"
#~ "\n"
#~ "    Atomkommandoer virker på hvert atom (eller en markering) enkeltvis\n"
#~ "    – disse kan bruge globale kommandoer på højresiden af en ligning\n"
#~ "    – brug \"kun markerede atomer\" for at begrænse anvendelsen af en "
#~ "kommando\n"
#~ "    <c>x,y,z</c>:\tatomkoordinater\n"
#~ "    <c>r,g,b</c>:\tatomvisningsfarve; interval er [0..1]\n"
#~ "    <c>rad</c>:\tatomradius (grafisk)\n"
#~ "    <c>s</c>:\t\tatom er markeret\n"
#~ "    <c>d</c>:\t\tatom kan flyttes\n"
#~ "    <c>f</c>:\t\tkraft\n"
#~ "    <c>Z</c>:\tatomnummer\n"
#~ "    <c>m</c>:\tmagnetisk moment\n"
#~ "    eksempler: <c>x -= A[0][0], s = z > 5, Z = 6</c>\n"
#~ "\n"
#~ "    Specialkommandoer og objekter:\n"
#~ "    <c>sa,cf</c>:\tslå begrænsning til markerede atomer/nuværende atomer\n"
#~ "                        til eller fra\n"
#~ "    <c>frame</c>:\tbillednummer\n"
#~ "    <c>center</c>:\tcentrerer systemet i dets eksisterende enhedscelle\n"
#~ "    <c>del S</c>:\tfjern markering\n"
#~ "    <c>CM</c>:\tmassemidtpunkt\n"
#~ "    <c>ans[-i]</c>:\ti'te sidst udregnede resultat\n"
#~ "    <c>exec file</c>: kører kommandoerne i en fil\n"
#~ "    <c>cov[Z]</c>:(skrivebeskyttet): kovalent radius for atomnummer Z\n"
#~ "    <c>gui</c>:\tavanceret: python-objekt for gui-vinduet\n"
#~ "    <c>img</c>:\tavanceret: gui-billeder som objekt\n"
#~ "    "

#~ msgid "Expert user mode"
#~ msgstr "Eksperttilstand"

#~ msgid "Welcome to the ASE Expert user mode"
#~ msgstr "Velkommen til ASE's eksperttilstand"

#~ msgid "Only selected atoms (sa)   "
#~ msgstr "Kun markerede atomer (sa)   "

#~ msgid "Only current frame (cf)  "
#~ msgstr "Kun nuværende billede (cf)  "

#~ msgid ""
#~ "Global: Use A, D, E, M, N, R, S, n, frame; Atoms: Use a, f, m, s, x, y, "
#~ "z, Z     "
#~ msgstr ""
#~ "Globalt: Brug A, D, E, M, N, R, S, n, frame; Atomer: Brug a, f, m, s, x, "
#~ "y, z, Z     "

#~ msgid "*** WARNING: file does not exist - %s"
#~ msgstr "*** ADVARSEL: filen findes ikke – %s"

#~ msgid "*** WARNING: No atoms selected to work with"
#~ msgstr "*** ADVARSEL: Ingen atomer markeret til at arbejde på"

#~ msgid "*** Only working on selected atoms"
#~ msgstr "*** Arbejder kun på markerede atomer"

#~ msgid "*** Working on all atoms"
#~ msgstr "*** Arbejder på alle atomer"

#~ msgid "*** Only working on current image"
#~ msgstr "*** Arbejder kun på nuværende billede"

#~ msgid "*** Working on all images"
#~ msgstr "*** Arbejder på alle billeder"

#~ msgid "Save Terminal text ..."
#~ msgstr "Gem terminaltekst …"

#~ msgid "Cancel"
#~ msgstr "Annullér"

#~ msgid "Algorithm: "
#~ msgstr "Algoritme: "

#~ msgid "Convergence criterion: F<sub>max</sub> = "
#~ msgstr "Konvergenskriterium: F<sub>max</sub> = "

#~ msgid "Max. number of steps: "
#~ msgstr "Maksimalt antal trin: "

#~ msgid "Pseudo time step: "
#~ msgstr "Pseudotidsskridt: "

#~ msgid "Energy minimization"
#~ msgstr "Energiminimering"

#~ msgid "Minimize the energy with respect to the positions."
#~ msgstr "Minimér energien med hensyn til positionerne."

#~ msgid "Running ..."
#~ msgstr "Kører …"

#~ msgid "Minimization CANCELLED after %i steps."
#~ msgstr "Minimering AFBRUDT efter %i trin."

#~ msgid "Out of memory, consider using LBFGS instead"
#~ msgstr "Løbet tør for hukommelse; overvej at bruge LBFGS i stedet"

#~ msgid "Minimization completed in %i steps."
#~ msgstr "Minimering fuldført på %i trin."

#~ msgid "Progress"
#~ msgstr "Fremgang"

#~ msgid "Scaling deformation:"
#~ msgstr "Skaleringsdeformation:"

#~ msgid "Step number %s of %s."
#~ msgstr "Trin nummer %s af %s."

#~ msgid "Energy minimization:"
#~ msgstr "Energiminimering:"

#~ msgid "Step number: "
#~ msgstr "Trinnummer: "

#~ msgid "F<sub>max</sub>: "
#~ msgstr "F<sub>max</sub>: "

#~ msgid "unknown"
#~ msgstr "ukendt"

#~ msgid "Status: "
#~ msgstr "Status: "

#~ msgid "Iteration: "
#~ msgstr "Iteration: "

#~ msgid "log<sub>10</sub>(change):"
#~ msgstr "log<sub>10</sub>(skift):"

#~ msgid "Wave functions: "
#~ msgstr "Bølgefunktioner: "

#~ msgid "Density: "
#~ msgstr "Tæthed: "

#~ msgid "GPAW version: "
#~ msgstr "GPAW-version: "

#~ msgid "N/A"
#~ msgstr "–"

#~ msgid "Memory estimate: "
#~ msgstr "Hukommelsesestimat: "

#~ msgid "No info"
#~ msgstr "Ingen info"

#~ msgid "Initializing"
#~ msgstr "Klargør"

#~ msgid "Positions:"
#~ msgstr "Positioner:"

#~ msgid "Starting calculation"
#~ msgstr "Starter beregning"

#~ msgid "unchanged"
#~ msgstr "uændret"

#~ msgid "Self-consistency loop"
#~ msgstr "Selvkonsistensløkke"

#~ msgid "Calculating forces"
#~ msgstr "Beregner kræfter"

#~ msgid " (converged)"
#~ msgstr " (konvergeret)"

#~ msgid "To get a full traceback, use: ase-gui --verbose"
#~ msgstr "Kør ase-gui --verbose for at se det fulde traceback"

#~ msgid "No atoms loaded."
#~ msgstr "Ingen atomer indlæst."

#~ msgid "FCC(111) non-orthogonal"
#~ msgstr "FCC(111) ikke-ortogonal"

#~ msgid "FCC(111) orthogonal"
#~ msgstr "FCC(111) ortogonal"

#~ msgid "BCC(110) non-orthogonal"
#~ msgstr "BCC(110) ikke-ortogonal"

#~ msgid "BCC(110) orthogonal"
#~ msgstr "BCC(110) ortogonal"

#~ msgid "BCC(111) non-orthogonal"
#~ msgstr "BCC(111) ikke-ortogonal"

#~ msgid "BCC(111) orthogonal"
#~ msgstr "BCC(111) ortogonal"

#~ msgid "HCP(0001) non-orthogonal"
#~ msgstr "HCP(0001) ikke-ortogonal"

#~ msgid "Element: "
#~ msgstr "Grundstof: "

#~ msgid "a:"
#~ msgstr "a:"

#~ msgid "(%.1f %% of ideal)"
#~ msgstr "(%.1f %% af ideel)"

#~ msgid "      \t\tz: "
#~ msgstr "      \t\tz: "

#~ msgid " layers,  "
#~ msgstr " lag,     "

#~ msgid " Å vacuum"
#~ msgstr " Å vakuum"

#~ msgid "\t\tNo size information yet."
#~ msgstr "\t\tEndnu ingen størrelsesoplysninger."

#~ msgid "%i atoms."
#~ msgstr "%i atomer."

#~ msgid "Invalid element."
#~ msgstr "Ugyldigt grundstof."

#~ msgid "No structure specified!"
#~ msgstr "Ingen struktur angivet!"

# %s ~ BCC
#~ msgid "%(struct)s lattice constant unknown for %(element)s."
#~ msgstr "%(struct)s-gitterkonstant ukendt for %(element)s."

#~ msgid "By atomic number, user specified"
#~ msgstr "Efter atomnummer, brugerdefineret"

#~ msgid "By coordination"
#~ msgstr "Efter koordination"

#~ msgid "Manually specified"
#~ msgstr "Manuelt angivet"

#~ msgid "All the same color"
#~ msgstr "Alle med samme farve"

#~ msgid "This should not be displayed in forces!"
#~ msgstr "Dette bør ikke blive vist ved kræfter!"

#~ msgid "Min: "
#~ msgstr "Min: "

#~ msgid "  Max: "
#~ msgstr "  Maks: "

#~ msgid "  Steps: "
#~ msgstr "  Trin: "

#~ msgid "This should not be displayed!"
#~ msgstr "Dette bør ikke blive vist!"

#~ msgid "Create a color scale:"
#~ msgstr "Opret en farveskala:"

#~ msgid "Black - white"
#~ msgstr "Sort – hvid"

#~ msgid "Black - red - yellow - white"
#~ msgstr "Sort – rød – gul – hvid"

#~ msgid "Black - green - white"
#~ msgstr "Sort – grøn – hvid"

#~ msgid "Black - blue - cyan"
#~ msgstr "Sort – blå – cyan"

#~ msgid "Blue - white - red"
#~ msgstr "Blå – hvid – rød"

#~ msgid "Hue"
#~ msgstr "Farvetone"

#~ msgid "Named colors"
#~ msgstr "Navngivne farver"

#~ msgid "Create"
#~ msgstr "Opret"

#~ msgid "ERROR"
#~ msgstr "FEJL"

#~ msgid "ERR"
#~ msgstr "FEJL"

#~ msgid "Incorrect color specification"
#~ msgstr "Forkert farveangivelse"

#~ msgid " selected atoms:"
#~ msgstr " markerede atomer:"

#~ msgid "Close"
#~ msgstr "Luk"

#~ msgid "Debug"
#~ msgstr "Fejlsøgning"

#~ msgid "Bug Detected"
#~ msgstr "Fejl fundet"

#~ msgid "A programming error has been detected."
#~ msgstr "Der blev fundet en programmeringsfejl."

#~ msgid ""
#~ "It probably isn't fatal, but the details should be reported to the "
#~ "developers nonetheless."
#~ msgstr ""
#~ "Den er nok ikke fatal, men detaljerne bør alligevel rapporteres til "
#~ "udviklerne."

#~ msgid ""
#~ "From: buggy_application\"\n"
#~ "To: bad_programmer\n"
#~ "Subject: Exception feedback\n"
#~ "\n"
#~ "%s"
#~ msgstr ""
#~ "Fra: fejlagtigt_program\"\n"
#~ "Til: dårlig_programmør\n"
#~ "Emne: Exception feedback\n"
#~ "\n"
#~ "%s"

#~ msgid "Bug Details"
#~ msgstr "Detaljer om fejl"

#~ msgid "Create a new file"
#~ msgstr "Opret en ny fil"

#~ msgid "New ase.gui window"
#~ msgstr "Nyt ase.gui-vindue"

#~ msgid "Save current file"
#~ msgstr "Gem den aktuelle fil"

#~ msgid "Quit"
#~ msgstr "Afslut"

#~ msgid "Copy current selection and its orientation to clipboard"
#~ msgstr "Kopiér nuværende markering og dens orientering til udklipsholderen"

#~ msgid "Insert current clipboard selection"
#~ msgstr "Indsæt nuværende markering fra udklipsholderen"

#~ msgid "Change tags, moments and atom types of the selected atoms"
#~ msgstr "Ændr mærker, impuls og atomnummer for de valgte atomer"

#~ msgid "Insert or import atoms and molecules"
#~ msgstr "Indsæt eller importér atomer og molekyler"

#~ msgid "Delete the selected atoms"
#~ msgstr "Slet de markerede atomer"

#~ msgid "'xy' Plane"
#~ msgstr "'xy'-plan"

#~ msgid "'yz' Plane"
#~ msgstr "'yz'-plan"

#~ msgid "'zx' Plane"
#~ msgstr "'zx'-plan"

#~ msgid "'yx' Plane"
#~ msgstr "'yx'-plan"

#~ msgid "'zy' Plane"
#~ msgstr "'zy'-plan"

#~ msgid "'xz' Plane"
#~ msgstr "'xz'-plan"

#~ msgid "Create a bulk crystal with arbitrary orientation"
#~ msgstr "Opret en krystalstruktur med arbitrær orientering"

#~ msgid "Create the most common surfaces"
#~ msgstr "Opret de mest almindelige overflader"

#~ msgid "Create a crystalline nanoparticle"
#~ msgstr "Opret en krystallinsk nanopartikel"

#~ msgid "Create a nanotube"
#~ msgstr "Opret et nanorør"

#~ msgid "Create a graphene sheet or nanoribbon"
#~ msgstr "Opret et lag eller bånd af grafén"

#~ msgid "Set a calculator used in all calculation modules"
#~ msgstr "Angiv en beregner der skal bruges i alle beregningsmoduler"

#~ msgid "Calculate energy and forces"
#~ msgstr "Beregn energi og kræfter"

#~ msgid "Minimize the energy"
#~ msgstr "Minimér energien"

#~ msgid "Scale system"
#~ msgstr "Skalér systemet"

#~ msgid "Deform system by scaling it"
#~ msgstr "Deformér systemet ved skalering"

#~ msgid "Orien_t atoms"
#~ msgstr "Orien_tér atomer"

#~ msgid "<<filename>>"
#~ msgstr "<<filnavn>>"

#~ msgid "Paste"
#~ msgstr "Indsæt"

#~ msgid "Insert atom or molecule"
#~ msgstr "Indsæt atom eller molekyle"

#~ msgid "_Cancel"
#~ msgstr "_Annullér"

#~ msgid "Atom"
#~ msgstr "Atom"

#~ msgid "Confirmation"
#~ msgstr "Bekræftelse"

#~ msgid "File type:"
#~ msgstr "Filtype:"

#~ msgid "Not implemented!"
#~ msgstr "Ikke implementeret!"

#~ msgid "do you really need it?"
#~ msgstr "har du virkelig brug for dette?"

#~ msgid "Dummy placeholder object"
#~ msgstr "Stedfortræderobjekt"

#~ msgid "Set all directions to default values"
#~ msgstr "Sæt alle retninger til standardværdier"

#~ msgid "Particle size: "
#~ msgstr "Partikelstørrelse: "

#~ msgid "%.1f Å"
#~ msgstr "%.1f Å"

#~ msgid "Python"
#~ msgstr "Python"

#~ msgid ""
#~ "\n"
#~ "Title: %(title)s\n"
#~ "Time: %(time)s\n"
#~ msgstr ""
#~ "\n"
#~ "Titel: %(title)s\n"
#~ "Tid: %(time)s\n"

#~ msgid "ag: Python code"
#~ msgstr "ag: Pythonkode"

#~ msgid "Information:"
#~ msgstr "Information:"

#~ msgid "Python code:"
#~ msgstr "Pythonkode:"

#~ msgid "Homogeneous scaling"
#~ msgstr "Homogen skalering"

#~ msgid "3D deformation   "
#~ msgstr "3D-deformation   "

#~ msgid "2D deformation   "
#~ msgstr "2D-deformation   "

#~ msgid "1D deformation   "
#~ msgstr "1D-deformation   "

#~ msgid "Bulk"
#~ msgstr "Krystal"

#~ msgid "x-axis"
#~ msgstr "x-akse"

#~ msgid "y-axis"
#~ msgstr "y-akse"

#~ msgid "z-axis"
#~ msgstr "z-akse"

#~ msgid "Allow deformation along non-periodic directions."
#~ msgstr "Tillad deformation langs ikke-periodiske retninger."

#~ msgid "Deformation:"
#~ msgstr "Deformation:"

#~ msgid "Maximal scale factor: "
#~ msgstr "Maksimal skaleringsfaktor: "

#~ msgid "Scale offset: "
#~ msgstr "Forskydning ved skalering: "

#~ msgid "Number of steps: "
#~ msgstr "Antal trin: "

#~ msgid "Only positive deformation"
#~ msgstr "Kun positiv deformation"

#~ msgid "On   "
#~ msgstr "Til  "

#~ msgid "Off"
#~ msgstr "Fra"

#~ msgid "Results:"
#~ msgstr "Resultater:"

#~ msgid "Keep original configuration"
#~ msgstr "Behold oprindelig konfiguration"

#~ msgid "Load optimal configuration"
#~ msgstr "Indlæs optimal konfiguration"

#~ msgid "Load all configurations"
#~ msgstr "Indlæs alle konfigurationer"

#~ msgid "Strain\t\tEnergy [eV]"
#~ msgstr "Spænding\t\tEnergi [eV]"

#~ msgid "Fit:"
#~ msgstr "Fit:"

#~ msgid "2nd"
#~ msgstr "2."

#~ msgid "3rd"
#~ msgstr "3."

#~ msgid "Order of fit: "
#~ msgstr "Orden for fit: "

#~ msgid "Calculation CANCELLED."
#~ msgstr "Beregning AFBRUDT."

#~ msgid "Calculation completed."
#~ msgstr "Beregning fuldført."

#~ msgid "No trustworthy minimum: Old configuration kept."
#~ msgstr "Intet troværdigt minimum: Gammel konfiguration beholdt."

#~ msgid ""
#~ "Insufficent data for a fit\n"
#~ "(only %i data points)\n"
#~ msgstr ""
#~ "Utilstrækkelige data til fit\n"
#~ "(kun %i datapunkter)\n"

#~ msgid ""
#~ "REVERTING TO 2ND ORDER FIT\n"
#~ "(only 3 data points)\n"
#~ "\n"
#~ msgstr ""
#~ "GÅR NED TIL ANDENORDENS FIT\n"
#~ "(kun 3 datapunkter)\n"
#~ "\n"

#~ msgid "No minimum found!"
#~ msgstr "Intet minimum fundet!"

#~ msgid ""
#~ "\n"
#~ "WARNING: Minimum is outside interval\n"
#~ msgstr ""
#~ "\n"
#~ "ADVARSEL: Minimum ligger uden for interval\n"

#~ msgid "It is UNRELIABLE!\n"
#~ msgstr "Det er UTILREGNELIGT!\n"

#~ msgid "\n"
#~ msgstr "\n"

#~ msgid "No crystal structure data"
#~ msgstr "Ingen data for krystalstruktur"

#~ msgid "Tip for status box ..."
#~ msgstr "Fif til statusboks ..."

#~ msgid "Clear constraint"
#~ msgstr "Ryd begrænsninger"

#~ msgid "DFT"
#~ msgstr "DFT"

#~ msgid "XC-functional: "
#~ msgstr "XC-funktional: "

#~ msgid "DFT ..."
#~ msgstr "DFT ..."

#~ msgid "building menus failed: %s"
#~ msgstr "bygning af menuer mislykkedes: %s"

#~ msgid "Dacapo netCDF output file"
#~ msgstr "netCDF-uddatafil fra Dacapo"

#~ msgid "Virtual Nano Lab file"
#~ msgstr "Virtual Nano Lab-fil"

#~ msgid "ASE pickle trajectory"
#~ msgstr "Pickletrajectory fra ASE"

#~ msgid "ASE bundle trajectory"
#~ msgstr "Bundletrajectory fra ASE"

#~ msgid "GPAW text output"
#~ msgstr "Textudskrift fra GPAW"

#~ msgid "CUBE file"
#~ msgstr "CUBE-fil"

#~ msgid "XCrySDen Structure File"
#~ msgstr "XCrySDen-strukturfil"

#~ msgid "Dacapo text output"
#~ msgstr "Tekstudskrift fra Dacapo"

#~ msgid "XYZ-file"
#~ msgstr "XYZ-fil"

#~ msgid "VASP POSCAR/CONTCAR file"
#~ msgstr "POSCAR/CONTCAR-fil fra VASP"

#~ msgid "VASP OUTCAR file"
#~ msgstr "OUTCAR-fil fra VASP"

#~ msgid "Protein Data Bank"
#~ msgstr "Proteindatabank"

#~ msgid "CIF-file"
#~ msgstr "CIF-fil"

#~ msgid "FHI-aims geometry file"
#~ msgstr "FHI-aims-geometrifil"

#~ msgid "FHI-aims output file"
#~ msgstr "Uddatafil fra FHI-aims"

#~ msgid "TURBOMOLE coord file"
#~ msgstr "TURBOMOLE-koordinatfil"

# exciting er et program
#~ msgid "exciting input"
#~ msgstr "exciting-inddata"

#~ msgid "WIEN2k structure file"
#~ msgstr "WIEN2k-strukturfil"

#~ msgid "DftbPlus input file"
#~ msgstr "DftbPlus-inddatafil"

#~ msgid "ETSF format"
#~ msgstr "ETSF-format"

#~ msgid "CASTEP geom file"
#~ msgstr "CASTEP-geom-fil"

#~ msgid "CASTEP output file"
#~ msgstr "Uddatafil fra CASTEP"

#~ msgid "CASTEP trajectory file"
#~ msgstr "Trajectory-fil fra CASTEP"

#~ msgid "DFTBPlus GEN format"
#~ msgstr "GEN-format fra DFTBPlus"

#~ msgid ""
#~ "\n"
#~ "An exception occurred!  Please report the issue to\n"
#~ "<EMAIL> - thanks!  Please also report this "
#~ "if\n"
#~ "it was a user error, so that a better error message can be provided\n"
#~ "next time."
#~ msgstr ""
#~ "\n"
#~ "Der opstod en undtagelse!  Rapportér venligst dette problem til \n"
#~ "<EMAIL> - mange tak!  Rapportér også gerne "
#~ "dette\n"
#~ "hvis det var en brugerfejl, så der kan gives en bedre fejlmeddelelse "
#~ "næste\n"
#~ "gang."

#~ msgid "Max force: %.2f (this frame), %.2f (all frames)"
#~ msgstr "Maks. kraft: %.2f (dette billede), %.2f (alle billeder)"

#~ msgid "Max velocity: %.2f (this frame), %.2f (all frames)"
#~ msgstr "Maks. hastighed: %.2f (dette billede), %.2f (alle billeder)"

#~ msgid "Max velocity: %.2f."
#~ msgstr "Maks. hastighed: %.2f."

#~ msgid "Min, max charge: %.2f, %.2f (this frame),"
#~ msgstr "Min., maks. ladning: %.2f, %.2f (dette billede),"

#~ msgid "Min, max charge: %.2f, %.2f."
#~ msgstr "Min., maks. ladning: %.2f, %.2f."

#~ msgid "XYZ file"
#~ msgstr "XYZ-fil"

#~ msgid "ASE trajectory"
#~ msgstr "Trajectory fra ASE"

#~ msgid "PDB file"
#~ msgstr "PDB-fil"

#~ msgid "Gaussian cube file"
#~ msgstr "Cube-fil fra Gaussian"

#~ msgid "Python script"
#~ msgstr "Pythonscript"

#~ msgid "VNL file"
#~ msgstr "VNL-fil"

#~ msgid "Portable Network Graphics"
#~ msgstr "Portable Network Graphics"

#~ msgid "Persistence of Vision"
#~ msgstr "Persistence of Vision"

#~ msgid "Encapsulated PostScript"
#~ msgstr "Encapsulated PostScript"

#~ msgid "FHI-aims geometry input"
#~ msgstr "Geometriinddata til FHI-aims"

#~ msgid "VASP geometry input"
#~ msgstr "Geometriinddata til VASP"

#~ msgid "cif file"
#~ msgstr "cif-fil"

#~ msgid "Save current image only (#%d)"
#~ msgstr "Gem kun nuværende billede (#%d)"

# slice ~ opdel
#~ msgid "Slice: "
#~ msgstr "Del: "

#~ msgid "Help for slice ..."
#~ msgstr "Hjælp til opdeling ..."

#~ msgid "ase-gui INTERNAL ERROR: strange response in Save,"
#~ msgstr "INTERN FEJL I ase-gui: mystisk svar i Save,"

#~ msgid "Unknown output format!"
#~ msgstr "Ukendt uddataformat!"

#~ msgid "Use one of: %s"
#~ msgstr "Brug et af: %s"

#~ msgid "  %8.3f, %8.3f, %8.3f eV/Å\n"
#~ msgstr "  %8.3f, %8.3f, %8.3f eV/Å\n"

#~ msgid "%s (a=%.3f Å)"
#~ msgstr "%s (a=%.3f Å)"

#~ msgid "  %s: %s, Z=%i, %s"
#~ msgstr "  %s: %s, Z=%i, %s"

#~ msgid " #%d %s (%s): %.3f Å, %.3f Å, %.3f Å "
#~ msgstr " #%d %s (%s): %.3f Å, %.3f Å, %.3f Å "

#~ msgid " %s-%s: %.3f Å"
#~ msgstr " %s-%s: %.3f Å"

#~ msgid " %s-%s-%s: %.1f°, %.1f°, %.1f°"
#~ msgstr " %s-%s-%s: %.1f°, %.1f°, %.1f°"

#~ msgid "dihedral %s->%s->%s->%s: %.1f°"
#~ msgstr "dihedral %s->%s->%s->%s: %.1f°"

#~ msgid "c:"
#~ msgstr "c:"

#~ msgid "\t\t%.2f Å x %.2f Å x %.2f Å,  %i atoms."
#~ msgstr "\t\t%.2f Å x %.2f Å x %.2f Å,  %i atomer."

#~ msgid "FILE"
#~ msgstr "FIL"

#~ msgid "%prog [options] [file[, file2, ...]]"
#~ msgstr "%prog [tilvalg] [fil[, fil2, ...]]"

#~ msgid "NUMBER"
#~ msgstr "NUMMER"

#~ msgid ""
#~ "Pick image(s) from trajectory.  NUMBER can be a single number (use a "
#~ "negative number to count from the back) or a range: start:stop:step, "
#~ "where the \":step\" part can be left out - default values are 0:nimages:1."
#~ msgstr ""
#~ "Vælg billeder fra traj-fil.  NUMMER kan være et enkelt tal (brug et "
#~ "negativt tal til at tælle bagfra) eller et interval på formen start:stop:"
#~ "trin, hvor elementet \":trin\" kan udelades.  Standardværdi er 0:"
#~ "antalbilleder:1."

#~ msgid "I"
#~ msgstr "I"

#~ msgid ""
#~ "0: Don't show unit cell.  1: Show unit cell.  2: Show all of unit cell."
#~ msgstr ""
#~ "0: Vis ikke enhedscellen.  1: Vis enhedscellen.  2: Vis hele enhedscellen."

#~ msgid "Repeat unit cell.  Use \"-r 2\" or \"-r 2,3,1\"."
#~ msgstr "Gentag enhedscellen.  Brug \"-r 2\" eller \"-r 2,3,1\"."

#~ msgid "Examples: \"-R -90x\", \"-R 90z,-30x\"."
#~ msgstr "Eksempler: \"-R -90x\", \"-R 90z,-30x\"."

#~ msgid "Write configurations to FILE."
#~ msgstr "Skriv konfigurationer til FIL."

#~ msgid "EXPR"
#~ msgstr "UDTRYK"

#~ msgid ""
#~ "Plot x,y1,y2,... graph from configurations or write data to sdtout in "
#~ "terminal mode.  Use the symbols: i, s, d, fmax, e, ekin, A, R, E and F.  "
#~ "See https://wiki.fysik.dtu.dk/ase/ase/gui.html#plotting-data for more "
#~ "details."
#~ msgstr ""
#~ "Tegn graf for x,y1,y2,... fra konfigurationer, eller skriv data til "
#~ "stdout i teksttilstand.  Brug symbolerne i, s, d, fmax, e, ekin, A, R, E "
#~ "og F.  Yderligere detaljer kan findes på https://wiki.fysik.dtu.dk/ase/"
#~ "ase/gui.html#plotting-data for more details."

#~ msgid "Run in terminal window - no GUI."
#~ msgstr "Kør i terminalvindue - uden grafisk grænseflade."

#~ msgid "Read ANEB data."
#~ msgstr "Læs ANEB-data."

#~ msgid "Interpolate N images between 2 given images."
#~ msgstr "Interpolér N billeder mellem to givne billeder."

#~ msgid "Draw bonds between atoms."
#~ msgstr "Tegn bindinger mellem atomer."
