# This makefile is used to update templates for internationalization ("i18n")
# of ase-gui.

# INSTRUCTIONS
# ------------
#
# To update existing templates with strings from the latest python files,
# just run 'make'.
#
# To create a translation template for a new language (e.g. de_DE.UTF8), run:
#     LANG=de_DE.UTF8 make init
#
# After writing translations, you need to run 'make' again to generate the
# binary translation files loaded by the programme.
#
# Then you can run 'LANG=de_DE.UTF8 ase-gui' and your translations should be
# visible if you have a developer installation.
#
# During build, the po-files are globbed from setup.py so
# they are automatically included when you run install.

# List of files calling gettext.
TRANSLATIONFILES=../add.py ../celleditor.py ../colors.py ../constraints.py ../graphs.py ../gui.py ../images.py ../modify.py ../movie.py ../nanoparticle.py ../nanotube.py ../quickinfo.py ../render.py ../repeat.py ../rotate.py ../save.py ../settings.py ../status.py ../surfaceslab.py ../ui.py ../view.py ../widgets.py

#TRANSLATIONFILES=../ag.py ../calculator.py ../colors.py ../constraints.py ../crystal.py ../debug.py ../dft.py ../energyforces.py ../execute.py ../graphs.py ../gtkexcepthook.py ../gui.py ../minimize.py ../movie.py ../nanoparticle.py ../nanotube.py ../progress.py ../pybutton.py ../quickinfo.py ../render.py ../repeat.py ../rotate.py ../scaling.py ../settings.py ../setupwindow.py ../simulation.py ../status.py ../surfaceslab.py ../widgets.py
#TRANSLATIONFILES=../*.py

i18n: ag.pot update-po compile

# This will update the English template (ag.pot) with English messages from
# the Python source code.
ag.pot: ${TRANSLATIONFILES}
	xgettext --from-code=utf-8 --add-comments --language=Python --keyword=_ --output=ag.pot --msgid-bugs-address=<EMAIL> --copyright-holder="ASE developers" --package-name=ase ${TRANSLATIONFILES}

# This will create an empty translation file ($LANG.po, where $LANG is
# an environment variable) from the English template ag.pot.
# The po-file header will have to be edited afterwards.
init: ag.pot
	mkdir -p ${LANG}/LC_MESSAGES
	msginit --locale=${LANG} --input=ag.pot --output-file=${LANG}/LC_MESSAGES/ag.po

# This will merge new/changed English strings from the template ag.pot
# into all existing translations (*.po), maximally
# reusing the existing translations.
update-po: ag.pot
	for FILE in $(wildcard */LC_MESSAGES/ag.po); do \
	  msgmerge --previous --update $$FILE ag.pot ;\
	done

# This will compile all translations (*.po) into binary files in gettext's
# search directory.
compile: */LC_MESSAGES/ag.po
	for DIR in $(wildcard */LC_MESSAGES); do \
	  echo Checking $$DIR/ag.po ;\
	  msgfmt -cv --output-file=$$DIR/ag.mo $$DIR/ag.po ;\
	done

gitrevert:
	git checkout -- '*/LC_MESSAGES/ag.po'

clean:
	rm -f ./*/LC_MESSAGES/ag.mo
	rm -f ./*/LC_MESSAGES/ag.po~
