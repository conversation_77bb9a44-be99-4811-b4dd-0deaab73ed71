# fmt: off

pw_keys = {
    "control": [
        "calculation",
        "title",
        "verbosity",
        "restart_mode",
        "wf_collect",
        "nstep",
        "iprint",
        "tstress",
        "tprnfor",
        "dt",
        "outdir",
        "wfcdir",
        "prefix",
        "lkpoint_dir",
        "max_seconds",
        "etot_conv_thr",
        "forc_conv_thr",
        "disk_io",
        "pseudo_dir",
        "tefield",
        "dipfield",
        "lelfield",
        "nberrycyc",
        "lorbm",
        "lberry",
        "gdir",
        "nppstr",
        "gate",
        "twochem",
        "lfcp",
        "trism"
    ],
    "system": [
        "ibrav",
        "celldm",
        "a",
        "b",
        "c",
        "cosab",
        "cosac",
        "cosbc",
        "nat",
        "ntyp",
        "nbnd",
        "nbnd_cond",
        "tot_charge",
        "starting_charge",
        "tot_magnetization",
        "starting_magnetization",
        "ecutwfc",
        "ecutrho",
        "ecutfock",
        "nr1",
        "nr2",
        "nr3",
        "nr1s",
        "nr2s",
        "nr3s",
        "nosym",
        "nosym_evc",
        "noinv",
        "no_t_rev",
        "force_symmorphic",
        "use_all_frac",
        "occupations",
        "one_atom_occupations",
        "starting_spin_angle",
        "degauss_cond",
        "nelec_cond",
        "degauss",
        "smearing",
        "nspin",
        "sic_gamma",
        "pol_type",
        "sic_energy",
        "sci_vb",
        "sci_cb",
        "noncolin",
        "ecfixed",
        "qcutz",
        "q2sigma",
        "input_dft",
        "ace",
        "exx_fraction",
        "screening_parameter",
        "exxdiv_treatment",
        "x_gamma_extrapolation",
        "ecutvcut",
        "nqx1",
        "nqx2",
        "nqx3",
        "localization_thr",
        "hubbard_occ",
        "hubbard_alpha",
        "hubbard_beta",
        "starting_ns_eigenvalue",
        "dmft",
        "dmft_prefix",
        "ensemble_energies",
        "edir",
        "emaxpos",
        "eopreg",
        "eamp",
        "angle1",
        "angle2",
        "lforcet",
        "constrained_magnetization",
        "fixed_magnetization",
        "lambda",
        "report",
        "lspinorb",
        "assume_isolated",
        "esm_bc",
        "esm_w",
        "esm_efield",
        "esm_nfit",
        "lgcscf",
        "gcscf_mu",
        "gcscf_conv_thr",
        "gcscf_beta",
        "vdw_corr",
        "london",
        "london_s6",
        "london_c6",
        "london_rvdw",
        "london_rcut",
        "dftd3_version",
        "dftd3_threebody",
        "ts_vdw_econv_thr",
        "ts_vdw_isolated",
        "xdm",
        "xdm_a1",
        "xdm_a2",
        "space_group",
        "uniqueb",
        "origin_choice",
        "rhombohedral",
        "zgate",
        "relaxz",
        "block",
        "block_1",
        "block_2",
        "block_height",
        "nextffield"
    ],
    "electrons": [
        "electron_maxstep",
        "exx_maxstep",
        "scf_must_converge",
        "conv_thr",
        "adaptive_thr",
        "conv_thr_init",
        "conv_thr_multi",
        "mixing_mode",
        "mixing_beta",
        "mixing_ndim",
        "mixing_fixed_ns",
        "diagonalization",
        "diago_thr_init",
        "diago_cg_maxiter",
        "diago_ppcg_maxiter",
        "diago_david_ndim",
        "diago_rmm_ndim",
        "diago_rmm_conv",
        "diago_gs_nblock",
        "diago_full_acc",
        "efield",
        "efield_cart",
        "efield_phase",
        "startingpot",
        "startingwfc",
        "tqr",
        "real_space"
    ],
    "ions": [
        "ion_positions",
        "ion_velocities",
        "ion_dynamics",
        "pot_extrapolation",
        "wfc_extrapolation",
        "remove_rigid_rot",
        "ion_temperature",
        "tempw",
        "tolp",
        "delta_t",
        "nraise",
        "refold_pos",
        "upscale",
        "bfgs_ndim",
        "trust_radius_max",
        "trust_radius_min",
        "trust_radius_ini",
        "w_1",
        "w_2",
        "fire_alpha_init",
        "fire_falpha",
        "fire_nmin",
        "fire_f_inc",
        "fire_f_dec",
        "fire_dtmax"
    ],
    "cell": [
        "cell_dynamics",
        "press",
        "wmass",
        "cell_factor",
        "press_conv_thr",
        "cell_dofree"
    ],
    "fcp": [
        "fcp_mu",
        "fcp_dynamics",
        "fcp_conv_thr",
        "fcp_ndiis",
        "fcp_mass",
        "fcp_velocity",
        "fcp_temperature",
        "fcp_tempw",
        "fcp_tolp",
        "fcp_delta_t",
        "fcp_nraise",
        "freeze_all_atoms"
    ],
    "rism": [
        "nsolv",
        "closure",
        "tempv",
        "ecutsolv",
        "solute_lj",
        "solute_epsilon",
        "solute_sigma",
        "starting1d",
        "starting3d",
        "smear1d",
        "smear3d",
        "rism1d_maxstep",
        "rism3d_maxstep",
        "rism1d_conv_thr",
        "rism3d_conv_thr",
        "mdiis1d_size",
        "mdiis3d_size",
        "mdiis1d_step",
        "mdiis3d_step",
        "rism1d_bond_width",
        "rism1d_dielectric",
        "rism1d_molesize",
        "rism1d_nproc",
        "rism3d_conv_level",
        "rism3d_planar_average",
        "laue_nfit",
        "laue_expand_right",
        "laue_expand_left",
        "laue_starting_right",
        "laue_starting_left",
        "laue_buffer_right",
        "laue_buffer_left",
        "laue_both_hands",
        "laue_wall",
        "laue_wall_z",
        "laue_wall_rho",
        "laue_wall_epsilon",
        "laue_wall_sigma",
        "laue_wall_lj6"
    ]
}

ph_keys = {
    "inputph": [
        "amass",
        "outdir",
        "prefix",
        "niter_ph",
        "tr2_ph",
        "alpha_mix",
        "nmix_ph",
        "verbosity",
        "reduce_io",
        "max_seconds",
        "dftd3_hess",
        "fildyn",
        "fildrho",
        "fildvscf",
        "epsil",
        "lrpa",
        "lnoloc",
        "trans",
        "lraman",
        "eth_rps",
        "eth_ns",
        "dek",
        "recover",
        "low_directory_check",
        "only_init",
        "qplot",
        "q2d",
        "q_in_band_form",
        "electron_phonon",
        "el_ph_nsigma",
        "el_ph_sigma",
        "ahc_dir",
        "ahc_nbnd",
        "ahc_nbndskip",
        "skip_upperfan",
        "lshift_q",
        "zeu",
        "zue",
        "elop",
        "fpol",
        "ldisp",
        "nogg",
        "asr",
        "ldiag",
        "lqdir",
        "search_sym",
        "nq1",
        "nq2",
        "nq3",
        "nk1",
        "nk2",
        "nk3",
        "k1",
        "k2",
        "k3",
        "diagonalization",
        "read_dns_bare",
        "ldvscf_interpolate",
        "wpot_dir",
        "do_long_range",
        "do_charge_neutral",
        "start_irr",
        "last_irr",
        "nat_todo",
        "modenum",
        "start_q",
        "last_q",
        "dvscf_star",
        "drho_star",
    ]
}

pp_keys = {
    "inputpp": [
        "prefix",
        "outdir",
        "filplot",
        "plot_num",
        "spin_component",
        "spin_component",
        "emin",
        "emax",
        "delta_e",
        "degauss_ldos",
        "use_gauss_ldos",
        "sample_bias",
        "kpoint",
        "kband",
        "lsign",
        "spin_component",
        "emin",
        "emax",
        "spin_component",
        "spin_component",
        "spin_component",
        "spin_component",
    ],
    "plot": [
        "nfile",
        "filepp",
        "weight",
        "iflag",
        "output_format",
        "fileout",
        "interpolation",
        "e1",
        "x0",
        "nx",
        "e1",
        "e2",
        "x0",
        "nx",
        "ny",
        "e1",
        "e2",
        "e3",
        "x0",
        "nx",
        "ny",
        "nz",
        "radius",
        "nx",
        "ny",
    ],
}

matdyn_keys = {
    "input": [
        "flfrc",
        "asr",
        "huang",
        "dos",
        "nk1",
        "nk2",
        "nk3",
        "deltae",
        "ndos",
        "degauss",
        "fldos",
        "flfrq",
        "flvec",
        "fleig",
        "fldyn",
        "at",
        "l1",
        "l2",
        "l3",
        "ntyp",
        "amass",
        "readtau",
        "fltau",
        "la2f",
        "q_in_band_form",
        "q_in_cryst_coord",
        "eigen_similarity",
        "fd",
        "na_ifc",
        "nosym",
        "loto_2d",
        "loto_disable",
        "read_lr",
        "write_frc",
    ]
}


dynmat_keys = {
    "input": [
        "fildyn",
        "q",
        "amass",
        "asr",
        "remove_interaction_blocks",
        "axis",
        "lperm",
        "lplasma",
        "filout",
        "fileig",
        "filmol",
        "filxsf",
        "loto_2d",
        "el_ph_nsig",
        "el_ph_sigma",
    ]
}


q2r_keys = {"input": ["fildyn", "flfrc", "zasr", "loto_2d", "write_lr"]}

dos_keys = {
    "dos": [
        "prefix",
        "outdir",
        "bz_sum",
        "ngauss",
        "degauss",
        "emin",
        "emax",
        "deltae",
        "fildos",
    ]
}


bands_keys = {
    "bands": [
        "prefix",
        "outdir",
        "filband",
        "spin_component",
        "lsigma",
        "lp",
        "filp",
        "lsym",
        "no_overlap",
        "plot_2d",
        "firstk",
        "lastk",
    ]
}


band_interpolation_keys = {
    "interpolation": [
        "method",
        "miller_max",
        "check_periodicity",
        "p_metric",
        "scale_sphere",
    ]
}


projwfc_keys = {
    "projwfc": [
        "prefix",
        "outdir",
        "ngauss",
        "degauss",
        "emin",
        "emax",
        "deltae",
        "lsym",
        "diag_basis",
        "pawproj",
        "filpdos",
        "filproj",
        "lwrite_overlaps",
        "lbinary_data",
        "kresolveddos",
        "tdosinboxes",
        "n_proj_boxes",
        "irmin",
        "irmax",
        "plotboxes",
    ]
}


molecularpdos_keys = {
    "inputmopdos": [
        "xmlfile_full",
        "xmlfile_part",
        "i_atmwfc_beg_full",
        "i_atmwfc_end_full",
        "i_atmwfc_beg_part",
        "i_atmwfc_end_part",
        "i_bnd_beg_full",
        "i_bnd_end_full",
        "i_bnd_beg_part",
        "i_bnd_end_part",
        "fileout",
        "ngauss",
        "degauss",
        "emin",
        "emax",
        "deltae",
        "kresolveddos",
    ]
}


importexport_binary_keys = {
    "inputpp": [
        "prefix",
        "outdir",
        "direction",
        "newoutdir"]}


oscdft_pp_keys = {"oscdft_pp_namelist": ["prefix", "outdir"]}

kcw_keys = {
    "control": [
        "prefix",
        "outdir",
        "calculation",
        "kcw_iverbosity",
        "kcw_at_ks",
        "read_unitary_matrix",
        "spread_thr",
        "homo_only",
        "l_vcut",
        "assume_isolated",
        "spin_component",
        "mp1",
        "mp2",
        "mp3",
        "lrpa",
    ],
    "wannier": [
        "seedname",
        "num_wann_occ",
        "num_wann_emp",
        "have_empty",
        "has_disentangle",
        "check_ks",
    ],
    "screen": ["niter", "nmix", "tr2", "i_orb", "eps_inf", "check_spread"],
    "ham": ["do_bands", "use_ws_distance", "write_hr", "on_site_only"],
}


cppp_keys = {
    "inputpp": [
        "prefix",
        "fileout",
        "output",
        "outdir",
        "lcharge",
        "lforces",
        "ldynamics",
        "lpdb",
        "lrotation",
        "np1",
        "np2",
        "np3",
        "nframes",
        "ndr",
        "atomic_number",
    ]
}


ppacf_keys = {
    "ppacf": [
        "prefix",
        "outdir",
        "n_lambda",
        "lplot",
        "ltks",
        "lfock",
        "use_ace",
        "code_num",
        "vdw_analysis",
    ]
}


all_currents_keys = {
    "energy_current": [
        "delta_t",
        "file_output",
        "trajdir",
        "vel_input_units",
        "eta",
        "n_max",
        "first_step",
        "last_step",
        "step_mul",
        "step_rem",
        "ethr_small_step",
        "ethr_big_step",
        "restart",
        "subtract_cm_vel",
        "add_i_current_b",
        "save_dvpsi",
        "re_init_wfc_1",
        "re_init_wfc_2",
        "re_init_wfc_3",
        "three_point_derivative",
        "n_repeat_every_step",
        "n_workers",
        "worker_id",
        "continue_not_converged",
    ]
}


turbo_lanczos_keys = {
    "lr_input": [
        "prefix",
        "outdir",
        "wfcdir",
        "restart",
        "restart_step",
        "lr_verbosity",
        "disk_io",
    ],
    "lr_control": [
        "itermax",
        "ipol",
        "n_ipol",
        "ltammd",
        "no_hxc",
        "lrpa",
        "scissor",
        "charge_response",
        "pseudo_hermitian",
        "d0psi_rs",
        "lshift_d0psi",
    ],
    "lr_post": [
        "omeg",
        "epsil",
        "beta_gamma_z_prefix",
        "w_t_npol",
        "plot_type"],
}


turbo_spectrum_keys = {
    "lr_input": [
        "prefix",
        "outdir",
        "verbosity",
        "itermax0",
        "itermax",
        "extrapolation",
        "epsil",
        "units",
        "start",
        "end",
        "increment",
        "ipol",
        "eels",
        "magnons",
        "td",
        "eign_file",
    ]
}


turbo_davidson_keys = {
    "lr_input": [
        "prefix",
        "outdir",
        "wfcdir",
        "max_seconds",
        "restart",
        "lr_verbosity",
        "disk_io",
    ],
    "lr_dav": [
        "num_eign",
        "num_init",
        "if_random_init",
        "num_basis_max",
        "residue_conv_thr",
        "precondition",
        "single_pole",
        "if_dft_spectrum",
        "reference",
        "broadening",
        "start",
        "finish",
        "step",
        "p_nbnd_occ",
        "p_nbnd_virt",
        "poor_of_ram",
        "poor_of_ram2",
        "max_iter",
        "no_hxc",
        "pseudo_hermitian",
        "ltammd",
        "lplot_drho",
        "d0psi_rs",
        "lshift_d0psi",
    ],
}


turbo_magnon_keys = {
    "lr_input": [
        "prefix",
        "outdir",
        "restart",
        "restart_step",
        "lr_verbosity",
        "disk_io",
    ],
    "lr_control": [
        "itermax",
        "pseudo_hermitian",
        "approximation",
        "ipol",
        "q1",
        "q2",
        "q3",
    ],
}


turbo_eels_keys = {
    "lr_input": [
        "prefix",
        "outdir",
        "restart",
        "restart_step",
        "lr_verbosity",
        "disk_io",
    ],
    "lr_control": [
        "approximation",
        "q1",
        "q2",
        "q3",
        "calculator",
        "itermax",
        "pseudo_hermitian",
        "alpha_mix(i",
        "epsil",
        "units",
        "start",
        "end",
        "increment",
        "ethr_nscf",
    ],
}


oscdft_et_keys = {
    "oscdft_et_namelist": [
        "initial_prefix",
        "final_prefix",
        "initial_dir",
        "final_dir",
        "print_matrix",
        "print_eigvect",
        "print_debug",
    ]
}


pprism_keys = {
    "inputpp": ["prefix", "outdir", "filplot", "lpunch"],
    "plot": [
        "iflag",
        "output_format",
        "fileout",
        "interpolation",
        "x0",
        "nx",
        "lebedev",
        "e1",
        "x0",
        "nx",
        "e1",
        "e2",
        "x0",
        "nx",
        "ny",
        "e1",
        "e2",
        "e3",
        "x0",
        "nx",
        "ny",
        "nz",
        "radius",
        "nx",
        "ny",
    ],
}


pwcond_keys = {
    "inputcond": [
        "outdir",
        "prefixt",
        "prefixl",
        "prefixs",
        "prefixr",
        "tran_prefix",
        "max_seconds",
        "recover",
        "band_file",
        "tran_file",
        "save_file",
        "fil_loc",
        "lwrite_cond",
        "loop_ek",
        "lread_cond",
        "lwrite_loc",
        "lread_loc",
        "ikind",
        "iofspin",
        "tk_plot",
        "llocal",
        "bdl",
        "bds",
        "bdr",
        "nz1",
        "energy0",
        "denergy",
        "nenergy",
        "start_e",
        "last_e",
        "start_k",
        "last_k",
        "ecut2d",
        "ewind",
        "epsproj",
        "orbj_in",
        "orbj_fin",
    ]
}


pw2bgw_keys = {
    "input_pw2bgw": [
        "prefix",
        "outdir",
        "real_or_complex",
        "symm_type",
        "wfng_flag",
        "wfng_file",
        "wfng_kgrid",
        "wfng_nk1",
        "wfng_nk2",
        "wfng_nk3",
        "wfng_dk1",
        "wfng_dk2",
        "wfng_dk3",
        "wfng_occupation",
        "wfng_nvmin",
        "wfng_nvmax",
        "rhog_flag",
        "rhog_file",
        "rhog_nvmin",
        "rhog_nvmax",
        "vxcg_flag",
        "vxcg_file",
        "vxc0_flag",
        "vxc0_file",
        "vxc_flag",
        "vxc_file",
        "vxc_integral",
        "vxc_diag_nmin",
        "vxc_diag_nmax",
        "vxc_offdiag_nmin",
        "vxc_offdiag_nmax",
        "vxc_zero_rho_core",
        "vscg_flag",
        "vscg_file",
        "vkbg_flag",
        "vkbg_file",
    ]
}


bgw2pw_keys = {
    "input_bgw2pw": [
        "prefix",
        "outdir",
        "real_or_complex",
        "wfng_flag",
        "wfng_file",
        "wfng_nband",
        "rhog_flag",
        "rhog_file",
    ]
}


hp_keys = {
    "inputhp": [
        "prefix",
        "outdir",
        "iverbosity",
        "max_seconds",
        "nq1",
        "nq2",
        "nq3",
        "skip_equivalence_q",
        "determine_num_pert_only",
        "determine_q_mesh_only",
        "find_atpert",
        "docc_thr",
        "skip_type",
        "equiv_type",
        "perturb_only_atom",
        "start_q",
        "last_q",
        "sum_pertq",
        "compute_hp",
        "conv_thr_chi",
        "thresh_init",
        "ethr_nscf",
        "niter_max",
        "alpha_mix(i",
        "nmix",
        "num_neigh",
        "lmin",
        "rmax",
        "dist_thr",
    ]
}


cp_keys = {
    "control": [
        "calculation",
        "title",
        "verbosity",
        "isave",
        "restart_mode",
        "nstep",
        "iprint",
        "tstress",
        "tprnfor",
        "dt",
        "outdir",
        "saverho",
        "prefix",
        "ndr",
        "ndw",
        "tabps",
        "max_seconds",
        "etot_conv_thr",
        "forc_conv_thr",
        "ekin_conv_thr",
        "disk_io",
        "memory",
        "pseudo_dir",
        "tefield",
    ],
    "system": [
        "ibrav",
        "celldm",
        "a",
        "b",
        "c",
        "cosab",
        "cosac",
        "cosbc",
        "nat",
        "ntyp",
        "nbnd",
        "tot_charge",
        "tot_magnetization",
        "ecutwfc",
        "ecutrho",
        "nr1",
        "nr2",
        "nr3",
        "nr1s",
        "nr2s",
        "nr3s",
        "nr1b",
        "nr2b",
        "nr3b",
        "occupations",
        "degauss",
        "smearing",
        "nspin",
        "ecfixed",
        "qcutz",
        "q2sigma",
        "input_dft",
        "exx_fraction",
        "lda_plus_u",
        "hubbard_u",
        "vdw_corr",
        "london_s6",
        "london_rcut",
        "ts_vdw",
        "ts_vdw_econv_thr",
        "ts_vdw_isolated",
        "assume_isolated",
    ],
    "electrons": [
        "electron_maxstep",
        "electron_dynamics",
        "conv_thr",
        "niter_cg_restart",
        "efield",
        "epol",
        "emass",
        "emass_cutoff",
        "orthogonalization",
        "ortho_eps",
        "ortho_max",
        "ortho_para",
        "electron_damping",
        "electron_velocities",
        "electron_temperature",
        "ekincw",
        "fnosee",
        "startingwfc",
        "tcg",
        "maxiter",
        "passop",
        "pre_state",
        "n_inner",
        "niter_cold_restart",
        "lambda_cold",
        "grease",
        "ampre",
    ],
    "ions": [
        "ion_dynamics",
        "ion_positions",
        "ion_velocities",
        "ion_damping",
        "ion_radius",
        "iesr",
        "ion_nstepe",
        "remove_rigid_rot",
        "ion_temperature",
        "tempw",
        "fnosep",
        "tolp",
        "nhpcl",
        "nhptyp",
        "nhgrp",
        "fnhscl",
        "ndega",
        "tranp",
        "amprp",
        "greasp",
    ],
    "cell": [
        "cell_parameters",
        "cell_dynamics",
        "cell_velocities",
        "cell_damping",
        "press",
        "wmass",
        "cell_factor",
        "cell_temperature",
        "temph",
        "fnoseh",
        "greash",
        "cell_dofree",
    ],
    "press_ai": [
        "abivol",
        "abisur",
        "p_ext",
        "pvar",
        "p_in",
        "p_fin",
        "surf_t",
        "rho_thr",
        "dthr",
    ],
    "wannier": [
        "wf_efield",
        "wf_switch",
        "sw_len",
        "efx0",
        "efy0",
        "efz0",
        "efx1",
        "efy1",
        "efz1",
        "wfsd",
        "wfdt",
        "maxwfdt",
        "nit",
        "nsd",
        "wf_q",
        "wf_friction",
        "nsteps",
        "tolw",
        "adapt",
        "calwf",
        "nwf",
        "wffort",
        "writev",
        "exx_neigh",
        "exx_dis_cutoff",
        "exx_poisson_eps",
        "exx_use_cube_domain",
        "exx_ps_rcut_self",
        "exx_ps_rcut_pair",
        "exx_me_rcut_self",
        "exx_me_rcut_pair",
    ],
}


ld1_keys = {
    "input": [
        "title",
        "zed",
        "atom",
        "xmin",
        "dx",
        "rmax",
        "beta",
        "tr2",
        "iswitch",
        "nld",
        "rlderiv",
        "eminld",
        "emaxld",
        "deld",
        "rpwe",
        "rel",
        "lsmall",
        "max_out_wfc",
        "noscf",
        "lsd",
        "dft",
        "latt",
        "isic",
        "rytoev_fact",
        "cau_fact",
        "vdw",
        "prefix",
        "verbosity",
        "file_charge",
        "config",
        "relpert",
        "rel_dist",
        "write_coulomb",
    ],
    "inputp": [
        "zval",
        "pseudotype",
        "file_pseudopw",
        "file_recon",
        "lloc",
        "rcloc",
        "nlcc",
        "new_core_ps",
        "rcore",
        "tm",
        "rho0",
        "lpaw",
        "which_augfun",
        "rmatch_augfun",
        "rmatch_augfun_nc",
        "lsave_wfc",
        "lgipaw_reconstruction",
        "use_paw_as_gipaw",
        "author",
        "file_chi",
        "file_beta",
        "file_qvan",
        "file_screen",
        "file_core",
        "file_wfcaegen",
        "file_wfcncgen",
        "file_wfcusgen",
    ],
    "test": [
        "nconf",
        "file_pseudo",
        "ecutmin",
        "ecutmax",
        "decut",
        "rm",
        "configts",
        "lsdts",
        "frozen_core",
        "rcutv",
    ],
}


d3hess_keys = {"input": ["prefix", "outdir", "filhess", "step"]}

neb_keys = {
    "path": [
        "string_method",
        "restart_mode",
        "nstep_path",
        "num_of_images",
        "opt_scheme",
        "ci_scheme",
        "first_last_opt",
        "minimum_image",
        "temp_req",
        "ds",
        "k_max",
        "k_min",
        "path_thr",
        "use_masses",
        "use_freezing",
        "lfcp",
        "fcp_mu",
        "fcp_thr",
        "fcp_scheme",
    ]
}

postahc_keys = {
    "input": [
        "ahc_dir",
        "nk",
        "nbnd",
        "nat",
        "nq",
        "ahc_nbnd",
        "ahc_nbndskip",
        "flvec",
        "eta",
        "temp_kelvin",
        "efermi",
        "amass_amu",
        "skip_upperfan",
        "skip_dw"
    ]
}

dvscf_q2r = {
    "input": [
        "prefix",
        "outdir",
        "fildyn",
        "fildvscf",
        "wpot_dir",
        "do_long_range",
        "do_charge_neutral",
        "verbosity"
    ]
}

epw_keys = {
    "inputepw": [
        "adapt_ethrdg_plrn",
        "a2f",
        "amass",
        "asr_typ",
        "assume_metal",
        "band_plot",
        "bands_skipped",
        "bfieldx",
        "bfieldy",
        "bfieldz",
        "bnd_cum",
        "broyden_beta",
        "broyden_ndim",
        "cal_psir_plrn",
        "carrier",
        "conv_thr_iaxis",
        "conv_thr_plrn",
        "conv_thr_racon",
        "conv_thr_raxis",
        "cumulant",
        "degaussq",
        "degaussw",
        "delta_approx",
        "delta_qsmear",
        "delta_smear",
        "dvscf_dir",
        "do_CHBB",
        "efermi_read",
        "eig_read",
        "elecselfen",
        "eliashberg",
        "elph",
        "ep_coupling",
        "epbwrite",
        "epbread",
        "epexst",
        "ephwrite",
        "epmatkqread",
        "eps_acustic",
        "epsiHEG",
        "epwread",
        "epwwrite",
        "etf_mem",
        "ethrdg_plrn",
        "fermi_diff",
        "fermi_energy",
        "fermi_plot",
        "fila2f",
        "fildvscf",
        "filkf",
        "filqf",
        "filukk",
        "filukq",
        "fixsym",
        "fsthick",
        "gap_edge",
        "gb_scattering",
        "gb_only",
        "gb_size",
        "imag_read",
        "init_ethrdg_plrn",
        "init_k0_plrn",
        "init_ntau_plrn",
        "init_plrn",
        "init_sigma_plrn",
        "interp_Ank_plrn",
        "interp_Bqu_plrn",
        "int_mob",
        "io_lvl_plrn",
        "iterative_bte",
        "iverbosity",
        "ii_g",
        "ii_scattering",
        "ii_only",
        "ii_lscreen",
        "ii_partion",
        "ii_charge",
        "ii_n",
        "ii_eda",
        "kerread",
        "kerwrite",
        "kmaps",
        "lacon",
        "laniso",
        "lifc",
        "limag",
        "lindabs",
        "liso",
        "longrange",
        "lpade",
        "lphase",
        "lpolar",
        "lreal",
        "lscreen",
        "lunif",
        "loptabs",
        "len_mesh",
        "max_memlt",
        "meff",
        "mob_maxiter",
        "mp_mesh_k",
        "mp_mesh_q",
        "muc",
        "meshnum",
        "nbndsub",
        "ncarrier",
        "nc",
        "nel",
        "nest_fn",
        "nethrdg_plrn",
        "ngaussw",
        "niter_plrn",
        "nk1",
        "nk2",
        "nk3",
        "nkf1",
        "nkf2",
        "nkf3",
        "nq1",
        "nq2",
        "nq3",
        "nqf1",
        "nqf2",
        "nqf3",
        "npade",
        "nqsmear",
        "nqstep",
        "n_r",
        "nsiter",
        "nsmear",
        "nstemp",
        "nswi",
        "nswc",
        "nswfc",
        "nw",
        "nw_specfun",
        "nq_init",
        "omegamax",
        "omegamin",
        "omegastep",
        "phonselfen",
        "plselfen",
        "plrn",
        "prefix",
        "prtgkk",
        "pwc",
        "QD_bin",
        "QD_min",
        "rand_nq",
        "rand_nk",
        "rand_q",
        "rand_k",
        "restart",
        "restart_filq",
        "restart_plrn",
        "restart_step",
        "scell_mat",
        "scell_mat_plrn",
        "scr_typ",
        "scatread",
        "scattering",
        "scattering_serta",
        "scattering_0rta",
        "scissor",
        "selecqread",
        "smear_rpa",
        "specfun_el",
        "specfun_ph",
        "specfun_pl",
        "system_2d",
        "shortrange",
        "step_wf_grid_plrn",
        "start_mesh",
        "temps",
        "tc_linear",
        "tc_linear_solver",
        "type_plrn",
        "vme",
        "wannierize",
        "wepexst",
        "wmax",
        "wmax_specfun",
        "wmin",
        "wmin_specfun",
        "wscut",
        "wsfc",
        "auto_projections",
        "dis_froz_min",
        "dis_froz_max",
        "iprint",
        "num_iter",
        "proj",
        "reduce_unk",
        "scdm_entanglement",
        "scdm_mu",
        "scdm_proj",
        "scdm_sigma",
        "wannier_plot",
        "wannier_plot_list",
        "wannier_plot_radius",
        "wannier_plot_scale",
        "wannier_plot_supercell",
        "wdata"
    ]
}

ALL_KEYS = {
    "pw": pw_keys,
    "ph": ph_keys,
    "phcg": ph_keys,
    "pp": pp_keys,
    "matdyn": matdyn_keys,
    "dynmat": dynmat_keys,
    "q2r": q2r_keys,
    "dos": dos_keys,
    "bands": bands_keys,
    "bands_interpolation": band_interpolation_keys,
    "projwfc": projwfc_keys,
    "molecularpdos": molecularpdos_keys,
    "importexport_binary": importexport_binary_keys,
    "oscdft_pp": oscdft_pp_keys,
    "kcw": kcw_keys,
    "cppp": cppp_keys,
    "ppacf": ppacf_keys,
    "all_currents": all_currents_keys,
    "turbo_lanczos": turbo_lanczos_keys,
    "turbo_spectrum": turbo_spectrum_keys,
    "turbo_davidson": turbo_davidson_keys,
    "turbo_magnon": turbo_magnon_keys,
    "turbo_eels": turbo_eels_keys,
    "oscdft_et": oscdft_et_keys,
    "pprism": pprism_keys,
    "pwcond": pwcond_keys,
    "pw2bgw": pw2bgw_keys,
    "bgw2pw": bgw2pw_keys,
    "hp": hp_keys,
    "cp": cp_keys,
    "ld1": ld1_keys,
    "d3hess": d3hess_keys,
    "neb": neb_keys,
    "postahc": postahc_keys,
    "dvscf_q2r": dvscf_q2r,
    "epw": epw_keys
}
