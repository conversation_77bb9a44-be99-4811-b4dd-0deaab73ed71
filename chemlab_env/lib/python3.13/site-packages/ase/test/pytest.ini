[pytest]
markers =
    calculator: parametrizes calculator tests with calculator factories
    calculator_lite: for calculator tests; include in calculators-lite job
    optimize: tests of optimizers
    slow: test takes longer than a few seconds

# 1. The "can't resolve package" warning is for oldlibs.
# 2. The rm_rf warning is for issue #600.
# 3. There is a problem where filterwarnings=error removes all ResourceWarnings
# in stead of raising them as errors.
# See: https://github.com/pytest-dev/pytest/issues/5676
# In stead we do "once" for those warnings.
# Actually: We currently just ignore ResourceWarning because even with
# 'once', we will be spammed by 350+ such warnings.
# Someone should start fixing trajectory closing.
filterwarnings =
    error
    once::ResourceWarning
    ignore:can't resolve package
    once:.*size changed, may indicate binary incompatibility
    default:\(rm_rf\) error removing
    ignore::ase.config.ASEEnvDeprecationWarning
