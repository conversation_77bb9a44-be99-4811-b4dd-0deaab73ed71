from ase import Atoms
from gpaw.output import plot


def test_ascii_art():
    """Left-handed cell.

    https://listserv.fysik.dtu.dk/pipermail/gpaw-users/2021-January/006416.html
    """
    atoms = Atoms('OCOOLiLiOCOOLiLi',
                  cell=[[5.13498110527, 0.0, 0.0], [0.0, -3.45897297536, 0.0],
                        [0.0, 0.0, 9.2392788066]],
                  positions=[[1.953948519928, 0.0, 0.221181997402],
                             [2.601709243681, 0.0, 1.347130855356],
                             [3.910323049787, 0.0, 1.371393448854],
                             [1.969296828916, 0.0, 2.522605893448],
                             [0.028583840044, 0.0, 2.90092366278],
                             [2.56749055263, -1.72948648768, 4.398457405883],
                             [4.521439072558, -1.72948648768, 4.840821400721],
                             [0.03421869104, -1.72948648768, 5.966770258642],
                             [1.342832497147, -1.72948648768, 5.991032852173],
                             [4.536787381546, -1.72948648768, 7.142245296767],
                             [2.596074392674, -1.72948648768, 7.520563066099],
                             [-0.00000000001, 0.0, 9.018096809202]],
                  pbc=True)
    print(plot(atoms))
