import pytest
import numpy as np
from gpaw.response.g0w0 import G0W0
from ase.units import Hartree as Ha


@pytest.mark.response
def test_ff(in_tmp_dir, gpw_files, scalapack):
    ref_result = np.array([[[11.290542, 21.613643],
                            [5.356609, 16.065227],
                            [8.751158, 23.156368]]])

    sigma_ref = np.array([[[[
        [-54.28131992 - 1.49405384e+01j, -3.99297594 - 3.34138144e+00j],
        [77.53444946 - 5.26294522e+01j, 0.34158023 - 5.43942159e+00j],
        [21.00968396 - 5.90428409e+00j, 1.79222844 - 5.45107415e-02j],
        [12.52837421 - 1.09142412e-01j, -1.07445709 - 1.51195031e+00j],
        [8.32999717 + 1.76578923e-01j, -1.77083851 + 3.69132791e-01j],
        [7.21061712 - 7.17471489e-02j, -2.12158432 + 2.95912500e-01j],
        [5.25289773 + 2.43118365e-02j, -2.86384646 + 2.03613621e-01j],
        [4.20152422 - 1.23790221e-01j, -3.63132078 + 7.23669876e-02j],
        [3.39972222 - 2.88785888e-01j, -4.64876178 - 4.59216213e-02j],
        [2.70152323 - 4.24685992e-01j, -5.94354422 + 1.27748382e-01j]],
        [[-1.45243829 - 4.83823574e+01j, -4.47198519 - 2.35840870e-01j],
         [31.07239191 - 2.48791670e+01j, -0.53176325 - 9.50304973e-01j],
         [16.50656288 - 1.99587470e+00j, -0.32352803 + 4.94696448e-01j],
         [9.35581896 - 1.24616236e-01j, -1.92505551 - 2.60874367e-01j],
         [7.16105024 - 1.90973768e-01j, -1.99847525 + 3.03456423e-01j],
         [5.81918794 + 1.10630252e-02j, -2.59841355 + 2.12420877e-01j],
         [4.43679693 - 6.47631603e-02j, -3.38445908 + 1.19905505e-01j],
         [3.52070699 - 2.16975760e-01j, -4.26994642 - 9.98749695e-03j],
         [2.72268915 - 3.88700282e-01j, -5.62133592 - 1.07457127e-01j],
         [1.92516787 - 4.58759267e-01j, -7.23187625 + 3.60674245e-01j]],
        [[-10.11883043 - 2.47964162e+01j, 1.17607804 - 3.87391357e+00j],
         [31.36099154 - 9.41255797e+00j, -0.56828571 + 1.57837318e+00j],
         [15.5793284 - 3.67516481e+00j, -2.43767203 - 1.35435991e+00j],
         [12.26791922 - 4.41209726e-01j, -0.74831727 + 4.73790420e-01j],
         [8.23568868 + 1.00502499e-01j, -1.7037632 + 5.50436582e-01j],
         [6.27982436 + 1.18822002e-01j, -2.40467913 + 3.81712973e-01j],
         [4.8619356 - 1.16976699e-02j, -3.07885097 + 2.52112548e-01j],
         [3.87790898 - 1.62036394e-01j, -3.82165819 + 8.79142685e-02j],
         [3.06868045 - 3.10815249e-01j, -4.79705615 - 4.84884214e-02j],
         [2.31127915 - 5.10778583e-01j, -6.21351531 - 1.58747149e-01j]]]]])

    gw = G0W0(gpw_files['bn_pw'],
              bands=(3, 5),
              nbands=9,
              nblocks=1,
              evaluate_sigma=np.linspace(-1, 1, 10),
              ecut=40)

    results = gw.calculate()
    np.testing.assert_allclose(results['qp'], ref_result, rtol=1e-03)

    np.testing.assert_allclose(results['sigma_eskwn'], sigma_ref, rtol=1e-3)


@pytest.mark.response
@pytest.mark.parametrize("mpa", [True, False])
def test_mpa_and_ppa(in_tmp_dir, gpw_files, scalapack, mpa):
    ref_result = np.array([[[11.303942, 21.624428],
                            [5.346694, 16.06346],
                            [8.7589, 22.461506]]])
    mpa_dict = {'npoles': 1, 'wrange': [0, 0],
                'varpi': Ha, 'eta0': 1e-6, 'eta_rest': 10000 * Ha,
                'alpha': 1}

    gw = G0W0(gpw_files['bn_pw'],
              bands=(3, 5),
              nbands=9,
              nblocks=1,
              ecut=40,
              ppa=not mpa,
              mpa=mpa_dict if mpa else None)

    results = gw.calculate()
    np.testing.assert_allclose(results['qp'], ref_result, rtol=1e-03)
