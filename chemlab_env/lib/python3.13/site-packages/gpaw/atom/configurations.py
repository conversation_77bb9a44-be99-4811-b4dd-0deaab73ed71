# Copyright (C) 2003  CAMP
# Please see the accompanying LICENSE file for further information.

# flake8: noqa
# Computer generated code:
# format: (energy in Hartrees)
#    'element': (atomic number, [(n, l, occ, energy), ...])
from typing import Dict, Any
import copy

configurations = {
 'Ac': (89,
        [(1, 0, 2, -3443.1103670000002),
         (2, 0, 2, -592.62287800000001),
         (2, 1, 6, -572.7627),
         (3, 0, 2, -147.320716),
         (3, 1, 6, -137.654394),
         (3, 2, 10, -119.541743),
         (4, 0, 2, -36.158259999999999),
         (4, 1, 6, -31.761845999999998),
         (4, 2, 10, -23.570609999999999),
         (5, 0, 2, -7.7130780000000003),
         (5, 1, 6, -6.0651099999999998),
         (4, 3, 14, -12.278225000000001),
         (5, 2, 10, -3.2227519999999998),
         (6, 0, 2, -1.1969799999999999),
         (6, 1, 6, -0.74452399999999996),
         (6, 2, 1, -0.13778599999999999),
         (7, 0, 2, -0.126551)]),
 'Ag': (47,
        [(1, 0, 2, -900.32457799999997),
         (2, 0, 2, -129.85980699999999),
         (2, 1, 6, -120.91335100000001),
         (3, 0, 2, -23.678436999999999),
         (3, 1, 6, -20.067630000000001),
         (3, 2, 10, -13.367803),
         (4, 0, 2, -3.22309),
         (4, 1, 6, -2.0866020000000001),
         (4, 2, 10, -0.29870600000000003),
         (5, 0, 1, -0.15740699999999999)]),
 'Al': (13,
        [(1, 0, 2, -55.156044000000001),
         (2, 0, 2, -3.9348269999999999),
         (2, 1, 6, -2.5640179999999999),
         (3, 0, 2, -0.286883),
         (3, 1, 1, -0.102545)]),
 'Ar': (18,
        [(1, 0, 2, -113.800134),
         (2, 0, 2, -10.794172),
         (2, 1, 6, -8.4434389999999997),
         (3, 0, 2, -0.88338399999999995),
         (3, 1, 6, -0.38233)]),
 'As': (33,
        [(1, 0, 2, -423.336658),
         (2, 0, 2, -53.093086),
         (2, 1, 6, -47.527869000000003),
         (3, 0, 2, -6.7307550000000003),
         (3, 1, 6, -4.8517250000000001),
         (3, 2, 10, -1.542767),
         (4, 0, 2, -0.52366999999999997),
         (4, 1, 3, -0.19749700000000001)]),
 'At': (85,
        [(1, 0, 2, -3127.3902760000001),
         (2, 0, 2, -531.81835000000001),
         (2, 1, 6, -513.04424300000005),
         (3, 0, 2, -129.03554199999999),
         (3, 1, 6, -119.995013),
         (3, 2, 10, -103.06037499999999),
         (4, 0, 2, -29.809515000000001),
         (4, 1, 6, -25.778264),
         (4, 2, 10, -18.295162000000001),
         (5, 0, 2, -5.4533829999999996),
         (5, 1, 6, -4.0270609999999998),
         (4, 3, 14, -8.0634829999999997),
         (5, 2, 10, -1.6437580000000001),
         (6, 0, 2, -0.56018900000000005),
         (6, 1, 5, -0.25545299999999999)]),
 'Au': (79,
        [(1, 0, 2, -2683.508245),
         (2, 0, 2, -447.88897300000002),
         (2, 1, 6, -430.72570100000002),
         (3, 0, 2, -104.824516),
         (3, 1, 6, -96.706999999999994),
         (3, 2, 10, -81.511751000000004),
         (4, 0, 2, -22.078357),
         (4, 1, 6, -18.578652000000002),
         (4, 2, 10, -12.131815),
         (5, 0, 2, -3.1139359999999998),
         (5, 1, 6, -2.0024950000000001),
         (4, 3, 14, -3.4868239999999999),
         (5, 2, 10, -0.30473800000000001),
         (6, 0, 1, -0.16233400000000001)]),
 'B': (5,
       [(1, 0, 2, -6.5643469999999997),
        (2, 0, 2, -0.34470099999999998),
        (2, 1, 1, -0.136603)]),
 'Ba': (56,
        [(1, 0, 2, -1305.743258),
         (2, 0, 2, -200.84444400000001),
         (2, 1, 6, -189.59848299999999),
         (3, 0, 2, -42.359434),
         (3, 1, 6, -37.536931000000003),
         (3, 2, 10, -28.528932999999999),
         (4, 0, 2, -8.2570610000000002),
         (4, 1, 6, -6.4976219999999998),
         (4, 2, 10, -3.4324409999999999),
         (5, 0, 2, -1.157159),
         (5, 1, 6, -0.69860500000000003),
         (6, 0, 2, -0.118967)]),
 'Be': (4, [(1, 0, 2, -3.856411), (2, 0, 2, -0.20574400000000001)]),
 'Bi': (83,
        [(1, 0, 2, -2975.5509590000001),
         (2, 0, 2, -502.95075800000001),
         (2, 1, 6, -484.71635900000001),
         (3, 0, 2, -120.613998),
         (3, 1, 6, -111.883393),
         (3, 2, 10, -95.532476000000003),
         (4, 0, 2, -27.070340000000002),
         (4, 1, 6, -23.218641000000002),
         (4, 2, 10, -16.084817000000001),
         (5, 0, 2, -4.6119339999999998),
         (5, 1, 6, -3.2936369999999999),
         (4, 3, 14, -6.3827439999999998),
         (5, 2, 10, -1.139408),
         (6, 0, 2, -0.42612899999999998),
         (6, 1, 3, -0.180198)]),
 'Br': (35,
        [(1, 0, 2, -480.18264299999998),
         (2, 0, 2, -61.710022000000002),
         (2, 1, 6, -55.677959999999999),
         (3, 0, 2, -8.4090570000000007),
         (3, 1, 6, -6.2988049999999998),
         (3, 2, 10, -2.5221100000000001),
         (4, 0, 2, -0.72006599999999998),
         (4, 1, 5, -0.29533399999999999)]),
 'C': (6,
       [(1, 0, 2, -9.9477180000000001),
        (2, 0, 2, -0.50086600000000003),
        (2, 1, 2, -0.199186)]),
 'Ca': (20,
        [(1, 0, 2, -143.935181),
         (2, 0, 2, -15.046905000000001),
         (2, 1, 6, -12.285375999999999),
         (3, 0, 2, -1.706331),
         (3, 1, 6, -1.030572),
         (4, 0, 2, -0.14141100000000001)]),
 'Cd': (48,
        [(1, 0, 2, -941.47664599999996),
         (2, 0, 2, -136.83249000000001),
         (2, 1, 6, -127.63512),
         (3, 0, 2, -25.379908),
         (3, 1, 6, -21.637522000000001),
         (3, 2, 10, -14.685252),
         (4, 0, 2, -3.596069),
         (4, 1, 6, -2.3952599999999999),
         (4, 2, 10, -0.47053),
         (5, 0, 2, -0.20422799999999999)]),
 'Ce': (58,
        [(1, 0, 2, -1406.1482840000001),
         (2, 0, 2, -218.684842),
         (2, 1, 6, -206.92514800000001),
         (3, 0, 2, -47.035283),
         (3, 1, 6, -41.938282000000001),
         (3, 2, 10, -32.412568999999998),
         (4, 0, 2, -9.4327439999999996),
         (4, 1, 6, -7.5321059999999997),
         (4, 2, 10, -4.1925480000000004),
         (5, 0, 2, -1.3697280000000001),
         (5, 1, 6, -0.85011000000000003),
         (4, 3, 1, -0.33744200000000002),
         (5, 2, 1, -0.14055000000000001),
         (6, 0, 2, -0.13397400000000001)]),
 'Cl': (17,
        [(1, 0, 2, -100.369229),
         (2, 0, 2, -9.1879930000000005),
         (2, 1, 6, -7.0399820000000002),
         (3, 0, 2, -0.75445799999999996),
         (3, 1, 5, -0.32038)]),
 'Co': (27,
        [(1, 0, 2, -275.61663900000002),
         (2, 0, 2, -32.379758000000002),
         (2, 1, 6, -28.152094999999999),
         (3, 0, 2, -3.6518120000000001),
         (3, 1, 6, -2.3882850000000002),
         (3, 2, 7, -0.32236799999999999),
         (4, 0, 2, -0.20449700000000001)]),
 'Cr': (24,
        [(1, 0, 2, -213.881191),
         (2, 0, 2, -24.113457),
         (2, 1, 6, -20.526273),
         (3, 0, 2, -2.6490849999999999),
         (3, 1, 6, -1.6542300000000001),
         (3, 2, 5, -0.11812300000000001),
         (4, 0, 1, -0.150445)]),
 'Cs': (55,
        [(1, 0, 2, -1256.738791),
         (2, 0, 2, -191.98187300000001),
         (2, 1, 6, -180.99534399999999),
         (3, 0, 2, -39.851584000000003),
         (3, 1, 6, -35.166423000000002),
         (3, 2, 10, -26.418398),
         (4, 0, 2, -7.4559660000000001),
         (4, 1, 6, -5.7693260000000004),
         (4, 2, 10, -2.8483860000000001),
         (5, 0, 2, -0.91581900000000005),
         (5, 1, 6, -0.50490299999999999),
         (6, 0, 1, -0.078699000000000005)]),
 'Cu': (29,
        [(1, 0, 2, -320.78852000000001),
         (2, 0, 2, -38.141309999999997),
         (2, 1, 6, -33.481247000000003),
         (3, 0, 2, -4.0574529999999998),
         (3, 1, 6, -2.6092439999999999),
         (3, 2, 10, -0.20227200000000001),
         (4, 0, 1, -0.17205599999999999)]),
 'Dy': (66,
        [(1, 0, 2, -1843.229585),
         (2, 0, 2, -295.34285599999998),
         (2, 1, 6, -281.55853100000002),
         (3, 0, 2, -65.299441999999999),
         (3, 1, 6, -59.091931000000002),
         (3, 2, 10, -47.486699999999999),
         (4, 0, 2, -12.551251000000001),
         (4, 1, 6, -10.094091000000001),
         (4, 2, 10, -5.6863520000000003),
         (5, 0, 2, -1.5479769999999999),
         (5, 1, 6, -0.90349000000000002),
         (4, 3, 10, -0.26530199999999998),
         (6, 0, 2, -0.132769)]),
 'Er': (68,
        [(1, 0, 2, -1961.799176),
         (2, 0, 2, -316.310631),
         (2, 1, 6, -302.01826999999997),
         (3, 0, 2, -70.310141999999999),
         (3, 1, 6, -63.818655),
         (3, 2, 10, -51.682149000000003),
         (4, 0, 2, -13.423546999999999),
         (4, 1, 6, -10.819573999999999),
         (4, 2, 10, -6.1274430000000004),
         (5, 0, 2, -1.6160730000000001),
         (5, 1, 6, -0.93520199999999998),
         (4, 3, 12, -0.27857700000000002),
         (6, 0, 2, -0.134905)]),
 'Eu': (63,
        [(1, 0, 2, -1672.3093220000001),
         (2, 0, 2, -265.19953400000003),
         (2, 1, 6, -252.17669699999999),
         (3, 0, 2, -58.068128000000002),
         (3, 1, 6, -52.281987000000001),
         (3, 2, 10, -41.465518000000003),
         (4, 0, 2, -11.267747),
         (4, 1, 6, -9.0254549999999991),
         (4, 2, 10, -5.0324200000000001),
         (5, 0, 2, -1.4440869999999999),
         (5, 1, 6, -0.85357499999999997),
         (4, 3, 7, -0.23277300000000001),
         (6, 0, 2, -0.12942600000000001)]),
 'F': (9,
       [(1, 0, 2, -24.189391000000001),
        (2, 0, 2, -1.086859),
        (2, 1, 5, -0.41560599999999998)]),
 'Fe': (26,
        [(1, 0, 2, -254.225505),
         (2, 0, 2, -29.564859999999999),
         (2, 1, 6, -25.551766000000001),
         (3, 0, 2, -3.3606210000000001),
         (3, 1, 6, -2.1875230000000001),
         (3, 2, 6, -0.29504900000000001),
         (4, 0, 2, -0.19797799999999999)]),
 'Fr': (87,
        [(1, 0, 2, -3283.2633989999999),
         (2, 0, 2, -561.73045000000002),
         (2, 1, 6, -542.41423999999995),
         (3, 0, 2, -137.959632),
         (3, 1, 6, -128.607136),
         (3, 2, 10, -111.085223),
         (4, 0, 2, -32.861013),
         (4, 1, 6, -28.648130999999999),
         (4, 2, 10, -20.812462),
         (5, 0, 2, -6.5095159999999996),
         (5, 1, 6, -4.9732799999999999),
         (4, 3, 14, -10.050648000000001),
         (5, 2, 10, -2.3609909999999998),
         (6, 0, 2, -0.84184800000000004),
         (6, 1, 6, -0.46619699999999997),
         (7, 0, 1, -0.076175999999999994)]),
 'Ga': (31,
        [(1, 0, 2, -370.17063899999999),
         (2, 0, 2, -45.200868999999997),
         (2, 1, 6, -40.093339),
         (3, 0, 2, -5.2416450000000001),
         (3, 1, 6, -3.5846659999999999),
         (3, 2, 10, -0.73620399999999997),
         (4, 0, 2, -0.32801900000000001),
         (4, 1, 1, -0.101634)]),
 'Gd': (64,
        [(1, 0, 2, -1728.6251950000001),
         (2, 0, 2, -275.36313000000001),
         (2, 1, 6, -262.081616),
         (3, 0, 2, -60.764408000000003),
         (3, 1, 6, -54.836922000000001),
         (3, 2, 10, -43.754556000000001),
         (4, 0, 2, -11.986485999999999),
         (4, 1, 6, -9.6698660000000007),
         (4, 2, 10, -5.5318350000000001),
         (5, 0, 2, -1.6084769999999999),
         (5, 1, 6, -0.97874899999999998),
         (4, 3, 7, -0.489012),
         (5, 2, 1, -0.12722),
         (6, 0, 2, -0.143627)]),
 'Ge': (32,
        [(1, 0, 2, -396.29299099999997),
         (2, 0, 2, -49.055281999999998),
         (2, 1, 6, -43.720129),
         (3, 0, 2, -5.9614719999999997),
         (3, 1, 6, -4.1948220000000003),
         (3, 2, 10, -1.117316),
         (4, 0, 2, -0.42652299999999999),
         (4, 1, 2, -0.14988199999999999)]),
 'H': (1, [(1, 0, 1, -0.23347100000000001)]),
 'He': (2, [(1, 0, 2, -0.57042499999999996)]),
 'Hf': (72,
        [(1, 0, 2, -2210.6519899999998),
         (2, 0, 2, -361.00652700000001),
         (2, 1, 6, -345.68702300000001),
         (3, 0, 2, -81.522812000000002),
         (3, 1, 6, -74.452656000000005),
         (3, 2, 10, -61.231442999999999),
         (4, 0, 2, -15.883625),
         (4, 1, 6, -12.971211),
         (4, 2, 10, -7.6766379999999996),
         (4, 3, 14, -0.87157399999999996),
         (5, 0, 2, -2.0498280000000002),
         (5, 1, 6, -1.2464409999999999),
         (5, 2, 2, -0.14380499999999999),
         (6, 0, 2, -0.166465)]),
 'Hg': (80,
        [(1, 0, 2, -2755.022637),
         (2, 0, 2, -461.27864),
         (2, 1, 6, -443.84867600000001),
         (3, 0, 2, -108.597921),
         (3, 1, 6, -100.328031),
         (3, 2, 10, -84.845491999999993),
         (4, 0, 2, -23.222920999999999),
         (4, 1, 6, -19.636187),
         (4, 2, 10, -13.019221),
         (4, 3, 14, -4.1102910000000001),
         (5, 0, 2, -3.423486),
         (5, 1, 6, -2.2619750000000001),
         (5, 2, 10, -0.45255200000000001),
         (6, 0, 2, -0.20513700000000001)]),
 'Ho': (67,
        [(1, 0, 2, -1902.0519079999999),
         (2, 0, 2, -305.73929399999997),
         (2, 1, 6, -291.70099399999998),
         (3, 0, 2, -67.785492000000005),
         (3, 1, 6, -61.436304),
         (3, 2, 10, -49.565995999999998),
         (4, 0, 2, -12.985498),
         (4, 1, 6, -10.455303000000001),
         (4, 2, 10, -5.9061950000000003),
         (5, 0, 2, -1.5820879999999999),
         (5, 1, 6, -0.91946300000000003),
         (4, 3, 11, -0.272677),
         (6, 0, 2, -0.13384499999999999)]),
 'I': (53,
       [(1, 0, 2, -1161.787047),
        (2, 0, 2, -175.073804),
        (2, 1, 6, -164.60378800000001),
        (3, 0, 2, -35.243350999999997),
        (3, 1, 6, -30.831092000000002),
        (3, 2, 10, -22.600693),
        (4, 0, 2, -6.1158109999999999),
        (4, 1, 6, -4.5725220000000002),
        (4, 2, 10, -1.9381790000000001),
        (5, 0, 2, -0.59633899999999995),
        (5, 1, 5, -0.26790399999999998)]),
 'In': (49,
        [(1, 0, 2, -983.64744499999995),
         (2, 0, 2, -144.07835700000001),
         (2, 1, 6, -134.62884500000001),
         (3, 0, 2, -27.220600000000001),
         (3, 1, 6, -23.345777999999999),
         (3, 2, 10, -16.139823),
         (4, 0, 2, -4.0626389999999999),
         (4, 1, 6, -2.7958319999999999),
         (4, 2, 10, -0.73048100000000005),
         (5, 0, 2, -0.29049700000000001),
         (5, 1, 1, -0.101782)]),
 'Ir': (77,
        [(1, 0, 2, -2543.7613419999998),
         (2, 0, 2, -422.159424),
         (2, 1, 6, -405.52683400000001),
         (3, 0, 2, -97.923080999999996),
         (3, 1, 6, -90.108427000000006),
         (3, 2, 10, -75.485027000000002),
         (4, 0, 2, -20.29429),
         (4, 1, 6, -16.966577999999998),
         (4, 2, 10, -10.856593),
         (5, 0, 2, -2.9091740000000001),
         (4, 3, 14, -2.7383389999999999),
         (5, 1, 6, -1.8833489999999999),
         (5, 2, 7, -0.33518900000000001),
         (6, 0, 2, -0.19551099999999999)]),
 'K': (19,
       [(1, 0, 2, -128.41495699999999),
        (2, 0, 2, -12.839001),
        (2, 1, 6, -10.283851),
        (3, 0, 2, -1.2818970000000001),
        (3, 1, 6, -0.69377599999999995),
        (4, 0, 1, -0.088815000000000005)]),
 'Kr': (36,
        [(1, 0, 2, -509.98298899999998),
         (2, 0, 2, -66.285953000000006),
         (2, 1, 6, -60.017327999999999),
         (3, 0, 2, -9.3151919999999997),
         (3, 1, 6, -7.0866340000000001),
         (3, 2, 10, -3.074109),
         (4, 0, 2, -0.82057400000000003),
         (4, 1, 6, -0.34633999999999998)]),
 'La': (57,
        [(1, 0, 2, -1355.6224460000001),
         (2, 0, 2, -209.83115100000001),
         (2, 1, 6, -198.325243),
         (3, 0, 2, -44.856282999999998),
         (3, 1, 6, -39.895837999999998),
         (3, 2, 10, -30.626695999999999),
         (4, 0, 2, -9.0005430000000004),
         (4, 1, 6, -7.1677239999999998),
         (4, 2, 10, -3.9580099999999998),
         (5, 0, 2, -1.3249359999999999),
         (5, 1, 6, -0.82449799999999995),
         (5, 2, 1, -0.14108499999999999),
         (6, 0, 2, -0.13223299999999999)]),
 'Li': (3, [(1, 0, 2, -1.8785639999999999), (2, 0, 1, -0.10553999999999999)]),
 'Lu': (71,
        [(1, 0, 2, -2146.8853509999999),
         (2, 0, 2, -349.39049199999999),
         (2, 1, 6, -334.33090199999998),
         (3, 0, 2, -78.462397999999993),
         (3, 1, 6, -71.538779000000005),
         (3, 2, 10, -58.592981999999999),
         (4, 0, 2, -15.08337),
         (4, 1, 6, -12.250904),
         (4, 2, 10, -7.1133639999999998),
         (5, 0, 2, -1.8720859999999999),
         (5, 1, 6, -1.111991),
         (4, 3, 14, -0.56809600000000005),
         (5, 2, 1, -0.103686),
         (6, 0, 2, -0.155112)]),
 'Mg': (12,
        [(1, 0, 2, -45.973166999999997),
         (2, 0, 2, -2.9037459999999999),
         (2, 1, 6, -1.7189700000000001),
         (3, 0, 2, -0.175427)]),
 'Mn': (25,
        [(1, 0, 2, -233.696912),
         (2, 0, 2, -26.866645999999999),
         (2, 1, 6, -23.066296999999999),
         (3, 0, 2, -3.0766369999999998),
         (3, 1, 6, -1.9914499999999999),
         (3, 2, 5, -0.26654),
         (4, 0, 2, -0.191136)]),
 'Mo': (42,
        [(1, 0, 2, -709.23211900000001),
         (2, 0, 2, -98.503637999999995),
         (2, 1, 6, -90.791540999999995),
         (3, 0, 2, -16.681545),
         (3, 1, 6, -13.71481),
         (3, 2, 10, -8.2577210000000001),
         (4, 0, 2, -2.2348240000000001),
         (4, 1, 6, -1.39005),
         (4, 2, 5, -0.15334700000000001),
         (5, 0, 1, -0.14788000000000001)]),
 'N': (7,
       [(1, 0, 2, -14.011501000000001),
        (2, 0, 2, -0.67615099999999995),
        (2, 1, 3, -0.26629700000000001)]),
 'Na': (11,
        [(1, 0, 2, -37.719974999999998),
         (2, 0, 2, -2.0634009999999998),
         (2, 1, 6, -1.0606359999999999),
         (3, 0, 1, -0.10341500000000001)]),
 'Nb': (41,
        [(1, 0, 2, -673.76252999999997),
         (2, 0, 2, -92.740859999999998),
         (2, 1, 6, -85.272175000000004),
         (3, 0, 2, -15.393727),
         (3, 1, 6, -12.552854999999999),
         (3, 2, 10, -7.3398389999999996),
         (4, 0, 2, -2.0366930000000001),
         (4, 1, 6, -1.250049),
         (4, 2, 4, -0.125252),
         (5, 0, 1, -0.14427200000000001)]),
 'Nd': (60,
        [(1, 0, 2, -1509.6989550000001),
         (2, 0, 2, -236.613572),
         (2, 1, 6, -224.35181600000001),
         (3, 0, 2, -51.161262999999998),
         (3, 1, 6, -45.791218999999998),
         (3, 2, 10, -35.754514999999998),
         (4, 0, 2, -10.000890999999999),
         (4, 1, 6, -7.9678199999999997),
         (4, 2, 10, -4.377027),
         (5, 0, 2, -1.3349340000000001),
         (5, 1, 6, -0.79850299999999996),
         (4, 3, 4, -0.179508),
         (6, 0, 2, -0.12579599999999999)]),
 'Ne': (10,
        [(1, 0, 2, -30.305855000000001),
         (2, 0, 2, -1.3228089999999999),
         (2, 1, 6, -0.49803399999999998)]),
 'Ni': (28,
        [(1, 0, 2, -297.87082400000003),
         (2, 0, 2, -35.312111999999999),
         (2, 1, 6, -30.868027000000001),
         (3, 0, 2, -3.950717),
         (3, 1, 6, -2.5941580000000002),
         (3, 2, 8, -0.34869899999999998),
         (4, 0, 2, -0.21076400000000001)]),
 'O': (8,
       [(1, 0, 2, -18.758244999999999),
        (2, 0, 2, -0.87136199999999997),
        (2, 1, 4, -0.33838099999999999)]),
 'Os': (76,
        [(1, 0, 2, -2475.238617),
         (2, 0, 2, -409.52239600000001),
         (2, 1, 6, -393.15408000000002),
         (3, 0, 2, -94.501323999999997),
         (3, 1, 6, -86.837046999999998),
         (3, 2, 10, -72.497183000000007),
         (4, 0, 2, -19.362527),
         (4, 1, 6, -16.119671),
         (4, 2, 10, -10.176081999999999),
         (5, 0, 2, -2.7382930000000001),
         (4, 3, 14, -2.3211750000000002),
         (5, 1, 6, -1.757404),
         (5, 2, 6, -0.29679100000000003),
         (6, 0, 2, -0.19148899999999999)]),
 'P': (15,
       [(1, 0, 2, -76.061897000000002),
        (2, 0, 2, -6.3293460000000001),
        (2, 1, 6, -4.5766169999999997),
        (3, 0, 2, -0.51236400000000004),
        (3, 1, 3, -0.20608000000000001)]),
 'Pa': (91,
        [(1, 0, 2, -3606.3336290000002),
         (2, 0, 2, -623.87043100000005),
         (2, 1, 6, -603.47027800000001),
         (3, 0, 2, -156.46674200000001),
         (3, 1, 6, -146.48567800000001),
         (3, 2, 10, -127.78116799999999),
         (4, 0, 2, -39.064506999999999),
         (4, 1, 6, -34.482930000000003),
         (4, 2, 10, -25.933121),
         (5, 0, 2, -8.4634630000000008),
         (5, 1, 6, -6.7098209999999998),
         (4, 3, 14, -14.105746999999999),
         (5, 2, 10, -3.6599279999999998),
         (6, 0, 2, -1.2872319999999999),
         (6, 1, 6, -0.79975600000000002),
         (5, 3, 2, -0.31681300000000001),
         (6, 2, 1, -0.142481),
         (7, 0, 2, -0.12965299999999999)]),
 'Pb': (82,
        [(1, 0, 2, -2901.0780610000002),
         (2, 0, 2, -488.84333500000002),
         (2, 1, 6, -470.87778500000002),
         (3, 0, 2, -116.52685200000001),
         (3, 1, 6, -107.950391),
         (3, 2, 10, -91.889923999999993),
         (4, 0, 2, -25.753329999999998),
         (4, 1, 6, -21.990563999999999),
         (4, 2, 10, -15.030025999999999),
         (5, 0, 2, -4.2067969999999999),
         (5, 1, 6, -2.9416570000000002),
         (4, 3, 14, -5.5925320000000003),
         (5, 2, 10, -0.902393),
         (6, 0, 2, -0.35718699999999998),
         (6, 1, 2, -0.14183100000000001)]),
 'Pd': (46,
        [(1, 0, 2, -860.13490899999999),
         (2, 0, 2, -123.10507800000001),
         (2, 1, 6, -114.408286),
         (3, 0, 2, -22.060898000000002),
         (3, 1, 6, -18.580798000000001),
         (3, 2, 10, -12.132197),
         (4, 0, 2, -2.889173),
         (4, 1, 6, -1.815215),
         (4, 2, 10, -0.160771)]),
 'Pm': (61,
        [(1, 0, 2, -1562.980284),
         (2, 0, 2, -245.97054800000001),
         (2, 1, 6, -233.45511400000001),
         (3, 0, 2, -53.429310999999998),
         (3, 1, 6, -47.921132),
         (3, 2, 10, -37.625433000000001),
         (4, 0, 2, -10.422756),
         (4, 1, 6, -8.3204949999999993),
         (4, 2, 10, -4.5968220000000004),
         (5, 0, 2, -1.3722650000000001),
         (5, 1, 6, -0.81770200000000004),
         (4, 3, 5, -0.200159),
         (6, 0, 2, -0.127053)]),
 'Po': (84,
        [(1, 0, 2, -3050.988417),
         (2, 0, 2, -517.27584300000001),
         (2, 1, 6, -498.77192000000002),
         (3, 0, 2, -124.783683),
         (3, 1, 6, -115.89838399999999),
         (3, 2, 10, -99.256067999999999),
         (4, 0, 2, -28.422540000000001),
         (4, 1, 6, -24.481337),
         (4, 2, 10, -17.173307000000001),
         (5, 0, 2, -5.0274470000000004),
         (5, 1, 6, -3.6553819999999999),
         (4, 3, 14, -7.206499),
         (5, 2, 10, -1.386458),
         (6, 0, 2, -0.49352800000000002),
         (6, 1, 4, -0.217889)]),
 'Pr': (59,
        [(1, 0, 2, -1457.3380669999999),
         (2, 0, 2, -227.42636300000001),
         (2, 1, 6, -215.41831300000001),
         (3, 0, 2, -48.924993999999998),
         (3, 1, 6, -43.692548000000002),
         (3, 2, 10, -33.913995999999997),
         (4, 0, 2, -9.5774469999999994),
         (4, 1, 6, -7.6131080000000004),
         (4, 2, 10, -4.1542279999999998),
         (5, 0, 2, -1.296106),
         (5, 1, 6, -0.77804600000000002),
         (4, 3, 3, -0.155138),
         (6, 0, 2, -0.12446500000000001)]),
 'Pt': (78,
        [(1, 0, 2, -2613.096532),
         (2, 0, 2, -434.858003),
         (2, 1, 6, -417.96053000000001),
         (3, 0, 2, -101.274869),
         (3, 1, 6, -93.309107999999995),
         (3, 2, 10, -78.400271000000004),
         (4, 0, 2, -21.110651000000001),
         (4, 1, 6, -17.697296999999999),
         (4, 2, 10, -11.419476),
         (5, 0, 2, -2.950526),
         (4, 3, 14, -3.038049),
         (5, 1, 6, -1.8842559999999999),
         (5, 2, 9, -0.27363399999999999),
         (6, 0, 1, -0.16130800000000001)]),
 'Ra': (88,
        [(1, 0, 2, -3362.7365629999999),
         (2, 0, 2, -577.10120800000004),
         (2, 1, 6, -557.51321399999995),
         (3, 0, 2, -142.63242600000001),
         (3, 1, 6, -133.12325000000001),
         (3, 2, 10, -115.306476),
         (4, 0, 2, -34.525627999999998),
         (4, 1, 6, -30.221208000000001),
         (4, 2, 10, -22.208124999999999),
         (5, 0, 2, -7.1391369999999998),
         (5, 1, 6, -5.5472029999999997),
         (4, 3, 14, -11.181066),
         (5, 2, 10, -2.8198530000000002),
         (6, 0, 2, -1.05135),
         (6, 1, 6, -0.63467399999999996),
         (7, 0, 2, -0.113732)]),
 'Rb': (37,
        [(1, 0, 2, -540.95711500000004),
         (2, 0, 2, -71.291201999999998),
         (2, 1, 6, -64.784678),
         (3, 0, 2, -10.513861),
         (3, 1, 6, -8.1654160000000005),
         (3, 2, 10, -3.915508),
         (4, 0, 2, -1.135051),
         (4, 1, 6, -0.59216999999999997),
         (5, 0, 1, -0.085375000000000006)]),
 'Re': (75,
        [(1, 0, 2, -2407.6655719999999),
         (2, 0, 2, -397.08770700000002),
         (2, 1, 6, -380.98286899999999),
         (3, 0, 2, -91.149192999999997),
         (3, 1, 6, -83.634578000000005),
         (3, 2, 10, -69.576759999999993),
         (4, 0, 2, -18.454325000000001),
         (4, 1, 6, -15.295495000000001),
         (4, 2, 10, -9.5168160000000004),
         (5, 0, 2, -2.567348),
         (4, 3, 14, -1.9250799999999999),
         (5, 1, 6, -1.631227),
         (5, 2, 5, -0.25863900000000001),
         (6, 0, 2, -0.186859)]),
 'Rh': (45,
        [(1, 0, 2, -821.13677299999995),
         (2, 0, 2, -116.80695),
         (2, 1, 6, -108.357665),
         (3, 0, 2, -20.765602999999999),
         (3, 1, 6, -17.415299000000001),
         (3, 2, 10, -11.21725),
         (4, 0, 2, -2.8255050000000002),
         (4, 1, 6, -1.8064560000000001),
         (4, 2, 8, -0.239422),
         (5, 0, 1, -0.15462400000000001)]),
 'Rn': (86,
        [(1, 0, 2, -3204.756288),
         (2, 0, 2, -546.57795999999996),
         (2, 1, 6, -527.53302499999995),
         (3, 0, 2, -133.36914400000001),
         (3, 1, 6, -124.17286199999999),
         (3, 2, 10, -106.94500600000001),
         (4, 0, 2, -31.230803999999999),
         (4, 1, 6, -27.108985000000001),
         (4, 2, 10, -19.449994),
         (5, 0, 2, -5.8896829999999998),
         (5, 1, 6, -4.4087019999999999),
         (4, 3, 14, -8.9533179999999994),
         (5, 2, 10, -1.9113290000000001),
         (6, 0, 2, -0.62656999999999996),
         (6, 1, 6, -0.29318)]),
 'Ru': (44,
        [(1, 0, 2, -782.91862100000003),
         (2, 0, 2, -110.53605399999999),
         (2, 1, 6, -102.33364899999999),
         (3, 0, 2, -19.366692),
         (3, 1, 6, -16.145216999999999),
         (3, 2, 10, -10.195668),
         (4, 0, 2, -2.6283629999999998),
         (4, 1, 6, -1.6675489999999999),
         (4, 2, 7, -0.21037500000000001),
         (5, 0, 1, -0.152834)]),
 'S': (16,
       [(1, 0, 2, -87.789936999999995),
        (2, 0, 2, -7.6999399999999998),
        (2, 1, 6, -5.7512569999999998),
        (3, 0, 2, -0.63091200000000003),
        (3, 1, 4, -0.26167600000000002)]),
 'Sb': (51,
        [(1, 0, 2, -1070.8234950000001),
         (2, 0, 2, -159.17174499999999),
         (2, 1, 6, -149.214271),
         (3, 0, 2, -31.098241999999999),
         (3, 1, 6, -26.956184),
         (3, 2, 10, -19.239895000000001),
         (4, 0, 2, -5.0496400000000001),
         (4, 1, 6, -3.646579),
         (4, 2, 10, -1.2973380000000001),
         (5, 0, 2, -0.44560499999999997),
         (5, 1, 3, -0.18562300000000001)]),
 'Sc': (21,
        [(1, 0, 2, -160.18410900000001),
         (2, 0, 2, -17.206464),
         (2, 1, 6, -14.240005999999999),
         (3, 0, 2, -1.988378),
         (3, 1, 6, -1.2331650000000001),
         (3, 2, 1, -0.13108),
         (4, 0, 2, -0.15647800000000001)]),
 'Se': (34,
        [(1, 0, 2, -451.30025799999999),
         (2, 0, 2, -57.311948000000001),
         (2, 1, 6, -51.514387999999997),
         (3, 0, 2, -7.547186),
         (3, 1, 6, -5.5535170000000003),
         (3, 2, 10, -2.0113919999999998),
         (4, 0, 2, -0.62124800000000002),
         (4, 1, 4, -0.245806)]),
 'Si': (14,
        [(1, 0, 2, -65.184426000000002),
         (2, 0, 2, -5.075056),
         (2, 1, 6, -3.5149379999999999),
         (3, 0, 2, -0.39813900000000002),
         (3, 1, 2, -0.15329300000000001)]),
 'Sm': (62,
        [(1, 0, 2, -1617.1834260000001),
         (2, 0, 2, -255.49884599999999),
         (2, 1, 6, -242.729726),
         (3, 0, 2, -55.731133),
         (3, 1, 6, -50.08426),
         (3, 2, 10, -39.528655999999998),
         (4, 0, 2, -10.844666999999999),
         (4, 1, 6, -8.6726849999999995),
         (4, 2, 10, -4.814978),
         (5, 0, 2, -1.408552),
         (5, 1, 6, -0.83598700000000004),
         (4, 3, 6, -0.21776000000000001),
         (6, 0, 2, -0.12825900000000001)]),
 'Sn': (50,
        [(1, 0, 2, -1026.7621690000001),
         (2, 0, 2, -151.523991),
         (2, 1, 6, -141.82109299999999),
         (3, 0, 2, -29.125969000000001),
         (3, 1, 6, -25.117913000000001),
         (3, 2, 10, -17.657276),
         (4, 0, 2, -4.546335),
         (4, 1, 6, -3.2119979999999999),
         (4, 2, 10, -1.0049520000000001),
         (5, 0, 2, -0.36934899999999998),
         (5, 1, 2, -0.14445)]),
 'Sr': (38,
        [(1, 0, 2, -572.87016900000003),
         (2, 0, 2, -76.491822999999997),
         (2, 1, 6, -69.745941000000002),
         (3, 0, 2, -11.771585),
         (3, 1, 6, -9.3018630000000009),
         (3, 2, 10, -4.8134980000000001),
         (4, 0, 2, -1.455317),
         (4, 1, 6, -0.84448900000000005),
         (5, 0, 2, -0.13179299999999999)]),
 'Ta': (73,
        [(1, 0, 2, -2275.3713870000001),
         (2, 0, 2, -372.82872400000002),
         (2, 1, 6, -357.248334),
         (3, 0, 2, -84.658467000000002),
         (3, 1, 6, -77.440942000000007),
         (3, 2, 10, -63.942520999999999),
         (4, 0, 2, -16.713336999999999),
         (4, 1, 6, -13.719810000000001),
         (4, 2, 10, -8.2658480000000001),
         (4, 3, 14, -1.1993469999999999),
         (5, 0, 2, -2.2238069999999999),
         (5, 1, 6, -1.37653),
         (5, 2, 3, -0.18246399999999999),
         (6, 0, 2, -0.174814)]),
 'Tb': (65,
        [(1, 0, 2, -1785.331942),
         (2, 0, 2, -285.121013),
         (2, 1, 6, -271.59058499999998),
         (3, 0, 2, -62.851562999999999),
         (3, 1, 6, -56.785113000000003),
         (3, 2, 10, -45.443863),
         (4, 0, 2, -12.120486),
         (4, 1, 6, -9.7356370000000005),
         (4, 2, 10, -5.4676619999999998),
         (5, 0, 2, -1.5136689999999999),
         (5, 1, 6, -0.88722999999999996),
         (4, 3, 9, -0.25631100000000001),
         (6, 0, 2, -0.13167699999999999)]),
 'Tc': (43,
        [(1, 0, 2, -745.74202400000001),
         (2, 0, 2, -104.567508),
         (2, 1, 6, -96.610209999999995),
         (3, 0, 2, -18.135303),
         (3, 1, 6, -15.041738),
         (3, 2, 10, -9.3398599999999998),
         (4, 0, 2, -2.5507119999999999),
         (4, 1, 6, -1.64323),
         (4, 2, 5, -0.270262),
         (5, 0, 2, -0.18363599999999999)]),
 'Te': (52,
        [(1, 0, 2, -1115.831819),
         (2, 0, 2, -167.02177599999999),
         (2, 1, 6, -156.808583),
         (3, 0, 2, -33.137484999999998),
         (3, 1, 6, -28.860685),
         (3, 2, 10, -20.887801),
         (4, 0, 2, -5.5728460000000002),
         (4, 1, 6, -4.1000839999999998),
         (4, 2, 10, -1.6083810000000001),
         (5, 0, 2, -0.52099700000000004),
         (5, 1, 4, -0.22659399999999999)]),
 'Th': (90,
        [(1, 0, 2, -3524.4390520000002),
         (2, 0, 2, -608.35095799999999),
         (2, 1, 6, -588.21811200000002),
         (3, 0, 2, -152.07974100000001),
         (3, 1, 6, -142.25581),
         (3, 2, 10, -123.846396),
         (4, 0, 2, -37.814093999999997),
         (4, 1, 6, -33.325251999999999),
         (4, 2, 10, -24.955183999999999),
         (5, 0, 2, -8.2870570000000008),
         (5, 1, 6, -6.5828100000000003),
         (4, 3, 14, -13.397389),
         (5, 2, 10, -3.6257290000000002),
         (6, 0, 2, -1.333769),
         (6, 1, 6, -0.84692100000000003),
         (6, 2, 2, -0.17289599999999999),
         (7, 0, 2, -0.13587199999999999)]),
 'Ti': (22,
        [(1, 0, 2, -177.27664300000001),
         (2, 0, 2, -19.457901),
         (2, 1, 6, -16.285339),
         (3, 0, 2, -2.2580070000000001),
         (3, 1, 6, -1.422947),
         (3, 2, 2, -0.17000999999999999),
         (4, 0, 2, -0.167106)]),
 'Tl': (81,
        [(1, 0, 2, -2827.5694079999998),
         (2, 0, 2, -474.95336800000001),
         (2, 1, 6, -457.25597099999999),
         (3, 0, 2, -112.52218000000001),
         (3, 1, 6, -104.099296),
         (3, 2, 10, -88.328299000000001),
         (4, 0, 2, -24.471512000000001),
         (4, 1, 6, -20.797077999999999),
         (4, 2, 10, -14.008848),
         (5, 0, 2, -3.811512),
         (5, 1, 6, -2.5987300000000002),
         (4, 3, 14, -4.8357469999999996),
         (5, 2, 10, -0.67454400000000003),
         (6, 0, 2, -0.28502),
         (6, 1, 1, -0.101507)]),
 'Tm': (69,
        [(1, 0, 2, -2022.4716080000001),
         (2, 0, 2, -327.05712),
         (2, 1, 6, -312.51060799999999),
         (3, 0, 2, -72.873752999999994),
         (3, 1, 6, -66.239338000000004),
         (3, 2, 10, -53.835493999999997),
         (4, 0, 2, -13.865665),
         (4, 1, 6, -11.187151),
         (4, 2, 10, -6.3503069999999999),
         (5, 0, 2, -1.6499900000000001),
         (5, 1, 6, -0.95074800000000004),
         (4, 3, 13, -0.28311999999999998),
         (6, 0, 2, -0.13595299999999999)]),
 'U': (92,
       [(1, 0, 2, -3689.355141),
        (2, 0, 2, -639.778728),
        (2, 1, 6, -619.10855000000004),
        (3, 0, 2, -161.11807300000001),
        (3, 1, 6, -150.97898000000001),
        (3, 2, 10, -131.97735800000001),
        (4, 0, 2, -40.528084),
        (4, 1, 6, -35.853321000000001),
        (4, 2, 10, -27.123211999999999),
        (5, 0, 2, -8.8240890000000007),
        (5, 1, 6, -7.0180920000000002),
        (4, 3, 14, -15.02746),
        (5, 2, 10, -3.8661750000000001),
        (6, 0, 2, -1.325976),
        (6, 1, 6, -0.82253799999999999),
        (5, 3, 3, -0.36654300000000001),
        (6, 2, 1, -0.14319000000000001),
        (7, 0, 2, -0.13094800000000001)]),
 'V': (23,
       [(1, 0, 2, -195.22401400000001),
        (2, 0, 2, -21.815346000000002),
        (2, 1, 6, -18.435189000000001),
        (3, 0, 2, -2.526904),
        (3, 1, 6, -1.6105160000000001),
        (3, 2, 3, -0.20463400000000001),
        (4, 0, 2, -0.17596800000000001)]),
 'W': (74,
       [(1, 0, 2, -2341.0428870000001),
        (2, 0, 2, -384.856157),
        (2, 1, 6, -369.01397300000002),
        (3, 0, 2, -87.867791999999994),
        (3, 1, 6, -80.502101999999994),
        (3, 2, 10, -66.724787000000006),
        (4, 0, 2, -17.570796999999999),
        (4, 1, 6, -14.495101999999999),
        (4, 2, 10, -8.8796929999999996),
        (4, 3, 14, -1.550835),
        (5, 0, 2, -2.3960180000000002),
        (5, 1, 6, -1.5044569999999999),
        (5, 2, 4, -0.22060299999999999),
        (6, 0, 2, -0.18141299999999999)]),
 'Xe': (54,
        [(1, 0, 2, -1208.688993),
         (2, 0, 2, -183.327495),
         (2, 1, 6, -172.599583),
         (3, 0, 2, -37.415453999999997),
         (3, 1, 6, -32.867041999999998),
         (3, 2, 10, -24.378229999999999),
         (4, 0, 2, -6.6783400000000004),
         (4, 1, 6, -5.0638019999999999),
         (4, 2, 10, -2.2866659999999999),
         (5, 0, 2, -0.67208599999999996),
         (5, 1, 6, -0.30983500000000003)]),
 'Y': (39,
       [(1, 0, 2, -605.631981),
        (2, 0, 2, -81.789102),
        (2, 1, 6, -74.803201000000001),
        (3, 0, 2, -12.992217),
        (3, 1, 6, -10.399926000000001),
        (3, 2, 10, -5.6714989999999998),
        (4, 0, 2, -1.6971240000000001),
        (4, 1, 6, -1.0244899999999999),
        (4, 2, 1, -0.108691),
        (5, 0, 2, -0.150727)]),
 'Yb': (70,
        [(1, 0, 2, -2084.0693889999998),
         (2, 0, 2, -337.97897599999999),
         (2, 1, 6, -323.17821900000001),
         (3, 0, 2, -75.47663),
         (3, 1, 6, -68.698655000000002),
         (3, 2, 10, -56.026314999999997),
         (4, 0, 2, -14.312075999999999),
         (4, 1, 6, -11.558246),
         (4, 2, 10, -6.5749630000000003),
         (5, 0, 2, -1.683886),
         (5, 1, 6, -0.96613700000000002),
         (4, 3, 14, -0.286408),
         (6, 0, 2, -0.136989)]),
 'Zn': (30,
        [(1, 0, 2, -344.96975600000002),
         (2, 0, 2, -41.531323),
         (2, 1, 6, -36.648764999999997),
         (3, 0, 2, -4.5730409999999999),
         (3, 1, 6, -3.0223629999999999),
         (3, 2, 10, -0.39894400000000002),
         (4, 0, 2, -0.22272500000000001)]),
 'Zr': (40,
        [(1, 0, 2, -639.292236),
         (2, 0, 2, -87.237061999999995),
         (2, 1, 6, -80.010042999999996),
         (3, 0, 2, -14.230432),
         (3, 1, 6, -11.514415),
         (3, 2, 10, -6.5446429999999998),
         (4, 0, 2, -1.918971),
         (4, 1, 6, -1.1865969999999999),
         (4, 2, 2, -0.150673),
         (5, 0, 2, -0.16239100000000001)])}

# End of computer generated code:

# add missing elements 93-102, based on U (92)
configurations.update({'Np': (93, copy.deepcopy(configurations['U'])[1])})
configurations['Np'][1][15] = (5, 3, 4, -0.36654300000000001)
configurations.update({'Pu': (94, copy.deepcopy(configurations['U'])[1])})
configurations['Pu'][1][15] = (5, 3, 6, -0.36654300000000001)
configurations['Pu'][1][16] = (6, 2, 0, -0.14319000000000001)
configurations.update({'Am': (95, copy.deepcopy(configurations['U'])[1])})
configurations['Am'][1][15] = (5, 3, 7, -0.36654300000000001)
configurations['Am'][1][16] = (6, 2, 0, -0.14319000000000001)
configurations.update({'Cm': (96, copy.deepcopy(configurations['U'])[1])})
configurations['Cm'][1][15] = (5, 3, 7, -0.36654300000000001)
configurations.update({'Bk': (97, copy.deepcopy(configurations['U'])[1])})
configurations['Bk'][1][15] = (5, 3, 9, -0.36654300000000001)
configurations['Bk'][1][16] = (6, 2, 0, -0.14319000000000001)
configurations.update({'Cf': (98, copy.deepcopy(configurations['U'])[1])})
configurations['Cf'][1][15] = (5, 3, 10, -0.36654300000000001)
configurations['Cf'][1][16] = (6, 2, 0, -0.14319000000000001)
configurations.update({'Es': (99, copy.deepcopy(configurations['U'])[1])})
configurations['Es'][1][15] = (5, 3, 11, -0.36654300000000001)
configurations['Es'][1][16] = (6, 2, 0, -0.14319000000000001)
configurations.update({'Fm': (100, copy.deepcopy(configurations['U'])[1])})
configurations['Fm'][1][15] = (5, 3, 12, -0.36654300000000001)
configurations['Fm'][1][16] = (6, 2, 0, -0.14319000000000001)
configurations.update({'Md': (101, copy.deepcopy(configurations['U'])[1])})
configurations['Md'][1][15] = (5, 3, 13, -0.36654300000000001)
configurations['Md'][1][16] = (6, 2, 0, -0.14319000000000001)
configurations.update({'No': (102, copy.deepcopy(configurations['U'])[1])})
configurations['No'][1][15] = (5, 3, 14, -0.36654300000000001)
configurations['No'][1][16] = (6, 2, 0, -0.14319000000000001)


parameters: Dict[str, Dict[str, Any]] = {
 'H' : {'rcut': 0.9},
 'He': {'rcut': 1.5},
 'Li': {'core': '[He]',       'rcut': 2.0},
 'Be': {'core': '[He]',       'rcut': 1.5},
 'B' : {'core': '[He]',       'rcut': 1.2},
 'C' : {'core': '[He]',       'rcut': 1.2},
 'N' : {'core': '[He]',       'rcut': [1.14, 1.00, 1.09],
        'vbar': ('f', 1.12), 'rcutcomp': 1.09, 'filter': (0.37, 1.80)},
 'O' : {'core': '[He]',       'rcut': [1.30, 1.13, 1.17],
        'vbar': ('f', 1.30), 'rcutcomp': 1.26, 'filter': (0.43, 1.66)},
 'F' : {'core': '[He]',       'rcut': 1.2},
 'Ne': {'core': '[He]',       'rcut': 1.8},
 'Na': {'core': '[He]2s',     'rcut': [2.27, 2.30, 2.34],
        'vbar': ('f', 2.33), 'rcutcomp': 2.23,
        'filter': (0.56, 1.72),
        'empty_states': '3p'},
 'Mg': {'core': '[He]',       'rcut': [2.06, 2.05, 1.96],
        'vbar': ('f', 2.00), 'rcutcomp': 1.95,
        'filter': (0.58, 1.68),
        'empty_states': '3p'},
 'Al': {'core': '[Ne]',     'rcut': 2.05},
 'Si': {'core': '[Ne]',       'rcut': 2.0},
 'P' : {'core': '[Ne]',       'rcut': 1.8},
 'S' : {'core': '[Ne]',       'rcut': [1.84, 1.85, 1.70],
        'vbar': ('f', 1.72), 'rcutcomp': 1.43,
        'filter': (0.50, 1.80)},
 'Cl': {'core': '[Ne]',       'rcut': 1.5},
 'Ar': {'core': '[Ne]',       'rcut': 1.6},
 'K' : {'core': '[Ne]',       'rcut': [2.12, 2.09, 1.77],
        'vbar': ('f', 2.07), 'rcutcomp': 2.09,
        'filter': (0.60, 1.68)},
 'Ca': {'core': '[Ne]',       'rcut': [2.22, 2.18, 1.77],
        'vbar': ('f', 2.16), 'rcutcomp': 2.20,
        'filter': (0.57, 1.77)},
 'Sc': {'core': '[Ne]',       'rcut': [2.32, 2.42, 2.26],
        'vbar': ('f', 2.26), 'rcutcomp': 2.32,
        'filter': (0.62, 1.66),
        'empty_states': '4p'},
 'Ti': {'core': '[Ne]',       'rcut': [2.4, 2.0, 2.0],
        'vbar': ('f', 1.8), 'rcutcomp': 2.3, 'empty_states': '4p'},
 'V' : {'core': '[Ne]',       'rcut': [2.37, 2.42, 2.21],
        'vbar': ('f', 2.31), 'rcutcomp': 2.24,
        'filter': (0.62, 1.66),
        'empty_states': '4p'},
 'Cr': {'core': '[Ar]',       'rcut': [2.2, 2.3, 2.1]},
 'Mn': {'core': '[Ne]',       'rcut': [2.41, 2.42, 2.15],
        'vbar': ('f', 2.35), 'rcutcomp': 2.29,
        'filter': (0.64, 1.66),
        'empty_states': '4p'},
 'Fe': {'core': '[Ar]',       'rcut': [2.2, 2.0, 2.0]},
 'Co': {'core': '[Ar]',       'rcut': [1.9, 2.0, 1.9]},
 'Ni': {'core': '[Ne]3s',     'rcut': [2.20, 2.28, 2.15],
        'vbar': ('f', 2.22), 'rcutcomp': 2.18,
        'filter': (0.62, 1.76),
        'empty_states': '4p'},
 'Cu': {'core': '[Ar]',       'rcut': [2.2, 2.2, 2.0]},
 'Zn': {'core': '[Ar]',       'rcut': [2.43, 2.40, 2.23],
        'vbar': ('f', 2.26), 'rcutcomp': 2.20,
        'filter': (0.64, 1.65)},
 'Ga': {'core': '[Ar]3d',     'rcut': 2.2},
 'Ge': {'core': '[Ar]3d',     'rcut': 1.9},
 'As': {'core': '[Ar]3d',     'rcut': 2.0},
 'Se': {'core': '[Ar]3d',     'rcut': [1.6, 1.9]},
 'Br': {'core': '[Ar]3d',     'rcut': 2.1},
 'Kr': {'core': '[Ar]3d',     'rcut': 2.2},
 'Rb': {'core': '[Ar]3d',     'rcut': [2.6, 2.4, 2.3]},
 'Sr': {'core': '[Ar]3d',     'rcut': [2.44, 2.44, 2.37],
        'vbar': ('f', 2.40), 'rcutcomp': 2.37,
        'filter': (0.62, 1.83)},
 'Y':  {'core': '[Ar]3d',     'rcut': [2.51, 2.49, 2.43],
        'vbar': ('f', 2.45), 'rcutcomp': 2.41,
        'filter': (0.63, 1.78),
        'empty_states': '5p'},
 'Zr': {'core': '[Ar]3d',     'rcut': [2.50, 2.52, 2.35],
        'vbar': ('f', 2.40), 'rcutcomp': 2.37,
        'filter': (0.55, 1.78),
        'empty_states': '5p'},
 'Nb': {'core': '[Ar]3d',     'rcut': [2.50, 2.50, 2.35],
        'vbar': ('f', 2.41), 'rcutcomp': 2.40,
        'filter': (0.64, 1.76),
        'empty_states': '5p'},
 'Mo': {'core': '[Ar]3d',     'rcut': [2.34, 2.45, 2.45],
        'vbar': ('f', 2.34), 'rcutcomp': 2.27,
        'filter': (0.64, 1.65),
        'empty_states': '5p'},
#'Tc': Radioactive
 'Ru': {'core': '[Ar]3d',     'rcut': [2.42, 2.43, 2.37],
        'vbar': ('f', 2.42), 'rcutcomp': 2.33,
        'filter': (0.56, 1.66),
        'empty_states': '5p'},
 'Rh': {'core': '[Ar]3d4s',   'rcut': [2.40, 2.61, 2.35],
        'vbar': ('f', 2.39), 'rcutcomp': 2.29,
        'filter': (0.60, 1.78),
        'empty_states': '5p'},
 'Pd': {'core': '[Ar]3d4s',   'rcut': [2.32, 2.57, 2.32],
        'vbar': ('f', 2.30), 'rcutcomp': 2.25,
        'filter': (0.63, 1.73),
        'empty_states': '5p'},
 'Ag': {'core': '[Ar]3d4s',   'rcut': [2.43, 2.51, 2.23],
        'vbar': ('f', 2.46), 'rcutcomp': 2.37,
        'filter': (0.61, 1.75),
        'empty_states': '5p'},
 'Cd': {'core': '[Kr]',       'rcut': [2.25, 2.32, 2.20],
        'vbar': ('f', 2.29), 'rcutcomp': 2.26,
        'filter': (0.62, 1.71)},
 'In': {'core': '[Kr]',       'rcut': [2.24, 2.35, 2.17],
        'vbar': ('f', 2.23), 'rcutcomp': 2.15,
        'filter': (0.62, 1.67)},
 'Sn': {'core': '[Kr]',       'rcut': [2.27, 2.40, 2.24],
        'vbar': ('f', 2.25), 'rcutcomp': 2.26,
        'filter': (0.61, 1.74)},
 'Sb': {'core': '[Kr]',       'rcut': [2.18, 2.33, 2.26],
        'vbar': ('f', 2.21), 'rcutcomp': 2.18,
        'filter': (0.61, 1.80)},
 'Te': {'core': '[Kr]4d',     'rcut': [2.23, 2.26, 2.34],
        'vbar': ('f', 2.16), 'rcutcomp': 2.16,
        'filter': (0.64, 1.68)},
 'I' : {'core': '[Kr]4d',     'rcut': 2.2},
 'Xe': {'core': '[Kr]4d',     'rcut': [2.27, 2.40, 2.24],
        'vbar': ('f', 1.96), 'rcutcomp': 2.01,
        'filter': (0.63, 1.66)},
 'Cs': {'core': '[Kr]4d',     'rcut': [2.2, 2.0]},
 'Ba': {'core': '[Kr]4d',     'rcut': 2.2, 'extra': {1: [0.0], 2: [0.0, 1.0]}},
 'La': {'core': '[Kr]4d',     'rcut': [2.3, 2.0, 1.9]},
#'La': {'core': '[Kr]4d5s',   'rcut': [2.3, 2.0, 1.9]},
#'Lu': Missing
 'Hf': {'core': '[Kr]4d4f',   'rcut': [2.64, 2.47, 2.52],
        'vbar': ('f', 2.47), 'rcutcomp': 2.45,
        'filter': (0.63, 1.68),
        'empty_states': '6p'},
 'Ta': {'core': '[Kr]4d4f',   'rcut': [2.58, 2.55, 2.47],
        'vbar': ('f', 2.50), 'rcutcomp': 2.49,
        'filter': (0.62, 1.72),
        'empty_states': '6p'},
 'W' : {'core': '[Kr]4d4f',   'rcut': [2.54, 2.56, 2.32],
        'vbar': ('f', 2.46), 'rcutcomp': 2.41,
        'filter': (0.63, 1.73),
        'empty_states': '6p'},
 'Re': {'core': '[Kr]4d5s4f', 'rcut': [2.60, 2.60, 2.47],
        'vbar': ('f', 2.50), 'rcutcomp': 2.50,
        'filter': (0.55, 1.73),
        'empty_states': '6p'},
 'Os': {'core': '[Kr]4d5s4f', 'rcut': [2.55, 2.64, 2.35],
        'vbar': ('f', 2.49), 'rcutcomp': 2.36,
        'filter': (0.53, 1.68),
        'empty_states': '6p'},
 'Ir': {'core': '[Kr]4d5s4f', 'rcut': [2.45, 2.51, 2.43],
        'vbar': ('f', 2.45), 'rcutcomp': 2.39,
        'filter': (0.54, 1.79),
        'empty_states': '6p'},
 'Au': {'core': '[Xe]4f',     'rcut': 2.5},
 'Pt': {'core': '[Kr]4d5s4f', 'rcut': [2.47, 2.59, 2.47],
        'vbar': ('f', 2.53), 'rcutcomp': 2.44,
        'filter': (0.61, 1.65),
        'empty_states': '6p'},
 'Hg': {'core': '[Kr]4d4f5s', 'rcut': [2.47, 2.44, 2.46],
        'vbar': ('f', 2.36), 'rcutcomp': 2.43,
        'filter': (0.60, 1.65),
        'empty_states': '6p'},
 'Tl': {'core': '[Xe]4f',     'rcut': [2.29, 2.40, 2.25],
        'vbar': ('f', 2.30), 'rcutcomp': 2.25,
        'filter': (0.63, 1.66)},
 'Pb': {'core': '[Xe]4f',     'rcut': [2.38, 2.41, 2.30],
        'vbar': ('f', 2.26), 'rcutcomp': 2.29,
        'filter': (0.64, 1.83)},
 'Bi': {'core': '[Xe]4f',     'rcut': [2.43, 2.41, 2.55],
        'vbar': ('f', 2.35), 'rcutcomp': 2.29,
        'filter': (0.62, 1.65)},
#'Po': Missing
#'At': Missing
 'Rn': {'core': '[Xe]4f5d',   'rcut': [2.30, 2.29, 2.31],
        'vbar': ('f', 1.96), 'rcutcomp': 2.02,
        'filter': (0.62, 1.69)},
 }

# Extra setups
parameters_extra = {
 'H' : {'name': 'single', 'rcut': 0.9, 'extra': {}},# No extra projectors
 'Li': {'name': 'hard', 'rcut': 1.5, 'extra': {1: [-0.0413]}}, # No core
 'Be': {'name': 'soft', 'core': '[He]', 'rcut': 1.9},
 'N' : {'name': 'v08',  'core': '[He]', 'rcut': 1.1},  # ver. 0.8
# 'O' : {'name': 'v08',  'core': '[He]', 'rcut': 1.4,
#        'filter': (0.5, 1.75)},   # ver. 0.8
 'O' : {'name': 'hard', 'core': '[He]', 'rcut': 1.2},
 'Na': {'name': '1',    'core': '[Ne]', 'rcut': 2.55},  # ver. 0.8
 'Mg': {'name': '2',    'core': '[Ne]', 'rcut': [1.9, 2.0]},  # ver. 0.8
 'Si': {'name': 'hard', 'core': '[Ne]', 'rcut': 1.85},
 'S' : {'name': 'v08',  'core': '[Ne]', 'rcut': 1.6},  # ver. 0.8
 'K' : {'name': 'old',  'core': '[Ne]', 'rcut': [2.5, 2.1, 2.1]},  # ver. 0.8
 'Ca': {'name': 'v08',  'core': '[Ne]', 'rcut': [2.0, 1.7]},  # ver. 0.8
 'Sc': {'name': 'v08',  'core': '[Ne]', 'rcut': [1.9, 1.8]},  # ver. 0.8
 'V' : {'name': '5',    'core': '[Ar]', 'rcut': [2.5, 2.4, 2.0],
        'vbar': ('poly', 2.3), 'rcutcomp': 2.5},  # ver. 0.8
 'Mn': {'name': '7',    'core': '[Ar]', 'rcut': [2.2, 2.1, 2.1]},  # ver. 0.8
 'Ni': {'name': '10',   'core': '[Ar]', 'rcut': [1.8, 1.9, 1.8]},  # ver. 0.8
 'Zn': {'name': 'v08',  'core': '[Ar]', 'rcut': [2.0, 1.9, 1.9]},  # ver. 0.8
 'Br': {'name': 'old',  'core': '[Ar]3d', 'rcut': 2.2},
 'Sr': {'name': 'v08',  'core': '[Ar]3d', 'rcut': [2.4, 2.4, 2.3],
        'extra':{1: [0.0], 2: [0.0]}},  # ver. 0.8
 'Zr': {'name': 'v08',  'core': '[Ar]3d', 'rcut': 2.0},  # ver. 0.8
 'Nb': {'name': '5',    'core': '[Kr]', 'rcut': [2.9, 2.9, 2.6]},  # ver. 0.8
 'Mo': {'name': '6',    'core': '[Kr]', 'rcut': [2.8, 2.8, 2.5]},  # ver. 0.8
 'Ru': {'name': '8',    'core': '[Kr]', 'rcut': 2.6},  # ver. 0.8
 'Rh': {'name': '9',    'core': '[Kr]', 'rcut': 2.5},  # ver. 0.8
 'Pd': {'name': '10',   'core': '[Kr]', 'rcut': [2.3, 2.5, 2.2]},  # ver. 0.8
 'Ag': {'name': '11',   'core': '[Kr]', 'rcut': 2.45},  # ver. 0.8
 'Cd': {'name': 'v08',  'core': '[Kr]', 'rcut': [2.1, 2.5, 2.0]},  # ver. 0.8
 'In': {'name': 'v08',  'core': '[Kr]', 'rcut': [2.1, 2.5, 2.0]},  # ver. 0.8
 'Sn': {'name': 'v08',  'core': '[Kr]', 'rcut': 2.2},  # ver. 0.8
 'Te': {'name': '16',   'core': '[Kr]', 'rcut': 2.2},  # ver. 0.8
 'Ta': {'name': '5',    'core': '[Kr]4d4f5s5p', 'rcut': 2.8},  # ver. 0.8
 'W' : {'name': '6',    'core': '[Kr]4d4f5s5p', 'rcut': 2.8},  # ver. 0.8
 'Os': {'name': '8',    'core': '[Kr]4d5s4f5p', 'rcut': [2.5, 2.7, 2.5]},  # ver. 0.8
 'Ir': {'name': '9',    'core': '[Kr]4d5s4f5p', 'rcut': [2.3, 2.6, 2.0],
        'vbar': ('poly', 2.1), 'rcutcomp': 2.3},  # ver. 0.8
 'Pt': {'name': '10',   'core': '[Kr]4d5s4f5p', 'rcut': [2.5, 2.7, 2.3]},  # ver. 0.8
# 'Pt': {'name': 'soft', 'core': '[Xe]4f', 'rcut': [2.5, 2.7, 2.3],
#        'rcutcomp': 2.5},  # ver. 0.8
 'Pb': {'name': 'v08',  'core': '[Xe]4f', 'rcut': [2.4, 2.6, 2.4]},  # ver. 0.8
 'Bi': {'name': 'v08',  'core': '[Xe]4f', 'rcut': [2.2, 2.4, 2.2]},  # ver. 0.8
 }

tf_parameters = {
    'B': {'rcut': 1.2},
    'Be': {'rcut': 1.2},
    'C': {'rcut': 1.2},
    'F': {'rcut': 1.2},
    'He': {'rcut': 1.2},
    'Li': {'rcut': 1.2},
    'N': {'rcut': 1.2},
    'Ne': {'rcut': 1.2},
    'O': {'rcut': 1.2}}


class AtomicData:
    def __init__(self, name, Z, mass, radius, configuration):
        self.name = name
        self.Z = Z
        self.mass = mass
        self.radius = radius
        self.configuration = configuration


def core_states(symbol):
    """Method returning the number of core states for given element."""
    core = parameters[symbol].get('core', '')

    # Parse core string:
    j = 0
    if core.startswith('['):
        a, core = core.split(']')
        core_symbol = a[1:]
        j = len(configurations[core_symbol][1])

    Njcore = j + len(core) // 2
    return Njcore


if __name__ == '__main__':
    import pprint
    # https://www.physics.nist.gov/PhysRefData/DFTdata/
    path = '/scratch/jensj/dftdata/'
    Ztable = {}
    confs = [['X', '']]
    Z = 1
    with open(path + 'configurations') as fd:
        for line in fd:
            if len(line) > 1 and line[1].isdigit():
                words = line[:44].split()
                symbol = words[1]
                Ztable[symbol] = Z
                confs.append(words[2:])
                Z += 1

    def get_occupations(symbol):
        Z = Ztable[symbol]
        configuration = confs[Z]
        if configuration[0][0] == '[':
            occupations = get_occupations(configuration[0][1:-1])
            configuration = configuration[1:]
        else:
            occupations = []
        for s in configuration:
            occupations.append((s[:2], int(s[3:])))
        return occupations

    dftdata = {}
    spdf = {'s': 0, 'p': 1, 'd': 2, 'f': 3}
    for symbol, Z in Ztable.items():
        occupations = get_occupations(symbol)
        with open(path + 'LDA/neutrals/%02d%s' % (Z, symbol)) as fd:
            for n in range(5):
                fd.readline()
            epsilons = {}
            for line in fd:
                state, eps = line.split()
                epsilons[state] = float(eps)
        nloe = []
        for state, occ in occupations:
            n = int(state[0])
            l = spdf[state[1]]
            e = epsilons[state]
            nloe.append((n, l, occ, e))
        dftdata[symbol] = (Z, nloe)
    print('# Computer generated code:')
    print()
    print('configurations = ', end=' ')
    pprint.pprint(dftdata)
    print()
