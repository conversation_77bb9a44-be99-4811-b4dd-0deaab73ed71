# 🧪 ChemLab: Advanced Chemical Reaction Simulator

A comprehensive Python toolkit for simulating chemical reactions using quantum chemistry methods, predicting reaction pathways, and analyzing reaction networks.

## 🔬 Features

- **Quantum Chemical Simulations**: DFT calculations using ASE and PySCF
- **Reaction Pathway Analysis**: NEB (Nudged Elastic Band) and transition state search
- **Multi-format Input**: Support for SMILES, XYZ, and other molecular formats
- **Thermodynamic & Kinetic Analysis**: Gibbs free energy, rate constants, Arrhenius plots
- **Reaction Network Modeling**: Build and analyze complex reaction networks
- **Interactive Visualization**: 3D molecular structures and potential energy surfaces
- **Customizable Conditions**: Temperature, pressure, solvent, catalyst effects
- **CLI Interface**: Easy-to-use command-line interface

## 🚀 Quick Start

### Automated Setup

The easiest way to get started is using our setup script:

```bash
# Clone this repository
git clone <repository-url>
cd chem_lab

# Run automated setup
python setup.py
```

This will:
- Check Python version compatibility
- Install all required dependencies
- Set up optional dependencies
- Run tests to verify installation
- Create example configuration files

### Manual Installation

If you prefer manual setup:

1. **Create a virtual environment:**
```bash
python -m venv chemlab_env
source chemlab_env/bin/activate  # On Windows: chemlab_env\Scripts\activate
```

2. **Install core dependencies:**
```bash
pip install -r requirements.txt
```

3. **Install optional dependencies for enhanced functionality:**
```bash
# For SMILES parsing (highly recommended)
conda install -c conda-forge rdkit

# For high-accuracy DFT calculations
pip install pyscf

# For advanced DFT with plane waves
pip install gpaw

# For 3D molecular visualization
pip install py3Dmol
```

4. **Verify installation:**
```bash
python test_chemlab.py
```

### Basic Usage

```bash
# Analyze a simple reaction
python main.py --reactants H2 O2 --products H2O --temperature 298.15

# Find reaction pathway with transition states
python main.py --pathway --reactants CH4 Cl2 --products CH3Cl HCl

# Build and analyze reaction network
python main.py --network --reactions "H2,O2->H2O" "CH4,O2->CO2,H2O"

# Optimize molecular geometry
python main.py --optimize --molecule H2O --method pyscf

# Run example reactions
python examples/sample_reactions.py
```

## 📁 Project Structure

```
chem_lab/
├── main.py                    # CLI entry point
├── input_handler.py          # Molecular input parsing
├── molecule_optimizer.py     # DFT geometry optimization
├── reaction_feasibility.py   # Thermodynamic feasibility
├── reaction_pathway.py       # NEB and transition states
├── thermo_kinetics.py        # Kinetic calculations
├── network_model.py          # Reaction network analysis
├── visualizer.py             # Visualization tools
├── examples/                 # Sample reactions and tutorials
└── requirements.txt          # Dependencies
```

## 🧬 Supported Reactions

- **Gas-phase reactions**: H2 + O2 → H2O
- **Substitution reactions**: CH4 + Cl2 → CH3Cl + HCl
- **SN2 mechanisms**: Nucleophilic substitutions
- **Custom reactions**: User-defined reactants and products

## 📊 Output

- Optimized molecular geometries
- Reaction energy profiles
- Activation energies and barriers
- Rate constants and kinetic parameters
- 3D visualizations of molecules and transition states
- Reaction network graphs

## 🔧 Advanced Features

- **Solvent Effects**: Implicit solvation models
- **Catalyst Modeling**: Include catalytic species
- **Temperature Scanning**: Analyze temperature dependence
- **Pressure Effects**: High-pressure reaction conditions
- **Multi-step Reactions**: Complex reaction mechanisms

## 📚 Detailed Examples

### 1. Hydrogen Combustion Analysis
```bash
# Complete thermodynamic and kinetic analysis
python main.py --reactants H2 O2 --products H2O \
  --feasibility --thermodynamics --visualize \
  --temperature 298.15 --output h2_combustion
```

### 2. Reaction Pathway with Transition States
```bash
# Find reaction pathway using NEB method
python main.py --pathway --reactants CH4 Cl2 --products CH3Cl HCl \
  --n-images 9 --spring-constant 1.5 --kinetics \
  --temperature 500 --visualize --save-structures
```

### 3. Multi-Reaction Network Analysis
```bash
# Build comprehensive reaction network
python main.py --network \
  --reactions "H2,O2->H2O" "CH4,O2->CO2,H2O" "CO2,H2->CO,H2O" \
  --visualize --output reaction_network
```

### 4. Run Example Reactions
```bash
# Execute all sample reactions with detailed analysis
python examples/sample_reactions.py

# Run comprehensive test suite
python test_chemlab.py
```

## 🛠️ Troubleshooting

### Common Issues

**Import Errors**: Check Python path and virtual environment
```bash
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

**RDKit Installation**: Use conda for best compatibility
```bash
conda install -c conda-forge rdkit
```

**Memory Issues**: Use EMT method for large molecules
```bash
python main.py --method emt --optimize --molecule large_molecule
```

## 🤝 Contributing

We welcome contributions! Please:

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Run the test suite: `python test_chemlab.py`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **ASE Team**: Atomic Simulation Environment framework
- **PySCF Developers**: Quantum chemistry calculations
- **RDKit Community**: Cheminformatics and molecular handling
- **Scientific Python**: NumPy, SciPy, Matplotlib ecosystem
