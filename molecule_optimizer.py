"""
Molecule Optimizer Module for ChemLab

This module handles DFT geometry optimization using ASE and PySCF for accurate
molecular structure determination and energy calculations.

Author: ChemLab Development Team
"""

import numpy as np
from typing import Dict, Any, Optional, Tuple, List
from ase import Atoms
from ase.optimize import BFGS, LBFGS
from ase.calculators.calculator import Calculator
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from pyscf import gto, scf, dft
    from pyscf.geomopt.geometric_solver import optimize
    PYSCF_AVAILABLE = True
except ImportError:
    logger.warning("PySCF not available. Using ASE calculators only.")
    PYSCF_AVAILABLE = False

try:
    from ase.calculators.emt import EMT
    EMT_AVAILABLE = True
except ImportError:
    EMT_AVAILABLE = False


class PySCFCalculator(Calculator):
    """
    ASE Calculator interface for PySCF DFT calculations.
    """
    
    implemented_properties = ['energy', 'forces']
    
    def __init__(self, basis='6-31g*', xc='b3lyp', charge=0, spin=0, **kwargs):
        """
        Initialize PySCF calculator.
        
        Args:
            basis: Basis set (default: '6-31g*')
            xc: Exchange-correlation functional (default: 'b3lyp')
            charge: Molecular charge (default: 0)
            spin: Spin multiplicity - 1 (default: 0 for singlet)
        """
        Calculator.__init__(self, **kwargs)
        self.basis = basis
        self.xc = xc
        self.charge = charge
        self.spin = spin
        self.mol = None
        self.mf = None
        
    def calculate(self, atoms=None, properties=['energy'], system_changes=['positions']):
        """
        Perform DFT calculation.
        
        Args:
            atoms: ASE Atoms object
            properties: List of properties to calculate
            system_changes: List of changes since last calculation
        """
        Calculator.calculate(self, atoms, properties, system_changes)
        
        # Build PySCF molecule
        atom_string = self._build_atom_string(atoms)
        
        try:
            self.mol = gto.Mole()
            self.mol.atom = atom_string
            self.mol.basis = self.basis
            self.mol.charge = self.charge
            self.mol.spin = self.spin
            self.mol.build()
            
            # Perform DFT calculation
            if self.xc.lower() in ['hf', 'hartree-fock']:
                self.mf = scf.RHF(self.mol)
            else:
                self.mf = dft.RKS(self.mol)
                self.mf.xc = self.xc
            
            energy = self.mf.kernel()
            
            # Calculate forces if requested
            forces = None
            if 'forces' in properties:
                forces = self._calculate_forces()
            
            # Store results
            self.results['energy'] = energy
            if forces is not None:
                self.results['forces'] = forces
                
        except Exception as e:
            logger.error(f"PySCF calculation failed: {e}")
            raise
    
    def _build_atom_string(self, atoms: Atoms) -> str:
        """
        Build PySCF atom string from ASE Atoms object.
        
        Args:
            atoms: ASE Atoms object
            
        Returns:
            PySCF-formatted atom string
        """
        atom_lines = []
        for symbol, pos in zip(atoms.get_chemical_symbols(), atoms.get_positions()):
            atom_lines.append(f"{symbol} {pos[0]:.6f} {pos[1]:.6f} {pos[2]:.6f}")
        return "; ".join(atom_lines)
    
    def _calculate_forces(self) -> np.ndarray:
        """
        Calculate forces using PySCF gradients.
        
        Returns:
            Forces array in ASE format (negative gradients)
        """
        try:
            from pyscf import grad
            g = grad.RKS(self.mf) if hasattr(self.mf, 'xc') else grad.RHF(self.mf)
            gradients = g.kernel()
            # ASE forces are negative gradients
            return -gradients
        except Exception as e:
            logger.warning(f"Force calculation failed: {e}")
            return np.zeros((len(self.mol.atom), 3))


class MoleculeOptimizer:
    """
    Handles geometry optimization of molecules using various methods.
    """
    
    def __init__(self, method='pyscf', **calc_kwargs):
        """
        Initialize molecule optimizer.
        
        Args:
            method: Calculation method ('pyscf', 'emt', 'custom')
            **calc_kwargs: Additional calculator parameters
        """
        self.method = method
        self.calc_kwargs = calc_kwargs
        self.calculator = None
        self._setup_calculator()
    
    def _setup_calculator(self):
        """Set up the appropriate calculator based on method."""
        if self.method == 'pyscf' and PYSCF_AVAILABLE:
            self.calculator = PySCFCalculator(**self.calc_kwargs)
            logger.info("Using PySCF DFT calculator")
        elif self.method == 'emt' and EMT_AVAILABLE:
            self.calculator = EMT()
            logger.info("Using EMT calculator (fast approximation)")
        else:
            logger.warning("No suitable calculator available. Using EMT fallback.")
            if EMT_AVAILABLE:
                self.calculator = EMT()
            else:
                raise RuntimeError("No calculators available for optimization")
    
    def optimize_geometry(self, atoms: Atoms, 
                         optimizer='BFGS', 
                         fmax=0.05, 
                         steps=200,
                         logfile=None) -> Tuple[Atoms, Dict[str, Any]]:
        """
        Optimize molecular geometry.
        
        Args:
            atoms: ASE Atoms object to optimize
            optimizer: Optimization algorithm ('BFGS', 'LBFGS')
            fmax: Force convergence criterion (eV/Å)
            steps: Maximum optimization steps
            logfile: Optional log file for optimization trajectory
            
        Returns:
            Tuple of (optimized_atoms, optimization_info)
        """
        logger.info(f"Starting geometry optimization with {self.method}")
        logger.info(f"Initial formula: {atoms.get_chemical_formula()}")
        
        # Set calculator
        atoms.set_calculator(self.calculator)
        
        # Get initial energy
        try:
            initial_energy = atoms.get_potential_energy()
            logger.info(f"Initial energy: {initial_energy:.6f} eV")
        except Exception as e:
            logger.warning(f"Could not calculate initial energy: {e}")
            initial_energy = None
        
        # Set up optimizer
        if optimizer.upper() == 'BFGS':
            opt = BFGS(atoms, logfile=logfile)
        elif optimizer.upper() == 'LBFGS':
            opt = LBFGS(atoms, logfile=logfile)
        else:
            raise ValueError(f"Unsupported optimizer: {optimizer}")
        
        # Run optimization
        try:
            opt.run(fmax=fmax, steps=steps)
            
            # Get final energy and forces
            final_energy = atoms.get_potential_energy()
            final_forces = atoms.get_forces()
            max_force = np.max(np.linalg.norm(final_forces, axis=1))
            
            # Check convergence
            converged = max_force < fmax
            
            optimization_info = {
                'converged': converged,
                'initial_energy': initial_energy,
                'final_energy': final_energy,
                'energy_change': final_energy - initial_energy if initial_energy else None,
                'max_force': max_force,
                'steps_taken': opt.get_number_of_steps(),
                'method': self.method,
                'optimizer': optimizer
            }
            
            logger.info(f"Optimization completed:")
            logger.info(f"  Converged: {converged}")
            logger.info(f"  Final energy: {final_energy:.6f} eV")
            logger.info(f"  Max force: {max_force:.6f} eV/Å")
            logger.info(f"  Steps taken: {opt.get_number_of_steps()}")
            
            return atoms.copy(), optimization_info
            
        except Exception as e:
            logger.error(f"Optimization failed: {e}")
            raise
    
    def calculate_single_point(self, atoms: Atoms) -> Dict[str, Any]:
        """
        Perform single-point energy calculation.
        
        Args:
            atoms: ASE Atoms object
            
        Returns:
            Dictionary with calculation results
        """
        logger.info("Performing single-point calculation")
        
        atoms.set_calculator(self.calculator)
        
        try:
            energy = atoms.get_potential_energy()
            forces = atoms.get_forces()
            
            results = {
                'energy': energy,
                'forces': forces,
                'max_force': np.max(np.linalg.norm(forces, axis=1)),
                'formula': atoms.get_chemical_formula(),
                'method': self.method
            }
            
            logger.info(f"Single-point energy: {energy:.6f} eV")
            
            return results
            
        except Exception as e:
            logger.error(f"Single-point calculation failed: {e}")
            raise
    
    def optimize_multiple_molecules(self, molecules: List[Atoms], 
                                  **opt_kwargs) -> List[Tuple[Atoms, Dict[str, Any]]]:
        """
        Optimize multiple molecules.
        
        Args:
            molecules: List of ASE Atoms objects
            **opt_kwargs: Optimization parameters
            
        Returns:
            List of (optimized_atoms, optimization_info) tuples
        """
        results = []
        
        for i, mol in enumerate(molecules):
            logger.info(f"Optimizing molecule {i+1}/{len(molecules)}: {mol.get_chemical_formula()}")
            
            try:
                opt_mol, opt_info = self.optimize_geometry(mol.copy(), **opt_kwargs)
                results.append((opt_mol, opt_info))
            except Exception as e:
                logger.error(f"Failed to optimize molecule {i+1}: {e}")
                results.append((mol, {'converged': False, 'error': str(e)}))
        
        return results
    
    def compare_methods(self, atoms: Atoms, methods: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Compare different calculation methods on the same molecule.
        
        Args:
            atoms: ASE Atoms object
            methods: List of method names to compare
            
        Returns:
            Dictionary with results for each method
        """
        results = {}
        original_method = self.method
        
        for method in methods:
            logger.info(f"Testing method: {method}")
            
            try:
                self.method = method
                self._setup_calculator()
                
                # Single-point calculation
                sp_results = self.calculate_single_point(atoms.copy())
                results[method] = sp_results
                
            except Exception as e:
                logger.error(f"Method {method} failed: {e}")
                results[method] = {'error': str(e)}
        
        # Restore original method
        self.method = original_method
        self._setup_calculator()
        
        return results


# Convenience functions
def optimize_molecule(atoms: Atoms, method='pyscf', **kwargs) -> Tuple[Atoms, Dict[str, Any]]:
    """
    Quick function to optimize a single molecule.
    
    Args:
        atoms: ASE Atoms object
        method: Calculation method
        **kwargs: Additional optimization parameters
        
    Returns:
        Tuple of (optimized_atoms, optimization_info)
    """
    optimizer = MoleculeOptimizer(method=method)
    return optimizer.optimize_geometry(atoms, **kwargs)


def calculate_energy(atoms: Atoms, method='pyscf', **kwargs) -> float:
    """
    Quick function to calculate single-point energy.
    
    Args:
        atoms: ASE Atoms object
        method: Calculation method
        **kwargs: Additional calculator parameters
        
    Returns:
        Energy in eV
    """
    optimizer = MoleculeOptimizer(method=method, **kwargs)
    results = optimizer.calculate_single_point(atoms)
    return results['energy']


if __name__ == "__main__":
    # Example usage
    from input_handler import parse_molecule
    
    try:
        # Test with a simple molecule
        h2o = parse_molecule("H2O", "formula")
        print(f"Original H2O positions:\n{h2o.get_positions()}")
        
        # Optimize geometry
        optimizer = MoleculeOptimizer(method='emt')  # Use EMT for quick testing
        opt_h2o, opt_info = optimizer.optimize_geometry(h2o)
        
        print(f"Optimization info: {opt_info}")
        print(f"Optimized H2O positions:\n{opt_h2o.get_positions()}")
        
        print("Molecule optimizer tests completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")
