#!/usr/bin/env python3
"""
ChemLab Setup Script

This script helps set up the ChemLab environment and install dependencies.

Author: ChemLab Development Team
"""

import subprocess
import sys
import os
import platform


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install requirements
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ Dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print("   Try installing manually with: pip install -r requirements.txt")
        return False
    except FileNotFoundError:
        print("❌ requirements.txt not found")
        return False


def check_optional_dependencies():
    """Check for optional dependencies and provide installation hints."""
    print("\n🔍 Checking optional dependencies...")
    
    optional_deps = {
        'rdkit': 'conda install -c conda-forge rdkit',
        'pyscf': 'pip install pyscf',
        'gpaw': 'pip install gpaw',
        'py3Dmol': 'pip install py3Dmol'
    }
    
    for dep, install_cmd in optional_deps.items():
        try:
            __import__(dep)
            print(f"✅ {dep} is available")
        except ImportError:
            print(f"⚠️  {dep} not found (optional)")
            print(f"   Install with: {install_cmd}")


def run_tests():
    """Run the test suite to verify installation."""
    print("\n🧪 Running tests to verify installation...")
    
    try:
        result = subprocess.run([sys.executable, "test_chemlab.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ All tests passed!")
            return True
        else:
            print("⚠️  Some tests failed, but basic functionality should work")
            print("   Check the test output for details")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️  Tests timed out, but installation is likely successful")
        return False
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False


def create_example_config():
    """Create example configuration files."""
    print("\n📝 Creating example configuration...")
    
    # Create a simple example input file
    example_config = """# ChemLab Example Configuration
# This file shows how to set up reaction calculations

reactions:
  - name: "hydrogen_combustion"
    reactants: ["H2", "O2"]
    products: ["H2O"]
    temperature: 298.15
    pressure: 101325
    
  - name: "methane_chlorination"
    reactants: ["CH4", "Cl2"]
    products: ["CH3Cl", "HCl"]
    temperature: 500.0
    pressure: 101325

calculation_settings:
  method: "emt"  # or "pyscf" for more accurate calculations
  optimization:
    fmax: 0.05
    steps: 200
  pathway:
    n_images: 7
    spring_constant: 1.0
"""
    
    try:
        with open("examples/example_config.yaml", "w") as f:
            f.write(example_config)
        print("✅ Created examples/example_config.yaml")
        return True
    except Exception as e:
        print(f"❌ Failed to create example config: {e}")
        return False


def print_usage_examples():
    """Print usage examples."""
    print("\n🚀 Usage Examples:")
    print("=" * 50)
    
    examples = [
        ("Analyze a simple reaction:", 
         "python main.py --reactants H2 O2 --products H2O --temperature 298.15"),
        
        ("Find reaction pathway:", 
         "python main.py --pathway --reactants CH4 Cl2 --products CH3Cl HCl"),
        
        ("Optimize molecule geometry:", 
         "python main.py --optimize --molecule H2O --method emt"),
        
        ("Build reaction network:", 
         "python main.py --network --reactions \"H2,O2->H2O\" \"CH4,O2->CO2,H2O\""),
        
        ("Run example reactions:", 
         "python examples/sample_reactions.py"),
        
        ("Run test suite:", 
         "python test_chemlab.py"),
        
        ("Get help:", 
         "python main.py --help")
    ]
    
    for description, command in examples:
        print(f"\n{description}")
        print(f"  {command}")


def main():
    """Main setup function."""
    print("🧪 ChemLab Setup")
    print("=" * 50)
    print("Setting up your chemical reaction simulation environment...")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check platform
    print(f"✅ Platform: {platform.system()} {platform.release()}")
    
    # Install dependencies
    deps_ok = install_dependencies()
    
    # Check optional dependencies
    check_optional_dependencies()
    
    # Create example config
    create_example_config()
    
    # Run tests if dependencies were installed successfully
    if deps_ok:
        tests_ok = run_tests()
    else:
        tests_ok = False
    
    # Print summary
    print("\n" + "=" * 50)
    print("📋 Setup Summary")
    print("=" * 50)
    
    if deps_ok and tests_ok:
        print("✅ ChemLab setup completed successfully!")
        print("   You're ready to simulate chemical reactions!")
    elif deps_ok:
        print("⚠️  ChemLab setup mostly completed")
        print("   Some tests failed, but basic functionality should work")
    else:
        print("❌ ChemLab setup encountered issues")
        print("   Please check error messages and try manual installation")
    
    # Print usage examples
    print_usage_examples()
    
    print("\n🎉 Happy simulating!")


if __name__ == "__main__":
    main()
