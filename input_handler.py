"""
Input Handler Module for ChemLab

This module handles parsing and conversion of molecular inputs from various formats
(SMILES, XYZ, chemical formulas) into ASE Atoms objects for computational chemistry.

Author: ChemLab Development Team
"""

import os
import numpy as np
from typing import List, Union, Optional, Dict, Any
from ase import Atoms
from ase.io import read, write
from ase.build import molecule
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from rdkit import Chem
    from rdkit.Chem import AllChem
    RDKIT_AVAILABLE = True
except ImportError:
    logger.warning("RDKit not available. SMILES parsing will be limited.")
    RDKIT_AVAILABLE = False


class MoleculeInputHandler:
    """
    Handles conversion of various molecular input formats to ASE Atoms objects.
    
    Supported formats:
    - SMILES strings
    - XYZ files
    - Chemical formulas (simple molecules)
    - ASE molecule database
    - Custom coordinate arrays
    """
    
    def __init__(self):
        """Initialize the input handler."""
        self.supported_formats = ['smiles', 'xyz', 'formula', 'ase_db', 'coordinates']
        
    def parse_molecule(self, input_data: Union[str, np.ndarray, Dict], 
                      input_type: str = 'auto') -> Atoms:
        """
        Parse molecular input and return ASE Atoms object.
        
        Args:
            input_data: Molecular data (SMILES string, file path, etc.)
            input_type: Type of input ('smiles', 'xyz', 'formula', 'auto')
            
        Returns:
            ASE Atoms object
            
        Raises:
            ValueError: If input format is not supported or parsing fails
        """
        if input_type == 'auto':
            input_type = self._detect_input_type(input_data)
            
        logger.info(f"Parsing molecule with input type: {input_type}")
        
        if input_type == 'smiles':
            return self._parse_smiles(input_data)
        elif input_type == 'xyz':
            return self._parse_xyz(input_data)
        elif input_type == 'formula':
            return self._parse_formula(input_data)
        elif input_type == 'ase_db':
            return self._parse_ase_molecule(input_data)
        elif input_type == 'coordinates':
            return self._parse_coordinates(input_data)
        else:
            raise ValueError(f"Unsupported input type: {input_type}")
    
    def _detect_input_type(self, input_data: Union[str, np.ndarray, Dict]) -> str:
        """
        Automatically detect the input type based on the data format.
        
        Args:
            input_data: Input molecular data
            
        Returns:
            Detected input type string
        """
        if isinstance(input_data, str):
            # Check if it's a file path
            if os.path.isfile(input_data):
                if input_data.endswith('.xyz'):
                    return 'xyz'
                else:
                    return 'xyz'  # Default to xyz for files
            
            # Check if it's a SMILES string (contains typical SMILES characters)
            if any(char in input_data for char in ['C', 'N', 'O', '(', ')', '=', '#']):
                if len(input_data) < 50 and not ' ' in input_data:  # Typical SMILES characteristics
                    return 'smiles'
            
            # Check if it's a simple chemical formula
            if all(char.isalnum() for char in input_data.replace(' ', '')):
                return 'formula'
                
        elif isinstance(input_data, (np.ndarray, list)):
            return 'coordinates'
        elif isinstance(input_data, dict):
            return 'coordinates'
            
        # Default fallback
        return 'formula'
    
    def _parse_smiles(self, smiles: str) -> Atoms:
        """
        Parse SMILES string to ASE Atoms object.
        
        Args:
            smiles: SMILES string representation
            
        Returns:
            ASE Atoms object with 3D coordinates
        """
        if not RDKIT_AVAILABLE:
            raise ImportError("RDKit is required for SMILES parsing. Please install rdkit.")
        
        try:
            # Create molecule from SMILES
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                raise ValueError(f"Invalid SMILES string: {smiles}")
            
            # Add hydrogens
            mol = Chem.AddHs(mol)
            
            # Generate 3D coordinates
            AllChem.EmbedMolecule(mol, randomSeed=42)
            AllChem.MMFFOptimizeMolecule(mol)
            
            # Extract atomic symbols and coordinates
            symbols = []
            positions = []
            
            for atom in mol.GetAtoms():
                symbols.append(atom.GetSymbol())
            
            conf = mol.GetConformer()
            for i in range(mol.GetNumAtoms()):
                pos = conf.GetAtomPosition(i)
                positions.append([pos.x, pos.y, pos.z])
            
            return Atoms(symbols=symbols, positions=positions)
            
        except Exception as e:
            logger.error(f"Failed to parse SMILES '{smiles}': {e}")
            raise ValueError(f"SMILES parsing failed: {e}")
    
    def _parse_xyz(self, xyz_input: str) -> Atoms:
        """
        Parse XYZ file or string to ASE Atoms object.
        
        Args:
            xyz_input: XYZ file path or XYZ format string
            
        Returns:
            ASE Atoms object
        """
        try:
            if os.path.isfile(xyz_input):
                # Read from file
                atoms = read(xyz_input)
            else:
                # Parse XYZ string
                lines = xyz_input.strip().split('\n')
                if len(lines) < 3:
                    raise ValueError("Invalid XYZ format")
                
                n_atoms = int(lines[0])
                symbols = []
                positions = []
                
                for i in range(2, 2 + n_atoms):
                    parts = lines[i].split()
                    symbols.append(parts[0])
                    positions.append([float(parts[1]), float(parts[2]), float(parts[3])])
                
                atoms = Atoms(symbols=symbols, positions=positions)
            
            return atoms
            
        except Exception as e:
            logger.error(f"Failed to parse XYZ input: {e}")
            raise ValueError(f"XYZ parsing failed: {e}")
    
    def _parse_formula(self, formula: str) -> Atoms:
        """
        Parse chemical formula using ASE's molecule database.
        
        Args:
            formula: Chemical formula (e.g., 'H2O', 'CH4', 'CO2')
            
        Returns:
            ASE Atoms object
        """
        try:
            # Try ASE's built-in molecule database first
            atoms = molecule(formula)
            return atoms
            
        except Exception as e:
            logger.warning(f"ASE molecule database lookup failed for '{formula}': {e}")
            
            # Fallback: try to build simple molecules manually
            return self._build_simple_molecule(formula)
    
    def _build_simple_molecule(self, formula: str) -> Atoms:
        """
        Build simple molecules manually for common cases.
        
        Args:
            formula: Chemical formula
            
        Returns:
            ASE Atoms object
        """
        # Simple molecule templates
        simple_molecules = {
            'H2': Atoms('H2', positions=[[0, 0, 0], [0.74, 0, 0]]),
            'O2': Atoms('O2', positions=[[0, 0, 0], [1.21, 0, 0]]),
            'N2': Atoms('N2', positions=[[0, 0, 0], [1.10, 0, 0]]),
            'CO': Atoms('CO', positions=[[0, 0, 0], [1.13, 0, 0]]),
            'HCl': Atoms('HCl', positions=[[0, 0, 0], [1.27, 0, 0]]),
        }
        
        if formula in simple_molecules:
            return simple_molecules[formula]
        else:
            raise ValueError(f"Cannot build molecule for formula: {formula}")
    
    def _parse_ase_molecule(self, molecule_name: str) -> Atoms:
        """
        Get molecule from ASE's built-in database.
        
        Args:
            molecule_name: Name of molecule in ASE database
            
        Returns:
            ASE Atoms object
        """
        try:
            return molecule(molecule_name)
        except Exception as e:
            raise ValueError(f"Molecule '{molecule_name}' not found in ASE database: {e}")
    
    def _parse_coordinates(self, coord_data: Union[Dict, np.ndarray, List]) -> Atoms:
        """
        Parse coordinate data to ASE Atoms object.
        
        Args:
            coord_data: Dictionary with 'symbols' and 'positions' or array of coordinates
            
        Returns:
            ASE Atoms object
        """
        if isinstance(coord_data, dict):
            if 'symbols' in coord_data and 'positions' in coord_data:
                return Atoms(symbols=coord_data['symbols'], 
                           positions=coord_data['positions'])
            else:
                raise ValueError("Dictionary must contain 'symbols' and 'positions' keys")
        else:
            raise ValueError("Coordinate data format not supported")
    
    def parse_multiple_molecules(self, molecule_list: List[Union[str, Dict]]) -> List[Atoms]:
        """
        Parse multiple molecules from a list of inputs.
        
        Args:
            molecule_list: List of molecular inputs
            
        Returns:
            List of ASE Atoms objects
        """
        molecules = []
        for i, mol_input in enumerate(molecule_list):
            try:
                atoms = self.parse_molecule(mol_input)
                molecules.append(atoms)
                logger.info(f"Successfully parsed molecule {i+1}: {atoms.get_chemical_formula()}")
            except Exception as e:
                logger.error(f"Failed to parse molecule {i+1}: {e}")
                raise
        
        return molecules
    
    def save_molecule(self, atoms: Atoms, filename: str, format: str = 'xyz') -> None:
        """
        Save ASE Atoms object to file.
        
        Args:
            atoms: ASE Atoms object
            filename: Output filename
            format: Output format ('xyz', 'pdb', etc.)
        """
        try:
            write(filename, atoms, format=format)
            logger.info(f"Molecule saved to {filename}")
        except Exception as e:
            logger.error(f"Failed to save molecule: {e}")
            raise


# Convenience functions for quick access
def parse_molecule(input_data: Union[str, np.ndarray, Dict], 
                  input_type: str = 'auto') -> Atoms:
    """
    Quick function to parse a single molecule.
    
    Args:
        input_data: Molecular input data
        input_type: Input type ('auto', 'smiles', 'xyz', 'formula')
        
    Returns:
        ASE Atoms object
    """
    handler = MoleculeInputHandler()
    return handler.parse_molecule(input_data, input_type)


def parse_molecules(molecule_list: List[Union[str, Dict]]) -> List[Atoms]:
    """
    Quick function to parse multiple molecules.
    
    Args:
        molecule_list: List of molecular inputs
        
    Returns:
        List of ASE Atoms objects
    """
    handler = MoleculeInputHandler()
    return handler.parse_multiple_molecules(molecule_list)


if __name__ == "__main__":
    # Example usage
    handler = MoleculeInputHandler()
    
    # Test different input formats
    try:
        # Test simple molecules
        h2o = handler.parse_molecule("H2O", "formula")
        print(f"H2O: {h2o.get_chemical_formula()}")
        
        h2 = handler.parse_molecule("H2", "formula")
        print(f"H2: {h2.get_chemical_formula()}")
        
        # Test SMILES (if RDKit is available)
        if RDKIT_AVAILABLE:
            methane = handler.parse_molecule("C", "smiles")
            print(f"Methane from SMILES: {methane.get_chemical_formula()}")
        
        print("Input handler tests completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")
