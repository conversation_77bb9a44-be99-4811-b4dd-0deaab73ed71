"""
Reaction Feasibility Module for ChemLab

This module analyzes the thermodynamic feasibility of chemical reactions
using energy calculations and thermodynamic principles.

Author: ChemLab Development Team
"""

import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from ase import Atoms
from ase.thermochemistry import IdealGasThermo
from ase.vibrations import Vibrations
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Physical constants
KB = 8.617333e-5  # Boltzmann constant in eV/K
R = 8.314  # Gas constant in J/(mol·K)
AVOGADRO = 6.022e23  # Avogadro's number


class ReactionFeasibilityAnalyzer:
    """
    Analyzes thermodynamic feasibility of chemical reactions.
    """
    
    def __init__(self, temperature=298.15, pressure=101325):
        """
        Initialize feasibility analyzer.
        
        Args:
            temperature: Temperature in Kelvin (default: 298.15 K)
            pressure: Pressure in Pa (default: 101325 Pa = 1 atm)
        """
        self.temperature = temperature
        self.pressure = pressure
        
    def analyze_reaction(self, reactants: List[Atoms], products: List[Atoms],
                        method='energy_only') -> Dict[str, Any]:
        """
        Analyze reaction feasibility.
        
        Args:
            reactants: List of reactant ASE Atoms objects
            products: List of product ASE Atoms objects
            method: Analysis method ('energy_only', 'thermodynamic', 'kinetic')
            
        Returns:
            Dictionary with feasibility analysis results
        """
        logger.info(f"Analyzing reaction feasibility at T={self.temperature}K, P={self.pressure}Pa")
        
        # Calculate reaction energetics
        reaction_energy = self._calculate_reaction_energy(reactants, products)
        
        results = {
            'reaction_energy': reaction_energy,
            'temperature': self.temperature,
            'pressure': self.pressure,
            'method': method,
            'feasible': None,
            'confidence': None
        }
        
        if method == 'energy_only':
            results.update(self._energy_based_analysis(reaction_energy))
        elif method == 'thermodynamic':
            results.update(self._thermodynamic_analysis(reactants, products, reaction_energy))
        elif method == 'kinetic':
            results.update(self._kinetic_analysis(reactants, products, reaction_energy))
        else:
            raise ValueError(f"Unknown analysis method: {method}")
        
        return results
    
    def _calculate_reaction_energy(self, reactants: List[Atoms], products: List[Atoms]) -> float:
        """
        Calculate reaction energy (ΔE = E_products - E_reactants).
        
        Args:
            reactants: List of reactant molecules
            products: List of product molecules
            
        Returns:
            Reaction energy in eV
        """
        from molecule_optimizer import calculate_energy
        
        # Calculate total energy of reactants
        reactant_energy = 0.0
        for reactant in reactants:
            try:
                energy = calculate_energy(reactant)
                reactant_energy += energy
                logger.info(f"Reactant {reactant.get_chemical_formula()}: {energy:.6f} eV")
            except Exception as e:
                logger.warning(f"Failed to calculate energy for reactant: {e}")
                return np.nan
        
        # Calculate total energy of products
        product_energy = 0.0
        for product in products:
            try:
                energy = calculate_energy(product)
                product_energy += energy
                logger.info(f"Product {product.get_chemical_formula()}: {energy:.6f} eV")
            except Exception as e:
                logger.warning(f"Failed to calculate energy for product: {e}")
                return np.nan
        
        reaction_energy = product_energy - reactant_energy
        logger.info(f"Reaction energy: {reaction_energy:.6f} eV")
        
        return reaction_energy
    
    def _energy_based_analysis(self, reaction_energy: float) -> Dict[str, Any]:
        """
        Simple energy-based feasibility analysis.
        
        Args:
            reaction_energy: Reaction energy in eV
            
        Returns:
            Analysis results
        """
        if np.isnan(reaction_energy):
            return {
                'feasible': False,
                'confidence': 0.0,
                'reason': 'Energy calculation failed'
            }
        
        # Simple heuristics for feasibility
        if reaction_energy < -2.0:  # Highly exothermic
            feasible = True
            confidence = 0.9
            reason = "Highly exothermic reaction"
        elif reaction_energy < 0.0:  # Exothermic
            feasible = True
            confidence = 0.7
            reason = "Exothermic reaction"
        elif reaction_energy < 1.0:  # Slightly endothermic
            feasible = True
            confidence = 0.5
            reason = "Slightly endothermic, may require activation"
        elif reaction_energy < 3.0:  # Moderately endothermic
            feasible = False
            confidence = 0.3
            reason = "Moderately endothermic, unlikely without external energy"
        else:  # Highly endothermic
            feasible = False
            confidence = 0.9
            reason = "Highly endothermic, thermodynamically unfavorable"
        
        return {
            'feasible': feasible,
            'confidence': confidence,
            'reason': reason,
            'energy_category': self._categorize_energy(reaction_energy)
        }
    
    def _thermodynamic_analysis(self, reactants: List[Atoms], products: List[Atoms],
                               reaction_energy: float) -> Dict[str, Any]:
        """
        Thermodynamic analysis including entropy effects.
        
        Args:
            reactants: List of reactant molecules
            products: List of product molecules
            reaction_energy: Reaction energy in eV
            
        Returns:
            Thermodynamic analysis results
        """
        try:
            # Estimate entropy changes (simplified)
            delta_s = self._estimate_entropy_change(reactants, products)
            
            # Calculate Gibbs free energy change
            # ΔG = ΔH - TΔS (approximating ΔH ≈ ΔE for gas-phase reactions)
            delta_g = reaction_energy - (self.temperature * delta_s / 1000)  # Convert J to eV
            
            # Equilibrium constant
            k_eq = np.exp(-delta_g / (KB * self.temperature))
            
            # Feasibility based on Gibbs free energy
            if delta_g < -0.1:  # Spontaneous
                feasible = True
                confidence = min(0.9, 0.5 + abs(delta_g) * 0.1)
                reason = f"Thermodynamically favorable (ΔG = {delta_g:.3f} eV)"
            elif delta_g < 0.1:  # Near equilibrium
                feasible = True
                confidence = 0.5
                reason = f"Near equilibrium (ΔG = {delta_g:.3f} eV)"
            else:  # Non-spontaneous
                feasible = False
                confidence = min(0.9, 0.5 + abs(delta_g) * 0.1)
                reason = f"Thermodynamically unfavorable (ΔG = {delta_g:.3f} eV)"
            
            return {
                'feasible': feasible,
                'confidence': confidence,
                'reason': reason,
                'delta_g': delta_g,
                'delta_s': delta_s,
                'equilibrium_constant': k_eq,
                'spontaneous': delta_g < 0
            }
            
        except Exception as e:
            logger.error(f"Thermodynamic analysis failed: {e}")
            return self._energy_based_analysis(reaction_energy)
    
    def _kinetic_analysis(self, reactants: List[Atoms], products: List[Atoms],
                         reaction_energy: float) -> Dict[str, Any]:
        """
        Kinetic feasibility analysis (simplified).
        
        Args:
            reactants: List of reactant molecules
            products: List of product molecules
            reaction_energy: Reaction energy in eV
            
        Returns:
            Kinetic analysis results
        """
        # Estimate activation energy using empirical correlations
        # This is a simplified approach - real kinetic analysis requires TS search
        
        if reaction_energy < 0:  # Exothermic
            # Bell-Evans-Polanyi principle: Ea ≈ α * ΔE + β
            activation_energy = max(0.1, 0.3 * abs(reaction_energy) + 0.5)
        else:  # Endothermic
            # For endothermic reactions, Ea ≥ ΔE
            activation_energy = reaction_energy + 0.5
        
        # Rate constant estimation (Arrhenius equation)
        # k = A * exp(-Ea / (kB * T))
        # Using typical pre-exponential factor A ≈ 10^13 s^-1
        pre_exponential = 1e13
        rate_constant = pre_exponential * np.exp(-activation_energy / (KB * self.temperature))
        
        # Half-life estimation (assuming first-order kinetics)
        half_life = np.log(2) / rate_constant if rate_constant > 0 else np.inf
        
        # Feasibility based on kinetics
        if activation_energy < 0.5:  # Low barrier
            feasible = True
            confidence = 0.8
            reason = f"Low activation barrier ({activation_energy:.3f} eV)"
        elif activation_energy < 1.5:  # Moderate barrier
            feasible = True
            confidence = 0.6
            reason = f"Moderate activation barrier ({activation_energy:.3f} eV)"
        elif activation_energy < 3.0:  # High barrier
            feasible = False
            confidence = 0.7
            reason = f"High activation barrier ({activation_energy:.3f} eV)"
        else:  # Very high barrier
            feasible = False
            confidence = 0.9
            reason = f"Very high activation barrier ({activation_energy:.3f} eV)"
        
        return {
            'feasible': feasible,
            'confidence': confidence,
            'reason': reason,
            'activation_energy': activation_energy,
            'rate_constant': rate_constant,
            'half_life': half_life,
            'kinetically_favorable': activation_energy < 1.5
        }
    
    def _estimate_entropy_change(self, reactants: List[Atoms], products: List[Atoms]) -> float:
        """
        Estimate entropy change for the reaction (simplified).
        
        Args:
            reactants: List of reactant molecules
            products: List of product molecules
            
        Returns:
            Entropy change in J/(mol·K)
        """
        # Simple estimation based on molecular complexity and stoichiometry
        
        def estimate_molecular_entropy(atoms):
            """Estimate molecular entropy based on size and complexity."""
            n_atoms = len(atoms)
            # Rough estimation: S ≈ 150 + 20*n_atoms J/(mol·K) for gas-phase molecules
            return 150 + 20 * n_atoms
        
        # Calculate entropy of reactants and products
        reactant_entropy = sum(estimate_molecular_entropy(mol) for mol in reactants)
        product_entropy = sum(estimate_molecular_entropy(mol) for mol in products)
        
        # Account for stoichiometry (change in number of molecules)
        n_reactants = len(reactants)
        n_products = len(products)
        
        # Additional entropy change due to change in number of particles
        # ΔS_mixing ≈ -R * ln(n_products/n_reactants) per mole
        if n_reactants != n_products:
            mixing_entropy = -R * np.log(n_products / n_reactants) if n_reactants > 0 else 0
        else:
            mixing_entropy = 0
        
        delta_s = product_entropy - reactant_entropy + mixing_entropy
        
        return delta_s
    
    def _categorize_energy(self, energy: float) -> str:
        """
        Categorize reaction energy.
        
        Args:
            energy: Reaction energy in eV
            
        Returns:
            Energy category string
        """
        if energy < -2.0:
            return "highly_exothermic"
        elif energy < -0.5:
            return "exothermic"
        elif energy < 0.5:
            return "thermoneutral"
        elif energy < 2.0:
            return "endothermic"
        else:
            return "highly_endothermic"
    
    def batch_analysis(self, reaction_list: List[Tuple[List[Atoms], List[Atoms]]],
                      method='energy_only') -> List[Dict[str, Any]]:
        """
        Analyze multiple reactions.
        
        Args:
            reaction_list: List of (reactants, products) tuples
            method: Analysis method
            
        Returns:
            List of analysis results
        """
        results = []
        
        for i, (reactants, products) in enumerate(reaction_list):
            logger.info(f"Analyzing reaction {i+1}/{len(reaction_list)}")
            
            try:
                result = self.analyze_reaction(reactants, products, method)
                result['reaction_id'] = i
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to analyze reaction {i+1}: {e}")
                results.append({
                    'reaction_id': i,
                    'feasible': False,
                    'confidence': 0.0,
                    'reason': f"Analysis failed: {e}"
                })
        
        return results
    
    def rank_reactions(self, reaction_list: List[Tuple[List[Atoms], List[Atoms]]],
                      method='energy_only') -> List[Tuple[int, Dict[str, Any]]]:
        """
        Rank reactions by feasibility.
        
        Args:
            reaction_list: List of (reactants, products) tuples
            method: Analysis method
            
        Returns:
            List of (reaction_index, analysis_result) sorted by feasibility
        """
        results = self.batch_analysis(reaction_list, method)
        
        # Sort by feasibility and confidence
        def sort_key(result):
            feasible = result.get('feasible', False)
            confidence = result.get('confidence', 0.0)
            return (feasible, confidence)
        
        sorted_results = sorted(enumerate(results), key=lambda x: sort_key(x[1]), reverse=True)
        
        return sorted_results


# Convenience functions
def is_reaction_feasible(reactants: List[Atoms], products: List[Atoms],
                        temperature=298.15, method='energy_only') -> bool:
    """
    Quick function to check if a reaction is feasible.
    
    Args:
        reactants: List of reactant molecules
        products: List of product molecules
        temperature: Temperature in Kelvin
        method: Analysis method
        
    Returns:
        True if reaction is predicted to be feasible
    """
    analyzer = ReactionFeasibilityAnalyzer(temperature=temperature)
    result = analyzer.analyze_reaction(reactants, products, method)
    return result.get('feasible', False)


def calculate_reaction_energy(reactants: List[Atoms], products: List[Atoms]) -> float:
    """
    Quick function to calculate reaction energy.
    
    Args:
        reactants: List of reactant molecules
        products: List of product molecules
        
    Returns:
        Reaction energy in eV
    """
    analyzer = ReactionFeasibilityAnalyzer()
    return analyzer._calculate_reaction_energy(reactants, products)


if __name__ == "__main__":
    # Example usage
    from input_handler import parse_molecule
    
    try:
        # Test reaction: H2 + 1/2 O2 -> H2O
        h2 = parse_molecule("H2", "formula")
        o2 = parse_molecule("O2", "formula")
        h2o = parse_molecule("H2O", "formula")
        
        reactants = [h2, o2]
        products = [h2o]
        
        analyzer = ReactionFeasibilityAnalyzer()
        result = analyzer.analyze_reaction(reactants, products, method='energy_only')
        
        print(f"Reaction feasibility analysis:")
        print(f"  Feasible: {result['feasible']}")
        print(f"  Confidence: {result['confidence']:.2f}")
        print(f"  Reason: {result['reason']}")
        print(f"  Reaction energy: {result['reaction_energy']:.6f} eV")
        
        print("Reaction feasibility tests completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")
