"""
Reaction Pathway Module for ChemLab

This module implements NEB (Nudged Elastic Band) and transition state search
methods for finding reaction pathways and activation barriers.

Author: ChemLab Development Team
"""

import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from ase import Atoms
from ase.neb import NEB
from ase.optimize import BFGS, FIRE
from ase.io import write
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from ase.calculators.emt import EMT
    EMT_AVAILABLE = True
except ImportError:
    EMT_AVAILABLE = False


class ReactionPathwayFinder:
    """
    Finds reaction pathways using NEB and transition state search methods.
    """
    
    def __init__(self, calculator=None, n_images=7):
        """
        Initialize pathway finder.
        
        Args:
            calculator: ASE calculator for energy/force calculations
            n_images: Number of images in NEB calculation (including endpoints)
        """
        self.calculator = calculator or (EMT() if EMT_AVAILABLE else None)
        self.n_images = n_images
        
        if self.calculator is None:
            raise RuntimeError("No calculator available for pathway calculations")
    
    def find_pathway(self, initial: Atoms, final: Atoms, 
                    method='neb', **kwargs) -> Dict[str, Any]:
        """
        Find reaction pathway between initial and final states.
        
        Args:
            initial: Initial state ASE Atoms object
            final: Final state ASE Atoms object
            method: Pathway method ('neb', 'string', 'dimer')
            **kwargs: Additional method-specific parameters
            
        Returns:
            Dictionary with pathway results
        """
        logger.info(f"Finding reaction pathway using {method}")
        logger.info(f"Initial: {initial.get_chemical_formula()}")
        logger.info(f"Final: {final.get_chemical_formula()}")
        
        if method.lower() == 'neb':
            return self._neb_pathway(initial, final, **kwargs)
        elif method.lower() == 'string':
            return self._string_method(initial, final, **kwargs)
        elif method.lower() == 'dimer':
            return self._dimer_method(initial, final, **kwargs)
        else:
            raise ValueError(f"Unknown pathway method: {method}")
    
    def _neb_pathway(self, initial: Atoms, final: Atoms,
                    n_images: Optional[int] = None,
                    spring_constant: float = 1.0,
                    climb: bool = True,
                    optimizer: str = 'FIRE',
                    fmax: float = 0.05,
                    steps: int = 200) -> Dict[str, Any]:
        """
        Find pathway using Nudged Elastic Band method.
        
        Args:
            initial: Initial state
            final: Final state
            n_images: Number of images (default: self.n_images)
            spring_constant: NEB spring constant
            climb: Use climbing image NEB
            optimizer: Optimization algorithm
            fmax: Force convergence criterion
            steps: Maximum optimization steps
            
        Returns:
            NEB pathway results
        """
        if n_images is None:
            n_images = self.n_images
        
        logger.info(f"Setting up NEB with {n_images} images")
        
        # Create image chain
        images = self._create_image_chain(initial, final, n_images)
        
        # Set calculators
        for image in images:
            image.set_calculator(self.calculator)
        
        # Create NEB object
        neb = NEB(images, k=spring_constant, climb=climb)
        
        # Set up optimizer
        if optimizer.upper() == 'FIRE':
            opt = FIRE(neb)
        elif optimizer.upper() == 'BFGS':
            opt = BFGS(neb)
        else:
            raise ValueError(f"Unsupported optimizer: {optimizer}")
        
        # Run optimization
        try:
            logger.info("Starting NEB optimization...")
            opt.run(fmax=fmax, steps=steps)
            
            # Extract results
            energies = [image.get_potential_energy() for image in images]
            forces = [image.get_forces() for image in images]
            
            # Find transition state
            ts_index = np.argmax(energies)
            ts_energy = energies[ts_index]
            ts_structure = images[ts_index].copy()
            
            # Calculate barriers
            forward_barrier = ts_energy - energies[0]
            reverse_barrier = ts_energy - energies[-1]
            reaction_energy = energies[-1] - energies[0]
            
            # Check convergence
            max_forces = [np.max(np.linalg.norm(f, axis=1)) for f in forces]
            converged = all(f < fmax for f in max_forces)
            
            results = {
                'method': 'NEB',
                'converged': converged,
                'n_images': n_images,
                'energies': energies,
                'forces': forces,
                'max_forces': max_forces,
                'images': [img.copy() for img in images],
                'ts_index': ts_index,
                'ts_energy': ts_energy,
                'ts_structure': ts_structure,
                'forward_barrier': forward_barrier,
                'reverse_barrier': reverse_barrier,
                'reaction_energy': reaction_energy,
                'spring_constant': spring_constant,
                'climb': climb,
                'steps_taken': opt.get_number_of_steps()
            }
            
            logger.info(f"NEB completed:")
            logger.info(f"  Converged: {converged}")
            logger.info(f"  Forward barrier: {forward_barrier:.6f} eV")
            logger.info(f"  Reverse barrier: {reverse_barrier:.6f} eV")
            logger.info(f"  Reaction energy: {reaction_energy:.6f} eV")
            logger.info(f"  TS at image {ts_index}")
            
            return results
            
        except Exception as e:
            logger.error(f"NEB calculation failed: {e}")
            raise
    
    def _create_image_chain(self, initial: Atoms, final: Atoms, n_images: int) -> List[Atoms]:
        """
        Create initial image chain by linear interpolation.
        
        Args:
            initial: Initial state
            final: Final state
            n_images: Total number of images
            
        Returns:
            List of interpolated images
        """
        if len(initial) != len(final):
            raise ValueError("Initial and final states must have same number of atoms")
        
        images = []
        
        # Add initial state
        images.append(initial.copy())
        
        # Create intermediate images by linear interpolation
        for i in range(1, n_images - 1):
            fraction = i / (n_images - 1)
            
            # Interpolate positions
            pos_initial = initial.get_positions()
            pos_final = final.get_positions()
            pos_interp = pos_initial + fraction * (pos_final - pos_initial)
            
            # Create interpolated image
            image = initial.copy()
            image.set_positions(pos_interp)
            images.append(image)
        
        # Add final state
        images.append(final.copy())
        
        logger.info(f"Created {len(images)} images for NEB calculation")
        
        return images
    
    def _string_method(self, initial: Atoms, final: Atoms, **kwargs) -> Dict[str, Any]:
        """
        String method for pathway finding (simplified implementation).
        
        Args:
            initial: Initial state
            final: Final state
            **kwargs: Additional parameters
            
        Returns:
            String method results
        """
        logger.warning("String method not fully implemented. Using NEB instead.")
        return self._neb_pathway(initial, final, **kwargs)
    
    def _dimer_method(self, initial: Atoms, final: Atoms, **kwargs) -> Dict[str, Any]:
        """
        Dimer method for transition state search (simplified implementation).
        
        Args:
            initial: Initial state
            final: Final state
            **kwargs: Additional parameters
            
        Returns:
            Dimer method results
        """
        logger.warning("Dimer method not fully implemented. Using NEB instead.")
        return self._neb_pathway(initial, final, **kwargs)
    
    def analyze_pathway(self, pathway_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze pathway results and extract key information.
        
        Args:
            pathway_results: Results from pathway calculation
            
        Returns:
            Analysis results
        """
        energies = pathway_results['energies']
        
        # Energy profile analysis
        min_energy = min(energies)
        max_energy = max(energies)
        energy_range = max_energy - min_energy
        
        # Find all local maxima (potential transition states)
        local_maxima = []
        for i in range(1, len(energies) - 1):
            if energies[i] > energies[i-1] and energies[i] > energies[i+1]:
                local_maxima.append((i, energies[i]))
        
        # Find all local minima (intermediates)
        local_minima = []
        for i in range(1, len(energies) - 1):
            if energies[i] < energies[i-1] and energies[i] < energies[i+1]:
                local_minima.append((i, energies[i]))
        
        # Reaction coordinate (simple distance-based)
        reaction_coordinate = self._calculate_reaction_coordinate(pathway_results['images'])
        
        analysis = {
            'energy_profile': energies,
            'reaction_coordinate': reaction_coordinate,
            'min_energy': min_energy,
            'max_energy': max_energy,
            'energy_range': energy_range,
            'local_maxima': local_maxima,
            'local_minima': local_minima,
            'n_transition_states': len(local_maxima),
            'n_intermediates': len(local_minima),
            'pathway_complexity': len(local_maxima) + len(local_minima)
        }
        
        return analysis
    
    def _calculate_reaction_coordinate(self, images: List[Atoms]) -> np.ndarray:
        """
        Calculate reaction coordinate based on structural changes.
        
        Args:
            images: List of images along pathway
            
        Returns:
            Reaction coordinate array
        """
        if len(images) < 2:
            return np.array([0.0])
        
        # Simple approach: cumulative distance along path
        coordinates = [0.0]
        
        for i in range(1, len(images)):
            # Calculate RMSD between consecutive images
            pos1 = images[i-1].get_positions()
            pos2 = images[i].get_positions()
            
            # Center both structures
            pos1 -= np.mean(pos1, axis=0)
            pos2 -= np.mean(pos2, axis=0)
            
            # Calculate distance
            distance = np.sqrt(np.sum((pos2 - pos1)**2))
            coordinates.append(coordinates[-1] + distance)
        
        # Normalize to [0, 1]
        coordinates = np.array(coordinates)
        if coordinates[-1] > 0:
            coordinates /= coordinates[-1]
        
        return coordinates
    
    def save_pathway(self, pathway_results: Dict[str, Any], 
                    filename_prefix: str = "pathway") -> None:
        """
        Save pathway images to files.
        
        Args:
            pathway_results: Results from pathway calculation
            filename_prefix: Prefix for output filenames
        """
        images = pathway_results['images']
        
        for i, image in enumerate(images):
            filename = f"{filename_prefix}_image_{i:03d}.xyz"
            write(filename, image)
        
        logger.info(f"Saved {len(images)} pathway images with prefix '{filename_prefix}'")
    
    def compare_pathways(self, pathway_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Compare multiple reaction pathways.
        
        Args:
            pathway_list: List of pathway results
            
        Returns:
            Comparison results
        """
        comparison = {
            'n_pathways': len(pathway_list),
            'barriers': [],
            'reaction_energies': [],
            'methods': [],
            'converged': []
        }
        
        for i, pathway in enumerate(pathway_list):
            comparison['barriers'].append(pathway.get('forward_barrier', np.nan))
            comparison['reaction_energies'].append(pathway.get('reaction_energy', np.nan))
            comparison['methods'].append(pathway.get('method', 'unknown'))
            comparison['converged'].append(pathway.get('converged', False))
        
        # Find best pathway (lowest barrier among converged)
        converged_indices = [i for i, conv in enumerate(comparison['converged']) if conv]
        
        if converged_indices:
            barriers = [comparison['barriers'][i] for i in converged_indices]
            best_index = converged_indices[np.argmin(barriers)]
            comparison['best_pathway_index'] = best_index
            comparison['best_barrier'] = comparison['barriers'][best_index]
        else:
            comparison['best_pathway_index'] = None
            comparison['best_barrier'] = None
        
        return comparison


# Convenience functions
def find_reaction_pathway(initial: Atoms, final: Atoms, 
                         method='neb', **kwargs) -> Dict[str, Any]:
    """
    Quick function to find reaction pathway.
    
    Args:
        initial: Initial state
        final: Final state
        method: Pathway method
        **kwargs: Additional parameters
        
    Returns:
        Pathway results
    """
    finder = ReactionPathwayFinder()
    return finder.find_pathway(initial, final, method, **kwargs)


def calculate_activation_barrier(initial: Atoms, final: Atoms, **kwargs) -> float:
    """
    Quick function to calculate activation barrier.
    
    Args:
        initial: Initial state
        final: Final state
        **kwargs: Additional parameters
        
    Returns:
        Forward activation barrier in eV
    """
    pathway = find_reaction_pathway(initial, final, **kwargs)
    return pathway.get('forward_barrier', np.nan)


    def optimize_pathway_images(self, images: List[Atoms],
                               optimizer: str = 'BFGS',
                               fmax: float = 0.05) -> List[Atoms]:
        """
        Optimize individual images before NEB calculation.

        Args:
            images: List of images to optimize
            optimizer: Optimization algorithm
            fmax: Force convergence criterion

        Returns:
            List of optimized images
        """
        from molecule_optimizer import MoleculeOptimizer

        mol_optimizer = MoleculeOptimizer(method='emt')  # Use fast method for pre-optimization
        optimized_images = []

        for i, image in enumerate(images):
            logger.info(f"Pre-optimizing image {i+1}/{len(images)}")
            try:
                opt_image, _ = mol_optimizer.optimize_geometry(
                    image.copy(), optimizer=optimizer, fmax=fmax, steps=50
                )
                optimized_images.append(opt_image)
            except Exception as e:
                logger.warning(f"Failed to optimize image {i+1}: {e}")
                optimized_images.append(image.copy())

        return optimized_images


if __name__ == "__main__":
    # Example usage
    from input_handler import parse_molecule

    try:
        # Test with simple reaction: H2 dissociation
        h2_initial = parse_molecule("H2", "formula")

        # Create final state (dissociated H2)
        h2_final = h2_initial.copy()
        positions = h2_final.get_positions()
        positions[1][0] += 2.0  # Move one H atom away
        h2_final.set_positions(positions)

        # Find pathway
        finder = ReactionPathwayFinder()
        pathway = finder.find_pathway(h2_initial, h2_final, method='neb', n_images=5)

        print(f"Pathway calculation completed:")
        print(f"  Method: {pathway['method']}")
        print(f"  Converged: {pathway['converged']}")
        print(f"  Forward barrier: {pathway['forward_barrier']:.6f} eV")
        print(f"  Reaction energy: {pathway['reaction_energy']:.6f} eV")

        # Analyze pathway
        analysis = finder.analyze_pathway(pathway)
        print(f"  Number of transition states: {analysis['n_transition_states']}")
        print(f"  Number of intermediates: {analysis['n_intermediates']}")

        print("Reaction pathway tests completed successfully!")

    except Exception as e:
        print(f"Test failed: {e}")
