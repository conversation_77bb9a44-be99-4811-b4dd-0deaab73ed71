"""
Thermodynamics and Kinetics Module for ChemLab

This module calculates thermodynamic properties (enthalpy, entropy, Gibbs free energy)
and kinetic parameters (rate constants, Arrhenius parameters) for chemical reactions.

Author: ChemLab Development Team
"""

import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from ase import Atoms
from ase.thermochemistry import IdealGasThermo
from ase.vibrations import Vibrations
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Physical constants
KB = 8.617333e-5  # Boltzmann constant in eV/K
H_PLANCK = 4.135667e-15  # Planck constant in eV·s
R = 8.314  # Gas constant in J/(mol·K)
AVOGADRO = 6.022e23  # Avogadro's number
C_LIGHT = 2.998e8  # Speed of light in m/s


class ThermoKineticsCalculator:
    """
    Calculates thermodynamic and kinetic properties of molecules and reactions.
    """
    
    def __init__(self, temperature=298.15, pressure=101325):
        """
        Initialize thermodynamics calculator.
        
        Args:
            temperature: Temperature in Kelvin (default: 298.15 K)
            pressure: Pressure in Pa (default: 101325 Pa = 1 atm)
        """
        self.temperature = temperature
        self.pressure = pressure
        
    def calculate_thermodynamics(self, atoms: Atoms, 
                               include_vibrations: bool = True) -> Dict[str, Any]:
        """
        Calculate thermodynamic properties of a molecule.
        
        Args:
            atoms: ASE Atoms object
            include_vibrations: Whether to include vibrational contributions
            
        Returns:
            Dictionary with thermodynamic properties
        """
        logger.info(f"Calculating thermodynamics for {atoms.get_chemical_formula()}")
        
        # Get electronic energy
        electronic_energy = atoms.get_potential_energy()
        
        results = {
            'temperature': self.temperature,
            'pressure': self.pressure,
            'electronic_energy': electronic_energy,
            'formula': atoms.get_chemical_formula()
        }
        
        if include_vibrations:
            try:
                # Calculate vibrational frequencies
                vib_results = self._calculate_vibrations(atoms)
                results.update(vib_results)
                
                # Calculate thermodynamic properties using ASE
                thermo_results = self._calculate_ideal_gas_thermo(atoms, vib_results['frequencies'])
                results.update(thermo_results)
                
            except Exception as e:
                logger.warning(f"Vibrational analysis failed: {e}")
                # Fallback to electronic energy only
                results.update(self._estimate_thermodynamics(atoms))
        else:
            results.update(self._estimate_thermodynamics(atoms))
        
        return results
    
    def _calculate_vibrations(self, atoms: Atoms) -> Dict[str, Any]:
        """
        Calculate vibrational frequencies and related properties.
        
        Args:
            atoms: ASE Atoms object
            
        Returns:
            Dictionary with vibrational properties
        """
        logger.info("Calculating vibrational frequencies...")
        
        try:
            # Set up vibrations calculation
            vib = Vibrations(atoms)
            vib.run()
            
            # Get frequencies
            frequencies = vib.get_frequencies()
            
            # Remove imaginary frequencies (convert to positive)
            real_frequencies = []
            imaginary_count = 0
            
            for freq in frequencies:
                if freq.imag != 0 or freq.real < 0:
                    imaginary_count += 1
                    # Convert imaginary frequency to positive real
                    real_frequencies.append(abs(freq.real) if freq.real != 0 else abs(freq.imag))
                else:
                    real_frequencies.append(freq.real)
            
            real_frequencies = np.array(real_frequencies)
            
            # Calculate zero-point energy
            zpe = 0.5 * np.sum(real_frequencies) * H_PLANCK  # in eV
            
            # Calculate vibrational temperature
            vib_temperatures = real_frequencies * H_PLANCK / KB  # in K
            
            results = {
                'frequencies': real_frequencies,
                'imaginary_frequencies': imaginary_count,
                'zero_point_energy': zpe,
                'vibrational_temperatures': vib_temperatures,
                'n_modes': len(real_frequencies)
            }
            
            logger.info(f"Found {len(real_frequencies)} vibrational modes")
            logger.info(f"Zero-point energy: {zpe:.6f} eV")
            if imaginary_count > 0:
                logger.warning(f"Found {imaginary_count} imaginary frequencies")
            
            return results
            
        except Exception as e:
            logger.error(f"Vibrational calculation failed: {e}")
            raise
    
    def _calculate_ideal_gas_thermo(self, atoms: Atoms, frequencies: np.ndarray) -> Dict[str, Any]:
        """
        Calculate thermodynamic properties using ideal gas approximation.
        
        Args:
            atoms: ASE Atoms object
            frequencies: Vibrational frequencies in eV
            
        Returns:
            Dictionary with thermodynamic properties
        """
        try:
            # Convert frequencies from eV to cm^-1 for ASE
            freq_cm = frequencies * 8065.54  # eV to cm^-1 conversion
            
            # Create thermochemistry object
            thermo = IdealGasThermo(
                vib_energies=freq_cm,
                potentialenergy=atoms.get_potential_energy(),
                atoms=atoms,
                geometry='nonlinear' if len(atoms) > 2 else 'linear',
                symmetrynumber=1,  # Simplified - should be calculated properly
                spin=0  # Simplified - should be determined from electronic structure
            )
            
            # Calculate properties at specified temperature
            enthalpy = thermo.get_enthalpy(self.temperature)
            entropy = thermo.get_entropy(self.temperature, self.pressure)
            gibbs = thermo.get_gibbs_energy(self.temperature, self.pressure)
            
            # Heat capacity
            heat_capacity = thermo.get_heat_capacity(self.temperature)
            
            results = {
                'enthalpy': enthalpy,
                'entropy': entropy,
                'gibbs_energy': gibbs,
                'heat_capacity': heat_capacity,
                'method': 'ideal_gas_thermo'
            }
            
            logger.info(f"Thermodynamic properties calculated:")
            logger.info(f"  Enthalpy: {enthalpy:.6f} eV")
            logger.info(f"  Entropy: {entropy:.6f} eV/K")
            logger.info(f"  Gibbs energy: {gibbs:.6f} eV")
            
            return results
            
        except Exception as e:
            logger.warning(f"Ideal gas thermodynamics failed: {e}")
            return self._estimate_thermodynamics(atoms)
    
    def _estimate_thermodynamics(self, atoms: Atoms) -> Dict[str, Any]:
        """
        Estimate thermodynamic properties using simple approximations.
        
        Args:
            atoms: ASE Atoms object
            
        Returns:
            Dictionary with estimated thermodynamic properties
        """
        logger.info("Using estimated thermodynamic properties")
        
        # Simple estimates based on molecular size and temperature
        n_atoms = len(atoms)
        
        # Translational contribution (3/2 RT for each molecule)
        h_trans = 1.5 * KB * self.temperature
        
        # Rotational contribution (RT for linear, 3/2 RT for nonlinear)
        if n_atoms == 1:
            h_rot = 0
        elif n_atoms == 2:
            h_rot = KB * self.temperature
        else:
            h_rot = 1.5 * KB * self.temperature
        
        # Vibrational contribution (rough estimate)
        n_vib_modes = 3 * n_atoms - 6 if n_atoms > 2 else 0
        h_vib = n_vib_modes * KB * self.temperature * 0.5  # Simplified
        
        # Total enthalpy
        enthalpy = atoms.get_potential_energy() + h_trans + h_rot + h_vib
        
        # Entropy estimate (Sackur-Tetrode for translation + rotational + vibrational)
        mass = sum(atoms.get_masses())  # Total molecular mass
        s_trans = 1.5 * KB * np.log(2 * np.pi * mass * KB * self.temperature / H_PLANCK**2) + 2.5 * KB
        s_rot = KB * np.log(self.temperature) if n_atoms > 1 else 0
        s_vib = n_vib_modes * KB * 0.5  # Simplified
        
        entropy = s_trans + s_rot + s_vib
        
        # Gibbs energy
        gibbs = enthalpy - self.temperature * entropy
        
        results = {
            'enthalpy': enthalpy,
            'entropy': entropy,
            'gibbs_energy': gibbs,
            'heat_capacity': 1.5 * KB * n_atoms,  # Rough estimate
            'method': 'estimated'
        }
        
        return results
    
    def calculate_reaction_thermodynamics(self, reactants: List[Atoms], 
                                        products: List[Atoms]) -> Dict[str, Any]:
        """
        Calculate thermodynamic properties of a reaction.
        
        Args:
            reactants: List of reactant molecules
            products: List of product molecules
            
        Returns:
            Dictionary with reaction thermodynamics
        """
        logger.info("Calculating reaction thermodynamics")
        
        # Calculate properties for reactants
        reactant_props = []
        for reactant in reactants:
            props = self.calculate_thermodynamics(reactant, include_vibrations=False)
            reactant_props.append(props)
        
        # Calculate properties for products
        product_props = []
        for product in products:
            props = self.calculate_thermodynamics(product, include_vibrations=False)
            product_props.append(props)
        
        # Sum properties
        def sum_property(prop_list, prop_name):
            return sum(props[prop_name] for props in prop_list)
        
        # Reaction thermodynamics
        delta_h = (sum_property(product_props, 'enthalpy') - 
                  sum_property(reactant_props, 'enthalpy'))
        
        delta_s = (sum_property(product_props, 'entropy') - 
                  sum_property(reactant_props, 'entropy'))
        
        delta_g = (sum_property(product_props, 'gibbs_energy') - 
                  sum_property(reactant_props, 'gibbs_energy'))
        
        # Equilibrium constant
        k_eq = np.exp(-delta_g / (KB * self.temperature))
        
        results = {
            'delta_h': delta_h,
            'delta_s': delta_s,
            'delta_g': delta_g,
            'equilibrium_constant': k_eq,
            'temperature': self.temperature,
            'pressure': self.pressure,
            'reactant_properties': reactant_props,
            'product_properties': product_props
        }
        
        logger.info(f"Reaction thermodynamics:")
        logger.info(f"  ΔH = {delta_h:.6f} eV")
        logger.info(f"  ΔS = {delta_s:.6f} eV/K")
        logger.info(f"  ΔG = {delta_g:.6f} eV")
        logger.info(f"  K_eq = {k_eq:.2e}")
        
        return results
    
    def calculate_rate_constant(self, activation_energy: float,
                              pre_exponential: Optional[float] = None,
                              temperature: Optional[float] = None) -> Dict[str, Any]:
        """
        Calculate reaction rate constant using Arrhenius equation.
        
        Args:
            activation_energy: Activation energy in eV
            pre_exponential: Pre-exponential factor in s^-1 (estimated if None)
            temperature: Temperature in K (uses self.temperature if None)
            
        Returns:
            Dictionary with kinetic parameters
        """
        if temperature is None:
            temperature = self.temperature
        
        if pre_exponential is None:
            # Estimate pre-exponential factor using transition state theory
            pre_exponential = self._estimate_pre_exponential(temperature)
        
        # Arrhenius equation: k = A * exp(-Ea / (kB * T))
        rate_constant = pre_exponential * np.exp(-activation_energy / (KB * temperature))
        
        # Half-life (assuming first-order kinetics)
        half_life = np.log(2) / rate_constant if rate_constant > 0 else np.inf
        
        results = {
            'rate_constant': rate_constant,
            'activation_energy': activation_energy,
            'pre_exponential': pre_exponential,
            'temperature': temperature,
            'half_life': half_life
        }
        
        logger.info(f"Rate constant: {rate_constant:.2e} s^-1")
        logger.info(f"Half-life: {half_life:.2e} s")
        
        return results
    
    def _estimate_pre_exponential(self, temperature: float) -> float:
        """
        Estimate pre-exponential factor using transition state theory.
        
        Args:
            temperature: Temperature in K
            
        Returns:
            Pre-exponential factor in s^-1
        """
        # TST: A ≈ (kB * T / h) * transmission_coefficient
        # Typical transmission coefficient ~ 1
        return KB * temperature / H_PLANCK
    
    def arrhenius_analysis(self, temperatures: np.ndarray,
                          activation_energy: float,
                          pre_exponential: float) -> Dict[str, Any]:
        """
        Perform Arrhenius analysis over temperature range.
        
        Args:
            temperatures: Array of temperatures in K
            activation_energy: Activation energy in eV
            pre_exponential: Pre-exponential factor in s^-1
            
        Returns:
            Dictionary with Arrhenius analysis results
        """
        rate_constants = []
        
        for T in temperatures:
            k = pre_exponential * np.exp(-activation_energy / (KB * T))
            rate_constants.append(k)
        
        rate_constants = np.array(rate_constants)
        
        # Linear fit to ln(k) vs 1/T
        x = 1.0 / temperatures
        y = np.log(rate_constants)
        
        # Linear regression
        slope, intercept = np.polyfit(x, y, 1)
        
        # Extract parameters
        fitted_ea = -slope * KB  # Activation energy from slope
        fitted_a = np.exp(intercept)  # Pre-exponential from intercept
        
        results = {
            'temperatures': temperatures,
            'rate_constants': rate_constants,
            'arrhenius_slope': slope,
            'arrhenius_intercept': intercept,
            'fitted_activation_energy': fitted_ea,
            'fitted_pre_exponential': fitted_a,
            'original_activation_energy': activation_energy,
            'original_pre_exponential': pre_exponential
        }
        
        return results


# Convenience functions
def calculate_gibbs_energy(atoms: Atoms, temperature: float = 298.15) -> float:
    """
    Quick function to calculate Gibbs free energy.
    
    Args:
        atoms: ASE Atoms object
        temperature: Temperature in K
        
    Returns:
        Gibbs free energy in eV
    """
    calc = ThermoKineticsCalculator(temperature=temperature)
    result = calc.calculate_thermodynamics(atoms, include_vibrations=False)
    return result['gibbs_energy']


def calculate_reaction_rate(activation_energy: float, temperature: float = 298.15) -> float:
    """
    Quick function to calculate reaction rate constant.
    
    Args:
        activation_energy: Activation energy in eV
        temperature: Temperature in K
        
    Returns:
        Rate constant in s^-1
    """
    calc = ThermoKineticsCalculator(temperature=temperature)
    result = calc.calculate_rate_constant(activation_energy)
    return result['rate_constant']


if __name__ == "__main__":
    # Example usage
    from input_handler import parse_molecule
    
    try:
        # Test thermodynamic calculations
        h2o = parse_molecule("H2O", "formula")
        
        calc = ThermoKineticsCalculator(temperature=298.15)
        thermo_result = calc.calculate_thermodynamics(h2o, include_vibrations=False)
        
        print(f"Thermodynamic properties of H2O:")
        print(f"  Enthalpy: {thermo_result['enthalpy']:.6f} eV")
        print(f"  Entropy: {thermo_result['entropy']:.6f} eV/K")
        print(f"  Gibbs energy: {thermo_result['gibbs_energy']:.6f} eV")
        
        # Test kinetic calculations
        ea = 1.0  # 1 eV activation energy
        kinetic_result = calc.calculate_rate_constant(ea)
        
        print(f"Kinetic properties (Ea = {ea} eV):")
        print(f"  Rate constant: {kinetic_result['rate_constant']:.2e} s^-1")
        print(f"  Half-life: {kinetic_result['half_life']:.2e} s")
        
        print("Thermodynamics and kinetics tests completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")
