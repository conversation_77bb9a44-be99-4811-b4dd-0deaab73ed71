# Core computational chemistry libraries
ase>=3.22.0                    # Atomic Simulation Environment
pyscf>=2.3.0                   # Python-based Simulations of Chemistry Framework
gpaw>=23.9.0                   # Grid-based Projector Augmented Wave (optional)

# Scientific computing
numpy>=1.21.0
scipy>=1.7.0
matplotlib>=3.5.0
pandas>=1.3.0

# Molecular handling and cheminformatics
rdkit>=2023.3.1               # For SMILES parsing and molecular operations
openbabel-wheel>=3.1.1        # Alternative molecular format converter

# Graph and network analysis
networkx>=2.8.0                # For reaction network modeling
igraph>=0.10.0                 # Alternative graph library

# Visualization
plotly>=5.0.0                  # Interactive plots
py3Dmol>=2.0.0                 # 3D molecular visualization
seaborn>=0.11.0                # Statistical plotting

# CLI and utilities
click>=8.0.0                   # Command line interface
tqdm>=4.64.0                   # Progress bars
pyyaml>=6.0                    # Configuration files
tabulate>=0.9.0                # Pretty table printing

# Optional: Machine learning for future enhancements
scikit-learn>=1.1.0
torch>=1.12.0                  # For potential ML models

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0
black>=22.0.0                  # Code formatting
flake8>=5.0.0                  # Linting
