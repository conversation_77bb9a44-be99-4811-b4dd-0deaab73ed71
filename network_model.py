"""
Reaction Network Model Module for ChemLab

This module builds and analyzes reaction networks, representing chemical species
as nodes and reactions as edges in a graph structure.

Author: ChemLab Development Team
"""

import numpy as np
from typing import Dict, Any, List, Tuple, Optional, Set
from ase import Atoms
import networkx as nx
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ReactionNetwork:
    """
    Represents and analyzes chemical reaction networks.
    """
    
    def __init__(self):
        """Initialize empty reaction network."""
        self.graph = nx.DiGraph()  # Directed graph for reactions
        self.species = {}  # Dictionary mapping species names to Atoms objects
        self.reactions = {}  # Dictionary storing reaction information
        self.reaction_counter = 0
        
    def add_species(self, name: str, atoms: Atoms, properties: Optional[Dict] = None) -> None:
        """
        Add a chemical species to the network.
        
        Args:
            name: Species name/identifier
            atoms: ASE Atoms object representing the species
            properties: Optional dictionary of species properties
        """
        self.species[name] = atoms
        
        # Add node to graph with properties
        node_props = {
            'formula': atoms.get_chemical_formula(),
            'n_atoms': len(atoms),
            'type': 'species'
        }
        
        if properties:
            node_props.update(properties)
        
        self.graph.add_node(name, **node_props)
        logger.info(f"Added species: {name} ({atoms.get_chemical_formula()})")
    
    def add_reaction(self, reactants: List[str], products: List[str],
                    activation_energy: Optional[float] = None,
                    rate_constant: Optional[float] = None,
                    reaction_energy: Optional[float] = None,
                    properties: Optional[Dict] = None) -> str:
        """
        Add a reaction to the network.
        
        Args:
            reactants: List of reactant species names
            products: List of product species names
            activation_energy: Activation energy in eV
            rate_constant: Rate constant in s^-1
            reaction_energy: Reaction energy in eV
            properties: Optional dictionary of reaction properties
            
        Returns:
            Reaction identifier
        """
        reaction_id = f"R{self.reaction_counter}"
        self.reaction_counter += 1
        
        # Store reaction information
        reaction_info = {
            'reactants': reactants,
            'products': products,
            'activation_energy': activation_energy,
            'rate_constant': rate_constant,
            'reaction_energy': reaction_energy,
            'id': reaction_id
        }
        
        if properties:
            reaction_info.update(properties)
        
        self.reactions[reaction_id] = reaction_info
        
        # Add reaction node to graph
        reaction_props = {
            'type': 'reaction',
            'activation_energy': activation_energy,
            'rate_constant': rate_constant,
            'reaction_energy': reaction_energy
        }
        
        if properties:
            reaction_props.update(properties)
        
        self.graph.add_node(reaction_id, **reaction_props)
        
        # Add edges: reactants -> reaction -> products
        for reactant in reactants:
            if reactant not in self.species:
                logger.warning(f"Reactant {reactant} not found in species list")
            self.graph.add_edge(reactant, reaction_id, type='consumes')
        
        for product in products:
            if product not in self.species:
                logger.warning(f"Product {product} not found in species list")
            self.graph.add_edge(reaction_id, product, type='produces')
        
        logger.info(f"Added reaction {reaction_id}: {' + '.join(reactants)} -> {' + '.join(products)}")
        
        return reaction_id
    
    def find_pathways(self, start_species: str, end_species: str,
                     max_length: int = 5) -> List[List[str]]:
        """
        Find reaction pathways between two species.
        
        Args:
            start_species: Starting species name
            end_species: Target species name
            max_length: Maximum pathway length
            
        Returns:
            List of pathways (each pathway is a list of reaction IDs)
        """
        logger.info(f"Finding pathways from {start_species} to {end_species}")
        
        pathways = []
        
        try:
            # Find all simple paths between species
            all_paths = nx.all_simple_paths(
                self.graph, start_species, end_species, cutoff=max_length
            )
            
            for path in all_paths:
                # Extract reaction steps from path
                reaction_steps = []
                for i in range(len(path) - 1):
                    node = path[i + 1]
                    if self.graph.nodes[node].get('type') == 'reaction':
                        reaction_steps.append(node)
                
                if reaction_steps:
                    pathways.append(reaction_steps)
            
            logger.info(f"Found {len(pathways)} pathways")
            
        except nx.NetworkXNoPath:
            logger.info("No pathways found")
        
        return pathways
    
    def analyze_network(self) -> Dict[str, Any]:
        """
        Analyze network properties and statistics.
        
        Returns:
            Dictionary with network analysis results
        """
        logger.info("Analyzing reaction network")
        
        # Basic statistics
        n_species = len([n for n in self.graph.nodes() if self.graph.nodes[n].get('type') == 'species'])
        n_reactions = len([n for n in self.graph.nodes() if self.graph.nodes[n].get('type') == 'reaction'])
        
        # Connectivity analysis
        species_nodes = [n for n in self.graph.nodes() if self.graph.nodes[n].get('type') == 'species']
        
        # Find connected components (considering undirected version)
        undirected = self.graph.to_undirected()
        components = list(nx.connected_components(undirected))
        n_components = len(components)
        
        # Find central species (high degree centrality)
        centrality = nx.degree_centrality(undirected)
        species_centrality = {node: cent for node, cent in centrality.items() 
                            if node in species_nodes}
        
        most_central = max(species_centrality.items(), key=lambda x: x[1]) if species_centrality else None
        
        # Find bottleneck reactions (high betweenness centrality)
        betweenness = nx.betweenness_centrality(self.graph)
        reaction_betweenness = {node: bet for node, bet in betweenness.items() 
                              if self.graph.nodes[node].get('type') == 'reaction'}
        
        bottleneck_reaction = max(reaction_betweenness.items(), key=lambda x: x[1]) if reaction_betweenness else None
        
        # Analyze reaction energetics
        reaction_energies = []
        activation_energies = []
        
        for reaction_id, reaction_info in self.reactions.items():
            if reaction_info.get('reaction_energy') is not None:
                reaction_energies.append(reaction_info['reaction_energy'])
            if reaction_info.get('activation_energy') is not None:
                activation_energies.append(reaction_info['activation_energy'])
        
        analysis = {
            'n_species': n_species,
            'n_reactions': n_reactions,
            'n_components': n_components,
            'most_central_species': most_central,
            'bottleneck_reaction': bottleneck_reaction,
            'avg_reaction_energy': np.mean(reaction_energies) if reaction_energies else None,
            'avg_activation_energy': np.mean(activation_energies) if activation_energies else None,
            'min_activation_energy': np.min(activation_energies) if activation_energies else None,
            'max_activation_energy': np.max(activation_energies) if activation_energies else None,
            'species_centrality': species_centrality,
            'reaction_betweenness': reaction_betweenness
        }
        
        logger.info(f"Network analysis completed:")
        logger.info(f"  Species: {n_species}, Reactions: {n_reactions}")
        logger.info(f"  Connected components: {n_components}")
        if most_central:
            logger.info(f"  Most central species: {most_central[0]} (centrality: {most_central[1]:.3f})")
        
        return analysis
    
    def find_reaction_cycles(self) -> List[List[str]]:
        """
        Find reaction cycles in the network.
        
        Returns:
            List of cycles (each cycle is a list of species names)
        """
        logger.info("Finding reaction cycles")
        
        cycles = []
        
        try:
            # Find cycles in the species subgraph
            species_subgraph = self.graph.subgraph([
                n for n in self.graph.nodes() 
                if self.graph.nodes[n].get('type') == 'species'
            ])
            
            # Convert to undirected for cycle finding
            undirected_species = species_subgraph.to_undirected()
            
            # Find all cycles
            cycle_basis = nx.cycle_basis(undirected_species)
            cycles = cycle_basis
            
            logger.info(f"Found {len(cycles)} reaction cycles")
            
        except Exception as e:
            logger.warning(f"Cycle detection failed: {e}")
        
        return cycles
    
    def get_species_neighbors(self, species_name: str) -> Dict[str, List[str]]:
        """
        Get neighboring species (connected by reactions).
        
        Args:
            species_name: Name of the species
            
        Returns:
            Dictionary with 'reactants' and 'products' lists
        """
        neighbors = {'reactants': [], 'products': []}
        
        # Find reactions where this species is involved
        for reaction_id in self.reactions:
            reaction_info = self.reactions[reaction_id]
            
            if species_name in reaction_info['reactants']:
                # This species is a reactant, so products are neighbors
                neighbors['products'].extend(reaction_info['products'])
            
            if species_name in reaction_info['products']:
                # This species is a product, so reactants are neighbors
                neighbors['reactants'].extend(reaction_info['reactants'])
        
        # Remove duplicates
        neighbors['reactants'] = list(set(neighbors['reactants']))
        neighbors['products'] = list(set(neighbors['products']))
        
        return neighbors
    
    def simulate_kinetics(self, initial_concentrations: Dict[str, float],
                         time_points: np.ndarray,
                         temperature: float = 298.15) -> Dict[str, np.ndarray]:
        """
        Simple kinetic simulation (first-order approximation).
        
        Args:
            initial_concentrations: Initial concentrations of species
            time_points: Time points for simulation
            temperature: Temperature in K
            
        Returns:
            Dictionary with concentration profiles
        """
        logger.info("Running kinetic simulation")
        
        # This is a simplified implementation
        # Real kinetic simulation would require solving ODEs
        
        species_names = list(self.species.keys())
        n_species = len(species_names)
        
        # Initialize concentration matrix
        concentrations = np.zeros((len(time_points), n_species))
        
        # Set initial concentrations
        for i, species in enumerate(species_names):
            concentrations[0, i] = initial_concentrations.get(species, 0.0)
        
        # Simple exponential decay/growth model
        # This is a placeholder - real implementation would solve rate equations
        for i in range(1, len(time_points)):
            dt = time_points[i] - time_points[i-1]
            
            for j, species in enumerate(species_names):
                # Simple first-order kinetics approximation
                rate = 0.0
                
                # Find reactions involving this species
                for reaction_id, reaction_info in self.reactions.items():
                    k = reaction_info.get('rate_constant', 1e-6)  # Default slow rate
                    
                    if species in reaction_info['reactants']:
                        # Species is consumed
                        rate -= k * concentrations[i-1, j]
                    
                    if species in reaction_info['products']:
                        # Species is produced
                        for reactant in reaction_info['reactants']:
                            if reactant in species_names:
                                reactant_idx = species_names.index(reactant)
                                rate += k * concentrations[i-1, reactant_idx]
                
                # Update concentration
                concentrations[i, j] = max(0, concentrations[i-1, j] + rate * dt)
        
        # Convert to dictionary
        result = {}
        for i, species in enumerate(species_names):
            result[species] = concentrations[:, i]
        
        logger.info("Kinetic simulation completed")
        
        return result
    
    def export_network(self, filename: str, format: str = 'graphml') -> None:
        """
        Export network to file.
        
        Args:
            filename: Output filename
            format: Export format ('graphml', 'gexf', 'json')
        """
        try:
            if format.lower() == 'graphml':
                nx.write_graphml(self.graph, filename)
            elif format.lower() == 'gexf':
                nx.write_gexf(self.graph, filename)
            elif format.lower() == 'json':
                data = nx.node_link_data(self.graph)
                import json
                with open(filename, 'w') as f:
                    json.dump(data, f, indent=2)
            else:
                raise ValueError(f"Unsupported format: {format}")
            
            logger.info(f"Network exported to {filename}")
            
        except Exception as e:
            logger.error(f"Export failed: {e}")
            raise


# Convenience functions
def build_simple_network(reactions: List[Tuple[List[str], List[str]]]) -> ReactionNetwork:
    """
    Build a simple reaction network from reaction list.
    
    Args:
        reactions: List of (reactants, products) tuples
        
    Returns:
        ReactionNetwork object
    """
    from input_handler import parse_molecule
    
    network = ReactionNetwork()
    
    # Collect all unique species
    all_species = set()
    for reactants, products in reactions:
        all_species.update(reactants)
        all_species.update(products)
    
    # Add species to network
    for species in all_species:
        try:
            atoms = parse_molecule(species, 'formula')
            network.add_species(species, atoms)
        except Exception as e:
            logger.warning(f"Could not parse species {species}: {e}")
    
    # Add reactions
    for reactants, products in reactions:
        network.add_reaction(reactants, products)
    
    return network


if __name__ == "__main__":
    # Example usage
    try:
        # Create a simple reaction network
        reactions = [
            (['H2', 'O2'], ['H2O']),
            (['CH4', 'O2'], ['CO2', 'H2O']),
            (['CO2', 'H2'], ['CO', 'H2O'])
        ]
        
        network = build_simple_network(reactions)
        
        # Analyze network
        analysis = network.analyze_network()
        print(f"Network analysis:")
        print(f"  Species: {analysis['n_species']}")
        print(f"  Reactions: {analysis['n_reactions']}")
        print(f"  Components: {analysis['n_components']}")
        
        # Find pathways
        pathways = network.find_pathways('H2', 'CO')
        print(f"Pathways from H2 to CO: {len(pathways)}")
        
        print("Reaction network tests completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")
