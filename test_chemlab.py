#!/usr/bin/env python3
"""
ChemLab Test Suite

Comprehensive testing script for all ChemLab modules.
Tests functionality and validates results against known chemical data.

Author: ChemLab Development Team
"""

import unittest
import numpy as np
import sys
import os
import logging

# Import ChemLab modules
from input_handler import MoleculeInput<PERSON>andler, parse_molecule
from molecule_optimizer import MoleculeOptimizer
from reaction_feasibility import ReactionFeasibilityAnalyzer
from reaction_pathway import ReactionPathwayFinder
from thermo_kinetics import ThermoKineticsCalculator
from network_model import ReactionNetwork
from visualizer import ChemLabVisualizer

# Set up logging for tests
logging.basicConfig(level=logging.WARNING)  # Reduce noise during testing


class TestInputHandler(unittest.TestCase):
    """Test input handling and molecule parsing."""
    
    def setUp(self):
        self.handler = MoleculeInputHandler()
    
    def test_parse_simple_molecules(self):
        """Test parsing of simple molecules."""
        # Test common molecules
        molecules = ['H2', 'O2', 'H2O', 'CH4', 'CO2', 'N2']
        
        for mol_name in molecules:
            with self.subTest(molecule=mol_name):
                atoms = self.handler.parse_molecule(mol_name, 'formula')
                self.assertIsNotNone(atoms)
                self.assertGreater(len(atoms), 0)
                
                # Check chemical formula matches
                formula = atoms.get_chemical_formula()
                self.assertTrue(mol_name in formula or formula in mol_name)
    
    def test_auto_detection(self):
        """Test automatic input type detection."""
        # Test formula detection
        h2o = self.handler.parse_molecule("H2O", "auto")
        self.assertEqual(h2o.get_chemical_formula(), "H2O")
        
        # Test multiple molecules
        molecules = self.handler.parse_multiple_molecules(["H2", "O2", "CO2"])
        self.assertEqual(len(molecules), 3)
    
    def test_invalid_input(self):
        """Test handling of invalid inputs."""
        with self.assertRaises(ValueError):
            self.handler.parse_molecule("InvalidMolecule123", "formula")


class TestMoleculeOptimizer(unittest.TestCase):
    """Test molecular geometry optimization."""
    
    def setUp(self):
        self.optimizer = MoleculeOptimizer(method='emt')  # Use fast EMT for testing
    
    def test_single_point_calculation(self):
        """Test single-point energy calculation."""
        h2o = parse_molecule("H2O", "formula")
        result = self.optimizer.calculate_single_point(h2o)
        
        self.assertIn('energy', result)
        self.assertIn('forces', result)
        self.assertIsInstance(result['energy'], float)
        self.assertEqual(len(result['forces']), len(h2o))
    
    def test_geometry_optimization(self):
        """Test geometry optimization."""
        h2 = parse_molecule("H2", "formula")
        
        # Slightly distort the molecule
        positions = h2.get_positions()
        positions[1][0] += 0.1  # Move one atom slightly
        h2.set_positions(positions)
        
        opt_mol, opt_info = self.optimizer.optimize_geometry(h2, steps=10)
        
        self.assertIn('converged', opt_info)
        self.assertIn('final_energy', opt_info)
        self.assertIn('steps_taken', opt_info)
        self.assertLessEqual(opt_info['steps_taken'], 10)
    
    def test_multiple_molecules(self):
        """Test optimization of multiple molecules."""
        molecules = [parse_molecule(mol, "formula") for mol in ["H2", "O2"]]
        results = self.optimizer.optimize_multiple_molecules(molecules, steps=5)
        
        self.assertEqual(len(results), 2)
        for opt_mol, opt_info in results:
            self.assertIsNotNone(opt_mol)
            self.assertIn('final_energy', opt_info)


class TestReactionFeasibility(unittest.TestCase):
    """Test reaction feasibility analysis."""
    
    def setUp(self):
        self.analyzer = ReactionFeasibilityAnalyzer(temperature=298.15)
    
    def test_h2_o2_reaction(self):
        """Test H2 + O2 -> H2O reaction feasibility."""
        h2 = parse_molecule("H2", "formula")
        o2 = parse_molecule("O2", "formula")
        h2o = parse_molecule("H2O", "formula")
        
        result = self.analyzer.analyze_reaction([h2, o2], [h2o], method='energy_only')
        
        self.assertIn('feasible', result)
        self.assertIn('confidence', result)
        self.assertIn('reaction_energy', result)
        self.assertIsInstance(result['feasible'], bool)
        self.assertIsInstance(result['confidence'], float)
        
        # H2 + O2 -> H2O should be exothermic (feasible)
        self.assertTrue(result['feasible'])
        self.assertLess(result['reaction_energy'], 0)  # Exothermic
    
    def test_batch_analysis(self):
        """Test analysis of multiple reactions."""
        h2 = parse_molecule("H2", "formula")
        o2 = parse_molecule("O2", "formula")
        h2o = parse_molecule("H2O", "formula")
        
        reactions = [
            ([h2, o2], [h2o]),
            ([h2o], [h2, o2])  # Reverse reaction
        ]
        
        results = self.analyzer.batch_analysis(reactions)
        self.assertEqual(len(results), 2)
        
        for result in results:
            self.assertIn('feasible', result)
            self.assertIn('reaction_id', result)


class TestReactionPathway(unittest.TestCase):
    """Test reaction pathway finding."""
    
    def setUp(self):
        self.finder = ReactionPathwayFinder()
    
    def test_h2_dissociation(self):
        """Test H2 dissociation pathway."""
        h2_initial = parse_molecule("H2", "formula")
        
        # Create dissociated state
        h2_final = h2_initial.copy()
        positions = h2_final.get_positions()
        positions[1][0] += 2.0  # Move H atoms apart
        h2_final.set_positions(positions)
        
        pathway = self.finder.find_pathway(
            h2_initial, h2_final, method='neb', n_images=5
        )
        
        self.assertIn('method', pathway)
        self.assertIn('energies', pathway)
        self.assertIn('forward_barrier', pathway)
        self.assertIn('reaction_energy', pathway)
        
        self.assertEqual(len(pathway['energies']), 5)
        self.assertGreater(pathway['forward_barrier'], 0)  # Should have barrier
    
    def test_pathway_analysis(self):
        """Test pathway analysis."""
        # Create mock pathway results
        energies = [0.0, 0.5, 1.0, 0.3, -0.2]
        images = [parse_molecule("H2", "formula") for _ in range(5)]
        
        pathway_results = {
            'energies': energies,
            'images': images
        }
        
        analysis = self.finder.analyze_pathway(pathway_results)
        
        self.assertIn('energy_profile', analysis)
        self.assertIn('local_maxima', analysis)
        self.assertIn('local_minima', analysis)
        self.assertIn('n_transition_states', analysis)


class TestThermoKinetics(unittest.TestCase):
    """Test thermodynamic and kinetic calculations."""
    
    def setUp(self):
        self.calc = ThermoKineticsCalculator(temperature=298.15)
    
    def test_thermodynamic_calculation(self):
        """Test thermodynamic property calculation."""
        h2o = parse_molecule("H2O", "formula")
        result = self.calc.calculate_thermodynamics(h2o, include_vibrations=False)
        
        self.assertIn('enthalpy', result)
        self.assertIn('entropy', result)
        self.assertIn('gibbs_energy', result)
        self.assertIn('electronic_energy', result)
        
        # Basic sanity checks
        self.assertIsInstance(result['enthalpy'], float)
        self.assertIsInstance(result['entropy'], float)
        self.assertIsInstance(result['gibbs_energy'], float)
    
    def test_reaction_thermodynamics(self):
        """Test reaction thermodynamic analysis."""
        h2 = parse_molecule("H2", "formula")
        o2 = parse_molecule("O2", "formula")
        h2o = parse_molecule("H2O", "formula")
        
        result = self.calc.calculate_reaction_thermodynamics([h2, o2], [h2o])
        
        self.assertIn('delta_h', result)
        self.assertIn('delta_s', result)
        self.assertIn('delta_g', result)
        self.assertIn('equilibrium_constant', result)
        
        # H2 + O2 -> H2O should be exothermic
        self.assertLess(result['delta_h'], 0)
    
    def test_rate_constant_calculation(self):
        """Test rate constant calculation."""
        activation_energy = 1.0  # eV
        result = self.calc.calculate_rate_constant(activation_energy)
        
        self.assertIn('rate_constant', result)
        self.assertIn('half_life', result)
        self.assertIn('activation_energy', result)
        
        self.assertGreater(result['rate_constant'], 0)
        self.assertGreater(result['half_life'], 0)
    
    def test_arrhenius_analysis(self):
        """Test Arrhenius analysis."""
        temperatures = np.array([300, 350, 400, 450, 500])
        activation_energy = 1.0
        pre_exponential = 1e13
        
        result = self.calc.arrhenius_analysis(temperatures, activation_energy, pre_exponential)
        
        self.assertIn('temperatures', result)
        self.assertIn('rate_constants', result)
        self.assertIn('fitted_activation_energy', result)
        
        self.assertEqual(len(result['rate_constants']), len(temperatures))
        
        # Rate should increase with temperature
        self.assertLess(result['rate_constants'][0], result['rate_constants'][-1])


class TestReactionNetwork(unittest.TestCase):
    """Test reaction network functionality."""
    
    def setUp(self):
        self.network = ReactionNetwork()
    
    def test_add_species_and_reactions(self):
        """Test adding species and reactions to network."""
        # Add species
        h2 = parse_molecule("H2", "formula")
        o2 = parse_molecule("O2", "formula")
        h2o = parse_molecule("H2O", "formula")
        
        self.network.add_species("H2", h2)
        self.network.add_species("O2", o2)
        self.network.add_species("H2O", h2o)
        
        self.assertEqual(len(self.network.species), 3)
        
        # Add reaction
        reaction_id = self.network.add_reaction(
            ["H2", "O2"], ["H2O"],
            activation_energy=1.0,
            rate_constant=1e-6
        )
        
        self.assertIn(reaction_id, self.network.reactions)
        self.assertEqual(len(self.network.reactions), 1)
    
    def test_network_analysis(self):
        """Test network analysis."""
        # Build simple network
        species = ["H2", "O2", "H2O", "CO2"]
        for spec in species:
            atoms = parse_molecule(spec, "formula")
            self.network.add_species(spec, atoms)
        
        self.network.add_reaction(["H2", "O2"], ["H2O"])
        self.network.add_reaction(["H2O"], ["H2", "O2"])
        
        analysis = self.network.analyze_network()
        
        self.assertIn('n_species', analysis)
        self.assertIn('n_reactions', analysis)
        self.assertIn('n_components', analysis)
        
        self.assertEqual(analysis['n_species'], 4)
        self.assertEqual(analysis['n_reactions'], 2)
    
    def test_pathway_finding(self):
        """Test pathway finding in network."""
        # Build network
        species = ["H2", "O2", "H2O"]
        for spec in species:
            atoms = parse_molecule(spec, "formula")
            self.network.add_species(spec, atoms)
        
        self.network.add_reaction(["H2", "O2"], ["H2O"])
        
        pathways = self.network.find_pathways("H2", "H2O", max_length=3)
        self.assertGreaterEqual(len(pathways), 0)


class TestVisualizer(unittest.TestCase):
    """Test visualization functionality."""
    
    def setUp(self):
        self.viz = ChemLabVisualizer()
    
    def test_energy_profile_plot(self):
        """Test energy profile plotting."""
        energies = [0.0, 0.5, 1.0, 0.3, -0.2]
        
        fig = self.viz.plot_energy_profile(energies, title="Test Profile")
        self.assertIsNotNone(fig)
        
        # Clean up
        import matplotlib.pyplot as plt
        plt.close(fig)
    
    def test_arrhenius_plot(self):
        """Test Arrhenius plotting."""
        temperatures = np.array([300, 350, 400, 450, 500])
        rate_constants = 1e13 * np.exp(-1.0 / (8.617e-5 * temperatures))
        
        fig = self.viz.plot_arrhenius(temperatures, rate_constants, activation_energy=1.0)
        self.assertIsNotNone(fig)
        
        # Clean up
        import matplotlib.pyplot as plt
        plt.close(fig)


def run_tests():
    """Run all tests and return results."""
    print("🧪 Running ChemLab Test Suite")
    print("=" * 50)
    
    # Create test suite
    test_classes = [
        TestInputHandler,
        TestMoleculeOptimizer,
        TestReactionFeasibility,
        TestReactionPathway,
        TestThermoKinetics,
        TestReactionNetwork,
        TestVisualizer
    ]
    
    suite = unittest.TestSuite()
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"  {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\n💥 Errors:")
        for test, traceback in result.errors:
            print(f"  {test}: {traceback.split('Exception:')[-1].strip()}")
    
    if len(result.failures) == 0 and len(result.errors) == 0:
        print(f"\n✅ All tests passed! ChemLab is ready to use.")
    else:
        print(f"\n⚠️  Some tests failed. Check dependencies and module implementations.")
    
    return result


if __name__ == "__main__":
    run_tests()
