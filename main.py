#!/usr/bin/env python3
"""
ChemLab Main CLI Interface

Command-line interface for the ChemLab chemical reaction simulation toolkit.
Provides easy access to all major functionality including reaction analysis,
pathway finding, and network modeling.

Author: ChemLab Development Team
"""

import argparse
import sys
import os
import logging
from typing import List, Dict, Any
import numpy as np

# Import ChemLab modules
from input_handler import MoleculeInputHandler, parse_molecule
from molecule_optimizer import MoleculeOptimizer
from reaction_feasibility import ReactionFeasibilityAnalyzer
from reaction_pathway import ReactionPathwayFinder
from thermo_kinetics import ThermoKineticsCalculator
from network_model import ReactionNetwork, build_simple_network
from visualizer import ChemLabVisualizer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def setup_argument_parser():
    """Set up command-line argument parser."""
    parser = argparse.ArgumentParser(
        description="ChemLab: Advanced Chemical Reaction Simulator",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Analyze single reaction
  python main.py --reactants H2 O2 --products H2O --temperature 298.15
  
  # Find reaction pathway
  python main.py --pathway --reactants CH4 Cl2 --products CH3Cl HCl
  
  # Build reaction network
  python main.py --network --reactions "H2,O2->H2O" "CH4,O2->CO2,H2O"
  
  # Optimize molecule geometry
  python main.py --optimize --molecule H2O --method pyscf
        """
    )
    
    # Main operation modes
    parser.add_argument('--mode', choices=['reaction', 'pathway', 'network', 'optimize'],
                       default='reaction', help='Operation mode')
    
    # Molecule/reaction specification
    parser.add_argument('--reactants', nargs='+', help='Reactant molecules')
    parser.add_argument('--products', nargs='+', help='Product molecules')
    parser.add_argument('--molecule', help='Single molecule for optimization')
    parser.add_argument('--reactions', nargs='+', help='Reactions in format "A,B->C,D"')
    
    # Calculation parameters
    parser.add_argument('--temperature', type=float, default=298.15,
                       help='Temperature in Kelvin (default: 298.15)')
    parser.add_argument('--pressure', type=float, default=101325,
                       help='Pressure in Pa (default: 101325)')
    parser.add_argument('--method', choices=['emt', 'pyscf'], default='emt',
                       help='Calculation method (default: emt)')
    
    # Analysis options
    parser.add_argument('--feasibility', action='store_true',
                       help='Perform feasibility analysis')
    parser.add_argument('--pathway', action='store_true',
                       help='Find reaction pathway')
    parser.add_argument('--network', action='store_true',
                       help='Build reaction network')
    parser.add_argument('--optimize', action='store_true',
                       help='Optimize molecular geometry')
    parser.add_argument('--thermodynamics', action='store_true',
                       help='Calculate thermodynamic properties')
    parser.add_argument('--kinetics', action='store_true',
                       help='Calculate kinetic parameters')
    
    # Pathway-specific options
    parser.add_argument('--n-images', type=int, default=7,
                       help='Number of images for NEB calculation')
    parser.add_argument('--spring-constant', type=float, default=1.0,
                       help='NEB spring constant')
    
    # Output options
    parser.add_argument('--output', default='chemlab_results',
                       help='Output file prefix')
    parser.add_argument('--visualize', action='store_true',
                       help='Generate visualizations')
    parser.add_argument('--save-structures', action='store_true',
                       help='Save molecular structures to files')
    parser.add_argument('--verbose', action='store_true',
                       help='Verbose output')
    
    return parser


def parse_reaction_string(reaction_str: str) -> tuple:
    """
    Parse reaction string in format "A,B->C,D".
    
    Args:
        reaction_str: Reaction string
        
    Returns:
        Tuple of (reactants, products)
    """
    try:
        reactant_str, product_str = reaction_str.split('->')
        reactants = [mol.strip() for mol in reactant_str.split(',')]
        products = [mol.strip() for mol in product_str.split(',')]
        return reactants, products
    except ValueError:
        raise ValueError(f"Invalid reaction format: {reaction_str}")


def analyze_single_reaction(args):
    """Analyze a single chemical reaction."""
    logger.info("Analyzing single reaction")
    
    if not args.reactants or not args.products:
        logger.error("Both --reactants and --products must be specified")
        return
    
    # Parse molecules
    handler = MoleculeInputHandler()
    
    try:
        reactant_molecules = handler.parse_multiple_molecules(args.reactants)
        product_molecules = handler.parse_multiple_molecules(args.products)
    except Exception as e:
        logger.error(f"Failed to parse molecules: {e}")
        return
    
    logger.info(f"Reactants: {[mol.get_chemical_formula() for mol in reactant_molecules]}")
    logger.info(f"Products: {[mol.get_chemical_formula() for mol in product_molecules]}")
    
    results = {}
    
    # Feasibility analysis
    if args.feasibility or args.mode == 'reaction':
        analyzer = ReactionFeasibilityAnalyzer(temperature=args.temperature, pressure=args.pressure)
        feasibility = analyzer.analyze_reaction(reactant_molecules, product_molecules, method='energy_only')
        results['feasibility'] = feasibility
        
        print(f"\nFeasibility Analysis:")
        print(f"  Feasible: {feasibility['feasible']}")
        print(f"  Confidence: {feasibility['confidence']:.2f}")
        print(f"  Reason: {feasibility['reason']}")
        print(f"  Reaction Energy: {feasibility['reaction_energy']:.6f} eV")
    
    # Thermodynamic analysis
    if args.thermodynamics:
        thermo_calc = ThermoKineticsCalculator(temperature=args.temperature, pressure=args.pressure)
        thermo_results = thermo_calc.calculate_reaction_thermodynamics(reactant_molecules, product_molecules)
        results['thermodynamics'] = thermo_results
        
        print(f"\nThermodynamic Analysis:")
        print(f"  ΔH: {thermo_results['delta_h']:.6f} eV")
        print(f"  ΔS: {thermo_results['delta_s']:.6f} eV/K")
        print(f"  ΔG: {thermo_results['delta_g']:.6f} eV")
        print(f"  K_eq: {thermo_results['equilibrium_constant']:.2e}")
    
    # Pathway analysis
    if args.pathway and len(reactant_molecules) == 1 and len(product_molecules) == 1:
        pathway_finder = ReactionPathwayFinder()
        pathway_results = pathway_finder.find_pathway(
            reactant_molecules[0], product_molecules[0],
            method='neb', n_images=args.n_images, spring_constant=args.spring_constant
        )
        results['pathway'] = pathway_results
        
        print(f"\nPathway Analysis:")
        print(f"  Method: {pathway_results['method']}")
        print(f"  Converged: {pathway_results['converged']}")
        print(f"  Forward Barrier: {pathway_results['forward_barrier']:.6f} eV")
        print(f"  Reverse Barrier: {pathway_results['reverse_barrier']:.6f} eV")
        print(f"  Reaction Energy: {pathway_results['reaction_energy']:.6f} eV")
        
        # Kinetic analysis
        if args.kinetics:
            thermo_calc = ThermoKineticsCalculator(temperature=args.temperature)
            kinetic_results = thermo_calc.calculate_rate_constant(pathway_results['forward_barrier'])
            results['kinetics'] = kinetic_results
            
            print(f"\nKinetic Analysis:")
            print(f"  Rate Constant: {kinetic_results['rate_constant']:.2e} s⁻¹")
            print(f"  Half-life: {kinetic_results['half_life']:.2e} s")
    
    # Visualization
    if args.visualize:
        visualizer = ChemLabVisualizer()
        
        # Plot molecules
        for i, mol in enumerate(reactant_molecules):
            mol_plot = visualizer.plot_molecule_3d(mol, f"Reactant {i+1}")
        
        for i, mol in enumerate(product_molecules):
            mol_plot = visualizer.plot_molecule_3d(mol, f"Product {i+1}")
        
        # Plot energy profile if pathway was calculated
        if 'pathway' in results:
            energy_plot = visualizer.plot_energy_profile(
                results['pathway']['energies'],
                title="Reaction Energy Profile"
            )
            energy_plot.savefig(f"{args.output}_energy_profile.png", dpi=300, bbox_inches='tight')
    
    # Save structures
    if args.save_structures:
        from ase.io import write
        
        for i, mol in enumerate(reactant_molecules):
            write(f"{args.output}_reactant_{i+1}.xyz", mol)
        
        for i, mol in enumerate(product_molecules):
            write(f"{args.output}_product_{i+1}.xyz", mol)
    
    return results


def optimize_molecule(args):
    """Optimize molecular geometry."""
    logger.info("Optimizing molecular geometry")
    
    if not args.molecule:
        logger.error("--molecule must be specified for optimization")
        return
    
    # Parse molecule
    try:
        molecule = parse_molecule(args.molecule)
    except Exception as e:
        logger.error(f"Failed to parse molecule: {e}")
        return
    
    logger.info(f"Optimizing: {molecule.get_chemical_formula()}")
    
    # Optimize
    optimizer = MoleculeOptimizer(method=args.method)
    optimized_mol, opt_info = optimizer.optimize_geometry(molecule)
    
    print(f"\nOptimization Results:")
    print(f"  Method: {opt_info['method']}")
    print(f"  Converged: {opt_info['converged']}")
    print(f"  Final Energy: {opt_info['final_energy']:.6f} eV")
    print(f"  Max Force: {opt_info['max_force']:.6f} eV/Å")
    print(f"  Steps: {opt_info['steps_taken']}")
    
    # Save optimized structure
    if args.save_structures:
        from ase.io import write
        write(f"{args.output}_optimized.xyz", optimized_mol)
    
    # Visualization
    if args.visualize:
        visualizer = ChemLabVisualizer()
        original_plot = visualizer.plot_molecule_3d(molecule, "Original Structure")
        optimized_plot = visualizer.plot_molecule_3d(optimized_mol, "Optimized Structure")
    
    return optimized_mol, opt_info


def build_reaction_network(args):
    """Build and analyze reaction network."""
    logger.info("Building reaction network")
    
    if not args.reactions:
        logger.error("--reactions must be specified for network analysis")
        return
    
    # Parse reactions
    reactions = []
    for reaction_str in args.reactions:
        try:
            reactants, products = parse_reaction_string(reaction_str)
            reactions.append((reactants, products))
        except ValueError as e:
            logger.error(f"Failed to parse reaction: {e}")
            continue
    
    if not reactions:
        logger.error("No valid reactions found")
        return
    
    # Build network
    network = build_simple_network(reactions)
    
    # Analyze network
    analysis = network.analyze_network()
    
    print(f"\nNetwork Analysis:")
    print(f"  Species: {analysis['n_species']}")
    print(f"  Reactions: {analysis['n_reactions']}")
    print(f"  Connected Components: {analysis['n_components']}")
    
    if analysis['most_central_species']:
        print(f"  Most Central Species: {analysis['most_central_species'][0]}")
    
    # Visualization
    if args.visualize:
        visualizer = ChemLabVisualizer()
        network_plot = visualizer.plot_reaction_network(network, title="Reaction Network")
        network_plot.savefig(f"{args.output}_network.png", dpi=300, bbox_inches='tight')
    
    # Export network
    network.export_network(f"{args.output}_network.graphml", format='graphml')
    
    return network, analysis


def main():
    """Main CLI function."""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    print("🧪 ChemLab: Advanced Chemical Reaction Simulator")
    print("=" * 50)
    
    try:
        if args.optimize or args.mode == 'optimize':
            optimize_molecule(args)
        elif args.network or args.mode == 'network':
            build_reaction_network(args)
        elif args.pathway or args.mode == 'pathway':
            args.pathway = True
            analyze_single_reaction(args)
        else:
            # Default: single reaction analysis
            analyze_single_reaction(args)
        
        print("\n✅ Analysis completed successfully!")
        
    except KeyboardInterrupt:
        print("\n❌ Analysis interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
