#!/usr/bin/env python3
"""
Sample Reactions for ChemLab

This module demonstrates the capabilities of ChemLab with several example
chemical reactions including combustion, halogenation, and substitution reactions.

Author: ChemLab Development Team
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
from input_handler import parse_molecule
from molecule_optimizer import MoleculeOptimizer
from reaction_feasibility import ReactionFeasibilityAnalyzer
from reaction_pathway import ReactionPathwayFinder
from thermo_kinetics import ThermoKineticsCalculator
from network_model import ReactionNetwork
from visualizer import ChemLabVisualizer
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_h2_o2_combustion():
    """
    Example: H2 + 1/2 O2 → H2O
    Classic hydrogen combustion reaction.
    """
    print("\n" + "="*60)
    print("🔥 EXAMPLE 1: Hydrogen Combustion")
    print("   H2 + 1/2 O2 → H2O")
    print("="*60)
    
    try:
        # Parse molecules
        h2 = parse_molecule("H2", "formula")
        o2 = parse_molecule("O2", "formula")
        h2o = parse_molecule("H2O", "formula")
        
        print(f"✓ Parsed molecules:")
        print(f"  H2: {h2.get_chemical_formula()}")
        print(f"  O2: {o2.get_chemical_formula()}")
        print(f"  H2O: {h2o.get_chemical_formula()}")
        
        # Feasibility analysis
        analyzer = ReactionFeasibilityAnalyzer(temperature=298.15)
        feasibility = analyzer.analyze_reaction([h2, o2], [h2o], method='thermodynamic')
        
        print(f"\n📊 Feasibility Analysis:")
        print(f"  Feasible: {feasibility['feasible']}")
        print(f"  Confidence: {feasibility['confidence']:.2f}")
        print(f"  Reason: {feasibility['reason']}")
        print(f"  Reaction Energy: {feasibility['reaction_energy']:.6f} eV")
        
        if 'delta_g' in feasibility:
            print(f"  ΔG: {feasibility['delta_g']:.6f} eV")
            print(f"  K_eq: {feasibility['equilibrium_constant']:.2e}")
        
        # Thermodynamic analysis
        thermo_calc = ThermoKineticsCalculator(temperature=298.15)
        thermo_results = thermo_calc.calculate_reaction_thermodynamics([h2, o2], [h2o])
        
        print(f"\n🌡️  Thermodynamic Properties:")
        print(f"  ΔH: {thermo_results['delta_h']:.6f} eV")
        print(f"  ΔS: {thermo_results['delta_s']:.6f} eV/K")
        print(f"  ΔG: {thermo_results['delta_g']:.6f} eV")
        
        # Simple pathway analysis (H2 dissociation as example)
        print(f"\n🛤️  Pathway Analysis (H2 dissociation):")
        h2_dissociated = h2.copy()
        positions = h2_dissociated.get_positions()
        positions[1][0] += 2.0  # Move H atoms apart
        h2_dissociated.set_positions(positions)
        
        pathway_finder = ReactionPathwayFinder()
        pathway = pathway_finder.find_pathway(h2, h2_dissociated, method='neb', n_images=5)
        
        print(f"  Forward Barrier: {pathway['forward_barrier']:.6f} eV")
        print(f"  Reaction Energy: {pathway['reaction_energy']:.6f} eV")
        print(f"  Converged: {pathway['converged']}")
        
        return {
            'molecules': {'H2': h2, 'O2': o2, 'H2O': h2o},
            'feasibility': feasibility,
            'thermodynamics': thermo_results,
            'pathway': pathway
        }
        
    except Exception as e:
        print(f"❌ Error in H2 + O2 example: {e}")
        return None


def example_methane_chlorination():
    """
    Example: CH4 + Cl2 → CH3Cl + HCl
    Methane chlorination reaction.
    """
    print("\n" + "="*60)
    print("🧪 EXAMPLE 2: Methane Chlorination")
    print("   CH4 + Cl2 → CH3Cl + HCl")
    print("="*60)
    
    try:
        # Parse molecules
        ch4 = parse_molecule("CH4", "formula")
        cl2 = parse_molecule("Cl2", "formula")
        
        # For products, we'll use simple approximations since exact structures are complex
        # In a real application, you'd use proper molecular builders or SMILES
        print(f"✓ Parsed reactants:")
        print(f"  CH4: {ch4.get_chemical_formula()}")
        print(f"  Cl2: {cl2.get_chemical_formula()}")
        
        # Note: For this example, we'll focus on energetics rather than exact product structures
        # In practice, you'd need proper molecular structures for CH3Cl and HCl
        
        # Feasibility analysis (simplified)
        analyzer = ReactionFeasibilityAnalyzer(temperature=500.0)  # Higher temperature
        
        # Estimate reaction energy based on bond energies
        # C-H bond: ~4.3 eV, Cl-Cl bond: ~2.5 eV, C-Cl bond: ~3.5 eV, H-Cl bond: ~4.4 eV
        # Rough estimate: ΔE ≈ (3.5 + 4.4) - (4.3 + 2.5) = 1.1 eV (endothermic)
        estimated_reaction_energy = 1.1
        
        print(f"\n📊 Estimated Analysis:")
        print(f"  Estimated Reaction Energy: {estimated_reaction_energy:.1f} eV (endothermic)")
        print(f"  Requires elevated temperature for feasibility")
        print(f"  Typical industrial conditions: 400-500°C")
        
        # Kinetic analysis
        thermo_calc = ThermoKineticsCalculator(temperature=500.0)
        
        # Estimate activation energy (typically 15-25 kcal/mol = 0.65-1.1 eV for this reaction)
        activation_energy = 0.8  # eV
        kinetic_results = thermo_calc.calculate_rate_constant(activation_energy)
        
        print(f"\n⚡ Kinetic Analysis (T = 500K):")
        print(f"  Estimated Activation Energy: {activation_energy:.1f} eV")
        print(f"  Rate Constant: {kinetic_results['rate_constant']:.2e} s⁻¹")
        print(f"  Half-life: {kinetic_results['half_life']:.2e} s")
        
        # Temperature dependence
        temperatures = np.linspace(400, 600, 5)
        print(f"\n🌡️  Temperature Dependence:")
        for T in temperatures:
            calc_T = ThermoKineticsCalculator(temperature=T)
            k_result = calc_T.calculate_rate_constant(activation_energy)
            print(f"  T = {T:.0f}K: k = {k_result['rate_constant']:.2e} s⁻¹")
        
        return {
            'molecules': {'CH4': ch4, 'Cl2': cl2},
            'estimated_reaction_energy': estimated_reaction_energy,
            'kinetics': kinetic_results,
            'activation_energy': activation_energy
        }
        
    except Exception as e:
        print(f"❌ Error in CH4 + Cl2 example: {e}")
        return None


def example_sn2_reaction():
    """
    Example: SN2 nucleophilic substitution (simplified)
    Demonstrates transition state concepts.
    """
    print("\n" + "="*60)
    print("🎯 EXAMPLE 3: SN2 Nucleophilic Substitution")
    print("   Nu⁻ + R-X → Nu-R + X⁻")
    print("="*60)
    
    try:
        # For this example, we'll use a simplified model
        # Real SN2 would require proper molecular structures
        
        print(f"✓ SN2 Reaction Characteristics:")
        print(f"  Mechanism: Concerted, single-step")
        print(f"  Stereochemistry: Inversion of configuration")
        print(f"  Rate law: Rate = k[Nu⁻][R-X]")
        
        # Typical SN2 activation energies
        activation_energies = {
            'CH3-I': 0.5,   # Primary, good leaving group
            'CH3-Cl': 0.8,  # Primary, moderate leaving group
            'C2H5-I': 0.7,  # Secondary, good leaving group
            'C2H5-Cl': 1.1  # Secondary, moderate leaving group
        }
        
        print(f"\n⚡ Typical Activation Energies:")
        for substrate, ea in activation_energies.items():
            print(f"  {substrate}: {ea:.1f} eV")
        
        # Kinetic analysis for different substrates
        thermo_calc = ThermoKineticsCalculator(temperature=298.15)
        
        print(f"\n📊 Rate Constants at 298K:")
        for substrate, ea in activation_energies.items():
            k_result = thermo_calc.calculate_rate_constant(ea)
            print(f"  {substrate}: k = {k_result['rate_constant']:.2e} s⁻¹")
        
        # Solvent effects (qualitative)
        print(f"\n🧪 Solvent Effects:")
        print(f"  Polar protic solvents: Decrease rate (solvate nucleophile)")
        print(f"  Polar aprotic solvents: Increase rate (don't solvate nucleophile)")
        print(f"  Examples: DMSO > DMF > acetone > methanol > water")
        
        # Temperature dependence for CH3-I
        ea_ch3i = activation_energies['CH3-I']
        temperatures = np.array([273, 298, 323, 348])
        
        print(f"\n🌡️  Temperature Dependence (CH3-I):")
        for T in temperatures:
            calc_T = ThermoKineticsCalculator(temperature=T)
            k_result = calc_T.calculate_rate_constant(ea_ch3i)
            print(f"  T = {T:.0f}K: k = {k_result['rate_constant']:.2e} s⁻¹")
        
        return {
            'activation_energies': activation_energies,
            'mechanism': 'SN2',
            'characteristics': 'concerted, inversion'
        }
        
    except Exception as e:
        print(f"❌ Error in SN2 example: {e}")
        return None


def example_reaction_network():
    """
    Example: Build a simple reaction network.
    """
    print("\n" + "="*60)
    print("🕸️  EXAMPLE 4: Reaction Network")
    print("   Multiple interconnected reactions")
    print("="*60)
    
    try:
        # Create reaction network
        network = ReactionNetwork()
        
        # Add species
        species_list = ['H2', 'O2', 'H2O', 'CH4', 'CO2', 'CO', 'Cl2', 'HCl']
        
        for species in species_list:
            try:
                atoms = parse_molecule(species, 'formula')
                network.add_species(species, atoms)
            except Exception as e:
                print(f"Warning: Could not add species {species}: {e}")
        
        # Add reactions
        reactions = [
            (['H2', 'O2'], ['H2O']),
            (['CH4', 'O2'], ['CO2', 'H2O']),
            (['CO2', 'H2'], ['CO', 'H2O']),
            (['CH4', 'Cl2'], ['HCl']),  # Simplified
            (['CO', 'O2'], ['CO2'])
        ]
        
        for reactants, products in reactions:
            # Add some estimated properties
            activation_energy = np.random.uniform(0.5, 2.0)  # Random for demo
            rate_constant = 1e13 * np.exp(-activation_energy / (8.617e-5 * 298.15))
            
            network.add_reaction(
                reactants, products,
                activation_energy=activation_energy,
                rate_constant=rate_constant
            )
        
        # Analyze network
        analysis = network.analyze_network()
        
        print(f"✓ Network Statistics:")
        print(f"  Species: {analysis['n_species']}")
        print(f"  Reactions: {analysis['n_reactions']}")
        print(f"  Connected Components: {analysis['n_components']}")
        
        if analysis['most_central_species']:
            print(f"  Most Central Species: {analysis['most_central_species'][0]}")
        
        if analysis['avg_activation_energy']:
            print(f"  Average Activation Energy: {analysis['avg_activation_energy']:.3f} eV")
        
        # Find pathways
        print(f"\n🛤️  Pathway Analysis:")
        try:
            pathways = network.find_pathways('H2', 'CO2', max_length=6)
            print(f"  Pathways from H2 to CO2: {len(pathways)}")
            
            for i, pathway in enumerate(pathways[:3]):  # Show first 3
                print(f"    Pathway {i+1}: {' → '.join(pathway)}")
        except Exception as e:
            print(f"  Pathway finding failed: {e}")
        
        # Simple kinetic simulation
        print(f"\n⏱️  Kinetic Simulation:")
        initial_conc = {'H2': 1.0, 'O2': 0.5, 'CH4': 0.8}
        time_points = np.linspace(0, 100, 11)
        
        try:
            conc_profiles = network.simulate_kinetics(initial_conc, time_points)
            
            print(f"  Initial concentrations: {initial_conc}")
            print(f"  Final concentrations:")
            for species, profile in conc_profiles.items():
                if profile[-1] > 0.01:  # Only show significant concentrations
                    print(f"    {species}: {profile[-1]:.3f} M")
        except Exception as e:
            print(f"  Kinetic simulation failed: {e}")
        
        return {
            'network': network,
            'analysis': analysis
        }
        
    except Exception as e:
        print(f"❌ Error in reaction network example: {e}")
        return None


def run_all_examples():
    """Run all example reactions."""
    print("🧪 ChemLab Sample Reactions")
    print("=" * 80)
    print("This script demonstrates ChemLab capabilities with example reactions.")
    print("=" * 80)
    
    results = {}
    
    # Run examples
    results['h2_o2'] = example_h2_o2_combustion()
    results['ch4_cl2'] = example_methane_chlorination()
    results['sn2'] = example_sn2_reaction()
    results['network'] = example_reaction_network()
    
    # Summary
    print("\n" + "="*60)
    print("📋 SUMMARY")
    print("="*60)
    
    successful = sum(1 for result in results.values() if result is not None)
    total = len(results)
    
    print(f"✅ Successfully completed {successful}/{total} examples")
    
    if successful > 0:
        print(f"\n🎉 ChemLab demonstration completed!")
        print(f"   You can now use the main CLI interface:")
        print(f"   python main.py --help")
    else:
        print(f"\n❌ Some examples failed. Check dependencies and try again.")
    
    return results


if __name__ == "__main__":
    run_all_examples()
