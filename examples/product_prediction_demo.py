#!/usr/bin/env python3
"""
Product Prediction Demo for ChemLab

This script demonstrates the AI-powered product prediction capabilities
of ChemLab with various reaction types and validation methods.

Author: ChemLab Development Team
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from product_predictor import ProductPredictor, predict_reaction_products
import json
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def demo_esterification():
    """Demo: Esterification reaction (Ethanol + Acetic Acid → Ethyl Acetate)"""
    print("\n" + "="*70)
    print("🧪 DEMO 1: Esterification Reaction")
    print("   Ethanol + Acetic Acid → ?")
    print("="*70)
    
    reactant_a = "CCO"  # Ethanol
    reactant_b = "CC(=O)O"  # Acetic acid
    
    print(f"Reactant A: {reactant_a} (Ethanol)")
    print(f"Reactant B: {reactant_b} (Acetic Acid)")
    
    # Run prediction with AI
    results = predict_reaction_products(
        reactant_a, reactant_b, 
        temperature=298.15,
        use_ai=True
    )
    
    print(f"\n🤖 AI Prediction Results:")
    print(f"  Method: {results.get('prediction_method', 'Unknown')}")
    
    products = results.get('predicted_products', [])
    if products:
        print(f"  Predicted Products:")
        for i, product in enumerate(products, 1):
            print(f"    {i}. {product}")
        
        # The AI should predict ethyl acetate: CCOC(C)=O
        expected = "CCOC(C)=O"
        if expected in products:
            print(f"  ✅ Correctly predicted ethyl acetate!")
        else:
            print(f"  ⚠️  Expected ethyl acetate ({expected}) not found")
    
    # Validation
    if 'summary' in results:
        summary = results['summary']
        print(f"\n🧪 DFT Validation:")
        print(f"  Thermodynamically Feasible: {summary.get('feasible', 'Unknown')}")
        if summary.get('delta_g') is not None:
            print(f"  ΔG: {summary['delta_g']:.3f} eV")
    
    return results


def demo_sn2_reaction():
    """Demo: SN2 nucleophilic substitution"""
    print("\n" + "="*70)
    print("🎯 DEMO 2: SN2 Nucleophilic Substitution")
    print("   Methyl Bromide + Hydroxide → ?")
    print("="*70)
    
    reactant_a = "CBr"  # Methyl bromide
    reactant_b = "[OH-]"  # Hydroxide ion
    
    print(f"Reactant A: {reactant_a} (Methyl Bromide)")
    print(f"Reactant B: {reactant_b} (Hydroxide Ion)")
    
    try:
        # Run prediction
        results = predict_reaction_products(
            reactant_a, reactant_b,
            temperature=298.15,
            use_ai=True
        )
        
        print(f"\n🤖 AI Prediction Results:")
        products = results.get('predicted_products', [])
        if products:
            print(f"  Predicted Products:")
            for i, product in enumerate(products, 1):
                print(f"    {i}. {product}")
        else:
            print(f"  No products predicted")
        
        return results
        
    except Exception as e:
        print(f"❌ Error in SN2 demo: {e}")
        return None


def demo_diels_alder():
    """Demo: Diels-Alder cycloaddition"""
    print("\n" + "="*70)
    print("🔄 DEMO 3: Diels-Alder Cycloaddition")
    print("   1,3-Butadiene + Ethylene → ?")
    print("="*70)
    
    reactant_a = "C=CC=C"  # 1,3-Butadiene
    reactant_b = "C=C"  # Ethylene
    
    print(f"Reactant A: {reactant_a} (1,3-Butadiene)")
    print(f"Reactant B: {reactant_b} (Ethylene)")
    
    try:
        # Run prediction
        results = predict_reaction_products(
            reactant_a, reactant_b,
            temperature=373.15,  # Higher temperature for Diels-Alder
            use_ai=True
        )
        
        print(f"\n🤖 AI Prediction Results:")
        products = results.get('predicted_products', [])
        if products:
            print(f"  Predicted Products:")
            for i, product in enumerate(products, 1):
                print(f"    {i}. {product}")
            
            # Expected: Cyclohexene (C1=CCCCC1)
            expected = "C1=CCCCC1"
            if any(expected in p for p in products):
                print(f"  ✅ Correctly predicted cyclohexene formation!")
        
        return results
        
    except Exception as e:
        print(f"❌ Error in Diels-Alder demo: {e}")
        return None


def demo_comparison_ai_vs_rules():
    """Demo: Compare AI vs rule-based predictions"""
    print("\n" + "="*70)
    print("⚖️  DEMO 4: AI vs Rule-Based Comparison")
    print("   Benzyl Alcohol + HBr → ?")
    print("="*70)
    
    reactant_a = "c1ccc(CO)cc1"  # Benzyl alcohol
    reactant_b = "Br"  # HBr (simplified)
    
    print(f"Reactant A: {reactant_a} (Benzyl Alcohol)")
    print(f"Reactant B: {reactant_b} (HBr)")
    
    predictor = ProductPredictor()
    
    # AI prediction
    print(f"\n🤖 AI Prediction:")
    try:
        ai_products = predictor.predict_products_ai(reactant_a, reactant_b)
        print(f"  Products: {ai_products}")
    except Exception as e:
        print(f"  Error: {e}")
        ai_products = []
    
    # Rule-based prediction
    print(f"\n📋 Rule-Based Prediction:")
    try:
        rule_products = predictor.predict_products_rules(reactant_a, reactant_b)
        print(f"  Products: {rule_products}")
    except Exception as e:
        print(f"  Error: {e}")
        rule_products = []
    
    # Compare
    print(f"\n📊 Comparison:")
    print(f"  AI found {len(ai_products)} products")
    print(f"  Rules found {len(rule_products)} products")
    
    common = set(ai_products) & set(rule_products)
    if common:
        print(f"  Common predictions: {list(common)}")
    else:
        print(f"  No common predictions")
    
    return {'ai': ai_products, 'rules': rule_products}


def demo_reaction_conditions():
    """Demo: Effect of reaction conditions"""
    print("\n" + "="*70)
    print("🌡️  DEMO 5: Temperature Effects")
    print("   Same reaction at different temperatures")
    print("="*70)
    
    reactant_a = "CCO"  # Ethanol
    reactant_b = "CC(=O)O"  # Acetic acid
    
    temperatures = [298.15, 373.15, 473.15]  # Room temp, 100°C, 200°C
    
    for temp in temperatures:
        print(f"\n🌡️  Temperature: {temp} K ({temp-273.15:.0f}°C)")
        
        try:
            results = predict_reaction_products(
                reactant_a, reactant_b,
                temperature=temp,
                use_ai=False  # Use rules for consistency
            )
            
            if 'summary' in results:
                summary = results['summary']
                print(f"  Feasible: {summary.get('feasible', 'Unknown')}")
                if summary.get('delta_g') is not None:
                    print(f"  ΔG: {summary['delta_g']:.3f} eV")
            
        except Exception as e:
            print(f"  Error: {e}")


def save_demo_results(results_dict: dict):
    """Save all demo results to file"""
    output_file = "product_prediction_demo_results.json"
    
    try:
        with open(output_file, 'w') as f:
            json.dump(results_dict, f, indent=2, default=str)
        print(f"\n💾 Demo results saved to {output_file}")
    except Exception as e:
        print(f"❌ Failed to save results: {e}")


def main():
    """Run all product prediction demos"""
    print("🧪 ChemLab Product Prediction Demos")
    print("=" * 80)
    print("This script demonstrates AI-powered product prediction capabilities.")
    print("=" * 80)
    
    results = {}
    
    # Run demos
    try:
        results['esterification'] = demo_esterification()
        results['sn2'] = demo_sn2_reaction()
        results['diels_alder'] = demo_diels_alder()
        results['ai_vs_rules'] = demo_comparison_ai_vs_rules()
        demo_reaction_conditions()  # This one doesn't return results
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Summary
    print("\n" + "="*70)
    print("📋 DEMO SUMMARY")
    print("="*70)
    
    successful_demos = sum(1 for result in results.values() if result is not None)
    total_demos = len(results)
    
    print(f"✅ Successfully completed {successful_demos}/{total_demos} demos")
    
    if successful_demos > 0:
        print(f"\n🎉 Product prediction demos completed!")
        print(f"   Key capabilities demonstrated:")
        print(f"   • AI-powered product prediction using OpenAI GPT-4")
        print(f"   • Rule-based fallback using RDKit reaction SMARTS")
        print(f"   • DFT validation of predicted products")
        print(f"   • Thermodynamic feasibility analysis")
        print(f"   • Temperature effect studies")
        
        # Save results
        save_demo_results(results)
        
        print(f"\n🚀 Try the CLI interface:")
        print(f"   python main.py --predict --reactant-a 'CCO' --reactant-b 'CC(=O)O'")
    else:
        print(f"\n❌ Some demos failed. Check API keys and dependencies.")
    
    return results


if __name__ == "__main__":
    main()
