"""
Visualizer Module for ChemLab

This module provides visualization tools for molecules, reaction pathways,
potential energy surfaces, and reaction networks.

Author: ChemLab Development Team
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any, List, Tuple, Optional
from ase import Atoms
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    logger.warning("Plotly not available. Some interactive features will be disabled.")
    PLOTLY_AVAILABLE = False

try:
    import py3Dmol
    PY3DMOL_AVAILABLE = True
except ImportError:
    logger.warning("py3Dmol not available. 3D molecular visualization will be limited.")
    PY3DMOL_AVAILABLE = False

try:
    import networkx as nx
    NETWORKX_AVAILABLE = True
except ImportError:
    logger.warning("NetworkX not available. Network visualization will be disabled.")
    NETWORKX_AVAILABLE = False


class ChemLabVisualizer:
    """
    Comprehensive visualization toolkit for chemical simulations.
    """
    
    def __init__(self, style='default'):
        """
        Initialize visualizer.
        
        Args:
            style: Plotting style ('default', 'publication', 'presentation')
        """
        self.style = style
        self._setup_style()
    
    def _setup_style(self):
        """Set up matplotlib style based on preference."""
        if self.style == 'publication':
            plt.style.use('seaborn-v0_8-paper')
            plt.rcParams.update({
                'font.size': 12,
                'axes.linewidth': 1.5,
                'lines.linewidth': 2,
                'figure.dpi': 300
            })
        elif self.style == 'presentation':
            plt.style.use('seaborn-v0_8-talk')
            plt.rcParams.update({
                'font.size': 14,
                'axes.linewidth': 2,
                'lines.linewidth': 3
            })
        else:
            plt.style.use('default')
    
    def plot_molecule_3d(self, atoms: Atoms, title: str = None, 
                        show_bonds: bool = True) -> Any:
        """
        Create 3D visualization of a molecule.
        
        Args:
            atoms: ASE Atoms object
            title: Plot title
            show_bonds: Whether to show chemical bonds
            
        Returns:
            3D plot object
        """
        if not PY3DMOL_AVAILABLE:
            logger.warning("py3Dmol not available. Using matplotlib 3D instead.")
            return self._plot_molecule_3d_matplotlib(atoms, title)
        
        # Create py3Dmol viewer
        viewer = py3Dmol.view(width=600, height=400)
        
        # Add atoms
        positions = atoms.get_positions()
        symbols = atoms.get_chemical_symbols()
        
        for i, (pos, symbol) in enumerate(zip(positions, symbols)):
            viewer.addSphere({
                'center': {'x': float(pos[0]), 'y': float(pos[1]), 'z': float(pos[2])},
                'radius': 0.5,
                'color': self._get_element_color(symbol)
            })
            
            # Add atom labels
            viewer.addLabel(symbol, {
                'position': {'x': float(pos[0]), 'y': float(pos[1]), 'z': float(pos[2])},
                'fontSize': 12,
                'fontColor': 'black'
            })
        
        if show_bonds:
            # Simple bond detection based on distance
            for i in range(len(atoms)):
                for j in range(i + 1, len(atoms)):
                    dist = np.linalg.norm(positions[i] - positions[j])
                    if dist < 2.0:  # Simple bond cutoff
                        viewer.addCylinder({
                            'start': {'x': float(positions[i][0]), 'y': float(positions[i][1]), 'z': float(positions[i][2])},
                            'end': {'x': float(positions[j][0]), 'y': float(positions[j][1]), 'z': float(positions[j][2])},
                            'radius': 0.1,
                            'color': 'gray'
                        })
        
        viewer.setStyle({'sphere': {'scale': 0.3}})
        viewer.zoomTo()
        
        if title:
            logger.info(f"Created 3D visualization: {title}")
        
        return viewer
    
    def _plot_molecule_3d_matplotlib(self, atoms: Atoms, title: str = None):
        """Fallback 3D plotting using matplotlib."""
        fig = plt.figure(figsize=(10, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        positions = atoms.get_positions()
        symbols = atoms.get_chemical_symbols()
        
        # Plot atoms
        for pos, symbol in zip(positions, symbols):
            color = self._get_element_color(symbol)
            ax.scatter(pos[0], pos[1], pos[2], c=color, s=200, alpha=0.8)
            ax.text(pos[0], pos[1], pos[2], symbol, fontsize=12)
        
        ax.set_xlabel('X (Å)')
        ax.set_ylabel('Y (Å)')
        ax.set_zlabel('Z (Å)')
        
        if title:
            ax.set_title(title)
        
        return fig
    
    def plot_energy_profile(self, energies: List[float], 
                           reaction_coordinate: Optional[List[float]] = None,
                           labels: Optional[List[str]] = None,
                           title: str = "Energy Profile") -> plt.Figure:
        """
        Plot reaction energy profile.
        
        Args:
            energies: List of energies in eV
            reaction_coordinate: Optional reaction coordinate values
            labels: Optional labels for points
            title: Plot title
            
        Returns:
            Matplotlib figure
        """
        fig, ax = plt.subplots(figsize=(10, 6))
        
        if reaction_coordinate is None:
            reaction_coordinate = list(range(len(energies)))
        
        # Plot energy profile
        ax.plot(reaction_coordinate, energies, 'o-', linewidth=2, markersize=8)
        
        # Add labels if provided
        if labels:
            for i, (x, y, label) in enumerate(zip(reaction_coordinate, energies, labels)):
                ax.annotate(label, (x, y), xytext=(5, 5), textcoords='offset points')
        
        # Highlight transition states (local maxima)
        for i in range(1, len(energies) - 1):
            if energies[i] > energies[i-1] and energies[i] > energies[i+1]:
                ax.plot(reaction_coordinate[i], energies[i], 'ro', markersize=10, 
                       label='Transition State' if i == 1 else "")
        
        # Highlight intermediates (local minima)
        for i in range(1, len(energies) - 1):
            if energies[i] < energies[i-1] and energies[i] < energies[i+1]:
                ax.plot(reaction_coordinate[i], energies[i], 'go', markersize=10,
                       label='Intermediate' if i == 1 else "")
        
        ax.set_xlabel('Reaction Coordinate')
        ax.set_ylabel('Energy (eV)')
        ax.set_title(title)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # Add energy barriers as annotations
        if len(energies) > 2:
            max_energy = max(energies)
            min_energy = min(energies)
            barrier = max_energy - energies[0]
            
            ax.annotate(f'Barrier: {barrier:.3f} eV', 
                       xy=(0.7, 0.9), xycoords='axes fraction',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
        
        plt.tight_layout()
        return fig
    
    def plot_arrhenius(self, temperatures: np.ndarray, rate_constants: np.ndarray,
                      activation_energy: float = None, title: str = "Arrhenius Plot") -> plt.Figure:
        """
        Create Arrhenius plot.
        
        Args:
            temperatures: Temperature array in K
            rate_constants: Rate constant array in s^-1
            activation_energy: Activation energy for annotation
            title: Plot title
            
        Returns:
            Matplotlib figure
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Regular plot
        ax1.semilogy(temperatures, rate_constants, 'o-', linewidth=2)
        ax1.set_xlabel('Temperature (K)')
        ax1.set_ylabel('Rate Constant (s⁻¹)')
        ax1.set_title('Rate vs Temperature')
        ax1.grid(True, alpha=0.3)
        
        # Arrhenius plot (ln(k) vs 1/T)
        x = 1000 / temperatures  # 1000/T for better scaling
        y = np.log(rate_constants)
        
        ax2.plot(x, y, 'o-', linewidth=2)
        ax2.set_xlabel('1000/T (K⁻¹)')
        ax2.set_ylabel('ln(k)')
        ax2.set_title('Arrhenius Plot')
        ax2.grid(True, alpha=0.3)
        
        # Linear fit
        slope, intercept = np.polyfit(x, y, 1)
        ax2.plot(x, slope * x + intercept, '--', color='red', alpha=0.7, label='Linear fit')
        
        if activation_energy:
            ax2.annotate(f'Ea = {activation_energy:.3f} eV', 
                        xy=(0.7, 0.9), xycoords='axes fraction',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
        
        ax2.legend()
        plt.suptitle(title)
        plt.tight_layout()
        return fig
    
    def plot_reaction_network(self, network, layout: str = 'spring',
                             title: str = "Reaction Network") -> plt.Figure:
        """
        Visualize reaction network.
        
        Args:
            network: ReactionNetwork object
            layout: Network layout ('spring', 'circular', 'kamada_kawai')
            title: Plot title
            
        Returns:
            Matplotlib figure
        """
        if not NETWORKX_AVAILABLE:
            logger.error("NetworkX not available for network visualization")
            return None
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Get layout
        if layout == 'spring':
            pos = nx.spring_layout(network.graph, k=2, iterations=50)
        elif layout == 'circular':
            pos = nx.circular_layout(network.graph)
        elif layout == 'kamada_kawai':
            pos = nx.kamada_kawai_layout(network.graph)
        else:
            pos = nx.spring_layout(network.graph)
        
        # Separate species and reaction nodes
        species_nodes = [n for n in network.graph.nodes() 
                        if network.graph.nodes[n].get('type') == 'species']
        reaction_nodes = [n for n in network.graph.nodes() 
                         if network.graph.nodes[n].get('type') == 'reaction']
        
        # Draw species nodes
        nx.draw_networkx_nodes(network.graph, pos, nodelist=species_nodes,
                              node_color='lightblue', node_size=1000, 
                              alpha=0.8, ax=ax)
        
        # Draw reaction nodes
        nx.draw_networkx_nodes(network.graph, pos, nodelist=reaction_nodes,
                              node_color='lightcoral', node_size=500, 
                              node_shape='s', alpha=0.8, ax=ax)
        
        # Draw edges
        nx.draw_networkx_edges(network.graph, pos, alpha=0.6, ax=ax,
                              arrows=True, arrowsize=20, arrowstyle='->')
        
        # Add labels
        nx.draw_networkx_labels(network.graph, pos, font_size=8, ax=ax)
        
        ax.set_title(title)
        ax.axis('off')
        
        # Add legend
        species_patch = plt.Line2D([0], [0], marker='o', color='w', 
                                  markerfacecolor='lightblue', markersize=10, label='Species')
        reaction_patch = plt.Line2D([0], [0], marker='s', color='w', 
                                   markerfacecolor='lightcoral', markersize=8, label='Reactions')
        ax.legend(handles=[species_patch, reaction_patch])
        
        plt.tight_layout()
        return fig
    
    def plot_concentration_profiles(self, time_points: np.ndarray,
                                   concentrations: Dict[str, np.ndarray],
                                   title: str = "Concentration Profiles") -> plt.Figure:
        """
        Plot concentration vs time profiles.
        
        Args:
            time_points: Time array
            concentrations: Dictionary of species concentrations
            title: Plot title
            
        Returns:
            Matplotlib figure
        """
        fig, ax = plt.subplots(figsize=(10, 6))
        
        for species, conc in concentrations.items():
            ax.plot(time_points, conc, label=species, linewidth=2)
        
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Concentration (M)')
        ax.set_title(title)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
    
    def _get_element_color(self, element: str) -> str:
        """
        Get color for chemical element.
        
        Args:
            element: Element symbol
            
        Returns:
            Color string
        """
        colors = {
            'H': 'white',
            'C': 'black',
            'N': 'blue',
            'O': 'red',
            'F': 'green',
            'Cl': 'green',
            'Br': 'brown',
            'I': 'purple',
            'S': 'yellow',
            'P': 'orange'
        }
        return colors.get(element, 'gray')
    
    def create_interactive_energy_plot(self, energies: List[float],
                                     reaction_coordinate: Optional[List[float]] = None,
                                     title: str = "Interactive Energy Profile"):
        """
        Create interactive energy profile using Plotly.
        
        Args:
            energies: List of energies
            reaction_coordinate: Optional reaction coordinate
            title: Plot title
            
        Returns:
            Plotly figure object
        """
        if not PLOTLY_AVAILABLE:
            logger.warning("Plotly not available. Using matplotlib instead.")
            return self.plot_energy_profile(energies, reaction_coordinate, title=title)
        
        if reaction_coordinate is None:
            reaction_coordinate = list(range(len(energies)))
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=reaction_coordinate,
            y=energies,
            mode='lines+markers',
            name='Energy Profile',
            line=dict(width=3),
            marker=dict(size=8)
        ))
        
        fig.update_layout(
            title=title,
            xaxis_title='Reaction Coordinate',
            yaxis_title='Energy (eV)',
            hovermode='x unified'
        )
        
        return fig
    
    def save_all_plots(self, figures: List[plt.Figure], prefix: str = "chemlab_plot"):
        """
        Save all matplotlib figures to files.
        
        Args:
            figures: List of matplotlib figures
            prefix: Filename prefix
        """
        for i, fig in enumerate(figures):
            filename = f"{prefix}_{i+1:02d}.png"
            fig.savefig(filename, dpi=300, bbox_inches='tight')
            logger.info(f"Saved plot: {filename}")


# Convenience functions
def plot_molecule(atoms: Atoms, title: str = None):
    """Quick function to visualize a molecule."""
    viz = ChemLabVisualizer()
    return viz.plot_molecule_3d(atoms, title)


def plot_pathway(pathway_results: Dict[str, Any], title: str = "Reaction Pathway"):
    """Quick function to plot reaction pathway."""
    viz = ChemLabVisualizer()
    energies = pathway_results['energies']
    return viz.plot_energy_profile(energies, title=title)


if __name__ == "__main__":
    # Example usage
    from input_handler import parse_molecule
    
    try:
        # Test molecule visualization
        h2o = parse_molecule("H2O", "formula")
        
        viz = ChemLabVisualizer()
        
        # Test 3D molecule plot
        mol_plot = viz.plot_molecule_3d(h2o, "Water Molecule")
        print("3D molecule visualization created")
        
        # Test energy profile
        energies = [0.0, 0.5, 1.2, 0.8, -0.3]  # Example energy profile
        energy_plot = viz.plot_energy_profile(energies, title="Example Reaction")
        print("Energy profile created")
        
        # Test Arrhenius plot
        temps = np.linspace(300, 600, 10)
        rates = 1e13 * np.exp(-1.0 / (8.617e-5 * temps))  # Example rates
        arrhenius_plot = viz.plot_arrhenius(temps, rates, activation_energy=1.0)
        print("Arrhenius plot created")
        
        print("Visualizer tests completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")
